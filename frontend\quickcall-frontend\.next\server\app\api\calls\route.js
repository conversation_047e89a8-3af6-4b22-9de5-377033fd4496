(()=>{var e={};e.id=117,e.ids=[117],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},59337:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>w,routeModule:()=>d,serverHooks:()=>g,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{GET:()=>p,POST:()=>l});var n=r(96559),o=r(48088),a=r(37719),i=r(32190),u=r(79646),c=r(28354);async function l(e){try{let{phoneNumber:t}=await e.json();if(!t)return i.NextResponse.json({error:"Phone number is required"},{status:400});try{let e=`lk dispatch create --new-room --agent-name outbound-caller --metadata ${t}`;return console.log(`Simulating execution of: ${e}`),i.NextResponse.json({success:!0,phoneNumber:t,callId:`call-${Date.now()}`,status:"initiated",command:e})}catch(e){return console.error("Error executing LiveKit command:",e),i.NextResponse.json({error:"Failed to initiate call"},{status:500})}}catch(e){return console.error("Error processing request:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function p(){return i.NextResponse.json({calls:[{id:"call-1",phoneNumber:"+33759215427",status:"completed",startTime:new Date(Date.now()-36e5).toISOString(),endTime:new Date(Date.now()-354e4).toISOString(),duration:"1m 20s"},{id:"call-2",phoneNumber:"+14155552671",status:"failed",startTime:new Date(Date.now()-72e5).toISOString(),endTime:new Date(Date.now()-7195e3).toISOString(),duration:"5s"}]})}r.n(c)().promisify(u.exec);let d=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/calls/route",pathname:"/api/calls",filename:"route",bundlePath:"app/api/calls/route"},resolvedPagePath:"E:\\quickcall\\frontend\\quickcall-frontend\\src\\app\\api\\calls\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:m,serverHooks:g}=d;function w(){return(0,a.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:m})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79646:e=>{"use strict";e.exports=require("child_process")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580],()=>r(59337));module.exports=s})();