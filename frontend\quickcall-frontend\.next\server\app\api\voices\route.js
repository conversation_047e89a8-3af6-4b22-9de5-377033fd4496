(()=>{var e={};e.id=367,e.ids=[367],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},45877:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>m,routeModule:()=>l,serverHooks:()=>f,workAsyncStorage:()=>v,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{GET:()=>p,POST:()=>d});var o=t(96559),i=t(48088),a=t(37719),n=t(32190);let u=[{id:"elise",displayName:"Elise",value:"elise",description:"A professional female voice with a warm tone"},{id:"laila",displayName:"Laila",value:"tara",description:"A confident female voice with a natural cadence"}],c="elise";async function p(){try{return n.NextResponse.json({voices:u,selectedVoice:c})}catch(e){return console.error("Error getting voices:",e),n.NextResponse.json({error:"Failed to get voices"},{status:500})}}async function d(e){try{let{voiceId:r}=await e.json();if(!r)return n.NextResponse.json({error:"Voice ID is required"},{status:400});let t=u.find(e=>e.id===r);if(!t)return n.NextResponse.json({error:"Invalid voice ID"},{status:400});return c=r,console.log(`Voice updated to ${r} (value: ${t.value})`),n.NextResponse.json({success:!0,selectedVoice:c,message:`Voice updated to ${t.displayName}`})}catch(e){return console.error("Error updating voice:",e),n.NextResponse.json({error:"Internal server error"},{status:500})}}let l=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/voices/route",pathname:"/api/voices",filename:"route",bundlePath:"app/api/voices/route"},resolvedPagePath:"E:\\quickcall\\frontend\\quickcall-frontend\\src\\app\\api\\voices\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:v,workUnitAsyncStorage:x,serverHooks:f}=l;function m(){return(0,a.patchFetch)({workAsyncStorage:v,workUnitAsyncStorage:x})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,580],()=>t(45877));module.exports=s})();