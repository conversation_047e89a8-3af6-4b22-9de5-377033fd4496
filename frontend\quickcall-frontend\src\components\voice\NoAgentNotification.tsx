"use client";

import { AgentState } from "@livekit/components-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";

interface NoAgentNotificationProps {
  state: AgentState;
}

export const NoAgentNotification: React.FC<NoAgentNotificationProps> = ({ state }) => {
  if (state !== "disconnected") {
    return null;
  }

  return (
    <Alert variant="destructive" className="mt-4">
      <AlertCircle className="h-4 w-4" />
      <AlertDescription>
        No agent is currently connected. Please check your connection and try again.
      </AlertDescription>
    </Alert>
  );
};
