{"version": 3, "file": "index.js", "sources": ["../src/index.ts"], "sourcesContent": ["export class Mutex {\n  private _locking: Promise<void>;\n\n  private _locks: number;\n\n  constructor() {\n    this._locking = Promise.resolve();\n    this._locks = 0;\n  }\n\n  isLocked() {\n    return this._locks > 0;\n  }\n\n  lock() {\n    this._locks += 1;\n\n    let unlockNext: () => void;\n\n    const willLock = new Promise<void>(\n      (resolve) =>\n        (unlockNext = () => {\n          this._locks -= 1;\n          resolve();\n        }),\n    );\n\n    const willUnlock = this._locking.then(() => unlockNext);\n\n    this._locking = this._locking.then(() => willLock);\n\n    return willUnlock;\n  }\n}\n\nexport class MultiMutex {\n  private _queue: (() => void)[];\n  private _limit: number;\n  private _locks: number;\n\n  constructor(limit: number) {\n    this._queue = [];\n    this._limit = limit;\n    this._locks = 0;\n  }\n\n  isLocked() {\n    return this._locks >= this._limit;\n  }\n\n  async lock(): Promise<() => void> {\n    if (!this.isLocked()) {\n      this._locks++;\n      return this._unlock.bind(this);\n    }\n\n    return new Promise((resolve) => {\n      this._queue.push(() => {\n        this._locks++;\n        resolve(this._unlock.bind(this));\n      });\n    });\n  }\n\n  private _unlock() {\n    this._locks--;\n    if (this._queue.length && !this.isLocked()) {\n      const nextUnlock = this._queue.shift();\n      nextUnlock?.();\n    }\n  }\n}\n"], "names": ["Mutex", "__publicField", "unlockNext", "will<PERSON>ock", "resolve", "<PERSON><PERSON><PERSON><PERSON>", "MultiMutex", "limit", "nextUnlock"], "mappings": "oPAAO,MAAMA,CAAM,CAKjB,aAAc,CAJNC,EAAA,iBAEAA,EAAA,eAGD,KAAA,SAAW,QAAQ,UACxB,KAAK,OAAS,CAChB,CAEA,UAAW,CACT,OAAO,KAAK,OAAS,CACvB,CAEA,MAAO,CACL,KAAK,QAAU,EAEX,IAAAC,EAEJ,MAAMC,EAAW,IAAI,QAClBC,GACEF,EAAa,IAAM,CAClB,KAAK,QAAU,EACPE,GACV,CAAA,EAGEC,EAAa,KAAK,SAAS,KAAK,IAAMH,CAAU,EAEtD,YAAK,SAAW,KAAK,SAAS,KAAK,IAAMC,CAAQ,EAE1CE,CACT,CACF,CAEO,MAAMC,CAAW,CAKtB,YAAYC,EAAe,CAJnBN,EAAA,eACAA,EAAA,eACAA,EAAA,eAGN,KAAK,OAAS,GACd,KAAK,OAASM,EACd,KAAK,OAAS,CAChB,CAEA,UAAW,CACF,OAAA,KAAK,QAAU,KAAK,MAC7B,CAEA,MAAM,MAA4B,CAC5B,OAAC,KAAK,WAKH,IAAI,QAASH,GAAY,CACzB,KAAA,OAAO,KAAK,IAAM,CAChB,KAAA,SACLA,EAAQ,KAAK,QAAQ,KAAK,IAAI,CAAC,CAAA,CAChC,CAAA,CACF,GATM,KAAA,SACE,KAAK,QAAQ,KAAK,IAAI,EASjC,CAEQ,SAAU,CAEhB,GADK,KAAA,SACD,KAAK,OAAO,QAAU,CAAC,KAAK,WAAY,CACpC,MAAAI,EAAa,KAAK,OAAO,MAAM,EACxBA,GAAA,MAAAA,GACf,CACF,CACF"}