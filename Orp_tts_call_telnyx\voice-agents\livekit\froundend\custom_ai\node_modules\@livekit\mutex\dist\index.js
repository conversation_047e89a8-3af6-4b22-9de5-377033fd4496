"use strict";var l=Object.defineProperty;var u=(t,s,i)=>s in t?l(t,s,{enumerable:!0,configurable:!0,writable:!0,value:i}):t[s]=i;var o=(t,s,i)=>u(t,typeof s!="symbol"?s+"":s,i);Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});class h{constructor(){o(this,"_locking");o(this,"_locks");this._locking=Promise.resolve(),this._locks=0}isLocked(){return this._locks>0}lock(){this._locks+=1;let s;const i=new Promise(c=>s=()=>{this._locks-=1,c()}),e=this._locking.then(()=>s);return this._locking=this._locking.then(()=>i),e}}class n{constructor(s){o(this,"_queue");o(this,"_limit");o(this,"_locks");this._queue=[],this._limit=s,this._locks=0}isLocked(){return this._locks>=this._limit}async lock(){return this.isLocked()?new Promise(s=>{this._queue.push(()=>{this._locks++,s(this._unlock.bind(this))})}):(this._locks++,this._unlock.bind(this))}_unlock(){if(this._locks--,this._queue.length&&!this.isLocked()){const s=this._queue.shift();s==null||s()}}}exports.MultiMutex=n;exports.Mutex=h;
//# sourceMappingURL=index.js.map
