(()=>{var B;function O(C){return O=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(G){return typeof G}:function(G){return G&&typeof Symbol=="function"&&G.constructor===Symbol&&G!==Symbol.prototype?"symbol":typeof G},O(C)}function x(C,G){var H=Object.keys(C);if(Object.getOwnPropertySymbols){var J=Object.getOwnPropertySymbols(C);G&&(J=J.filter(function(X){return Object.getOwnPropertyDescriptor(C,X).enumerable})),H.push.apply(H,J)}return H}function A(C){for(var G=1;G<arguments.length;G++){var H=arguments[G]!=null?arguments[G]:{};G%2?x(Object(H),!0).forEach(function(J){W(C,J,H[J])}):Object.getOwnPropertyDescriptors?Object.defineProperties(C,Object.getOwnPropertyDescriptors(H)):x(Object(H)).forEach(function(J){Object.defineProperty(C,J,Object.getOwnPropertyDescriptor(H,J))})}return C}function W(C,G,H){if(G=D(G),G in C)Object.defineProperty(C,G,{value:H,enumerable:!0,configurable:!0,writable:!0});else C[G]=H;return C}function D(C){var G=S(C,"string");return O(G)=="symbol"?G:String(G)}function S(C,G){if(O(C)!="object"||!C)return C;var H=C[Symbol.toPrimitive];if(H!==void 0){var J=H.call(C,G||"default");if(O(J)!="object")return J;throw new TypeError("@@toPrimitive must return a primitive value.")}return(G==="string"?String:Number)(C)}var M=Object.defineProperty,YC=function C(G,H){for(var J in H)M(G,J,{get:H[J],enumerable:!0,configurable:!0,set:function X(Y){return H[J]=function(){return Y}}})};function Q(C){return function(G,H){var J=H!==null&&H!==void 0&&H.context?String(H.context):"standalone",X;if(J==="formatting"&&C.formattingValues){var Y=C.defaultFormattingWidth||C.defaultWidth,Z=H!==null&&H!==void 0&&H.width?String(H.width):Y;X=C.formattingValues[Z]||C.formattingValues[Y]}else{var U=C.defaultWidth,$=H!==null&&H!==void 0&&H.width?String(H.width):C.defaultWidth;X=C.values[$]||C.values[U]}var I=C.argumentCallback?C.argumentCallback(G):G;return X[I]}}function N(C){var G=C.toString().replace(/[१२३४५६७८९०]/g,function(H){return E.number[H]});return Number(G)}function z(C){return C.toString().replace(/\d/g,function(G){return E.locale[G]})}var E={locale:{1:"\u0967",2:"\u0968",3:"\u0969",4:"\u096A",5:"\u096B",6:"\u096C",7:"\u096D",8:"\u096E",9:"\u096F",0:"\u0966"},number:{"\u0967":"1","\u0968":"2","\u0969":"3","\u096A":"4","\u096B":"5","\u096C":"6","\u096D":"7","\u096E":"8","\u096F":"9","\u0966":"0"}},R={narrow:["\u0908\u0938\u093E-\u092A\u0942\u0930\u094D\u0935","\u0908\u0938\u094D\u0935\u0940"],abbreviated:["\u0908\u0938\u093E-\u092A\u0942\u0930\u094D\u0935","\u0908\u0938\u094D\u0935\u0940"],wide:["\u0908\u0938\u093E-\u092A\u0942\u0930\u094D\u0935","\u0908\u0938\u0935\u0940 \u0938\u0928"]},L={narrow:["1","2","3","4"],abbreviated:["\u0924\u093F1","\u0924\u093F2","\u0924\u093F3","\u0924\u093F4"],wide:["\u092A\u0939\u0932\u0940 \u0924\u093F\u092E\u093E\u0939\u0940","\u0926\u0942\u0938\u0930\u0940 \u0924\u093F\u092E\u093E\u0939\u0940","\u0924\u0940\u0938\u0930\u0940 \u0924\u093F\u092E\u093E\u0939\u0940","\u091A\u094C\u0925\u0940 \u0924\u093F\u092E\u093E\u0939\u0940"]},V={narrow:["\u091C","\u092B\u093C","\u092E\u093E","\u0905","\u092E\u0908","\u091C\u0942","\u091C\u0941","\u0905\u0917","\u0938\u093F","\u0905\u0915\u094D\u091F\u0942","\u0928","\u0926\u093F"],abbreviated:["\u091C\u0928","\u092B\u093C\u0930","\u092E\u093E\u0930\u094D\u091A","\u0905\u092A\u094D\u0930\u0948\u0932","\u092E\u0908","\u091C\u0942\u0928","\u091C\u0941\u0932","\u0905\u0917","\u0938\u093F\u0924","\u0905\u0915\u094D\u091F\u0942","\u0928\u0935","\u0926\u093F\u0938"],wide:["\u091C\u0928\u0935\u0930\u0940","\u092B\u093C\u0930\u0935\u0930\u0940","\u092E\u093E\u0930\u094D\u091A","\u0905\u092A\u094D\u0930\u0948\u0932","\u092E\u0908","\u091C\u0942\u0928","\u091C\u0941\u0932\u093E\u0908","\u0905\u0917\u0938\u094D\u0924","\u0938\u093F\u0924\u0902\u092C\u0930","\u0905\u0915\u094D\u091F\u0942\u092C\u0930","\u0928\u0935\u0902\u092C\u0930","\u0926\u093F\u0938\u0902\u092C\u0930"]},j={narrow:["\u0930","\u0938\u094B","\u092E\u0902","\u092C\u0941","\u0917\u0941","\u0936\u0941","\u0936"],short:["\u0930","\u0938\u094B","\u092E\u0902","\u092C\u0941","\u0917\u0941","\u0936\u0941","\u0936"],abbreviated:["\u0930\u0935\u093F","\u0938\u094B\u092E","\u092E\u0902\u0917\u0932","\u092C\u0941\u0927","\u0917\u0941\u0930\u0941","\u0936\u0941\u0915\u094D\u0930","\u0936\u0928\u093F"],wide:["\u0930\u0935\u093F\u0935\u093E\u0930","\u0938\u094B\u092E\u0935\u093E\u0930","\u092E\u0902\u0917\u0932\u0935\u093E\u0930","\u092C\u0941\u0927\u0935\u093E\u0930","\u0917\u0941\u0930\u0941\u0935\u093E\u0930","\u0936\u0941\u0915\u094D\u0930\u0935\u093E\u0930","\u0936\u0928\u093F\u0935\u093E\u0930"]},w={narrow:{am:"\u092A\u0942\u0930\u094D\u0935\u093E\u0939\u094D\u0928",pm:"\u0905\u092A\u0930\u093E\u0939\u094D\u0928",midnight:"\u092E\u0927\u094D\u092F\u0930\u093E\u0924\u094D\u0930\u093F",noon:"\u0926\u094B\u092A\u0939\u0930",morning:"\u0938\u0941\u092C\u0939",afternoon:"\u0926\u094B\u092A\u0939\u0930",evening:"\u0936\u093E\u092E",night:"\u0930\u093E\u0924"},abbreviated:{am:"\u092A\u0942\u0930\u094D\u0935\u093E\u0939\u094D\u0928",pm:"\u0905\u092A\u0930\u093E\u0939\u094D\u0928",midnight:"\u092E\u0927\u094D\u092F\u0930\u093E\u0924\u094D\u0930\u093F",noon:"\u0926\u094B\u092A\u0939\u0930",morning:"\u0938\u0941\u092C\u0939",afternoon:"\u0926\u094B\u092A\u0939\u0930",evening:"\u0936\u093E\u092E",night:"\u0930\u093E\u0924"},wide:{am:"\u092A\u0942\u0930\u094D\u0935\u093E\u0939\u094D\u0928",pm:"\u0905\u092A\u0930\u093E\u0939\u094D\u0928",midnight:"\u092E\u0927\u094D\u092F\u0930\u093E\u0924\u094D\u0930\u093F",noon:"\u0926\u094B\u092A\u0939\u0930",morning:"\u0938\u0941\u092C\u0939",afternoon:"\u0926\u094B\u092A\u0939\u0930",evening:"\u0936\u093E\u092E",night:"\u0930\u093E\u0924"}},_={narrow:{am:"\u092A\u0942\u0930\u094D\u0935\u093E\u0939\u094D\u0928",pm:"\u0905\u092A\u0930\u093E\u0939\u094D\u0928",midnight:"\u092E\u0927\u094D\u092F\u0930\u093E\u0924\u094D\u0930\u093F",noon:"\u0926\u094B\u092A\u0939\u0930",morning:"\u0938\u0941\u092C\u0939",afternoon:"\u0926\u094B\u092A\u0939\u0930",evening:"\u0936\u093E\u092E",night:"\u0930\u093E\u0924"},abbreviated:{am:"\u092A\u0942\u0930\u094D\u0935\u093E\u0939\u094D\u0928",pm:"\u0905\u092A\u0930\u093E\u0939\u094D\u0928",midnight:"\u092E\u0927\u094D\u092F\u0930\u093E\u0924\u094D\u0930\u093F",noon:"\u0926\u094B\u092A\u0939\u0930",morning:"\u0938\u0941\u092C\u0939",afternoon:"\u0926\u094B\u092A\u0939\u0930",evening:"\u0936\u093E\u092E",night:"\u0930\u093E\u0924"},wide:{am:"\u092A\u0942\u0930\u094D\u0935\u093E\u0939\u094D\u0928",pm:"\u0905\u092A\u0930\u093E\u0939\u094D\u0928",midnight:"\u092E\u0927\u094D\u092F\u0930\u093E\u0924\u094D\u0930\u093F",noon:"\u0926\u094B\u092A\u0939\u0930",morning:"\u0938\u0941\u092C\u0939",afternoon:"\u0926\u094B\u092A\u0939\u0930",evening:"\u0936\u093E\u092E",night:"\u0930\u093E\u0924"}},f=function C(G,H){var J=Number(G);return z(J)},v={ordinalNumber:f,era:Q({values:R,defaultWidth:"wide"}),quarter:Q({values:L,defaultWidth:"wide",argumentCallback:function C(G){return G-1}}),month:Q({values:V,defaultWidth:"wide"}),day:Q({values:j,defaultWidth:"wide"}),dayPeriod:Q({values:w,defaultWidth:"wide",formattingValues:_,defaultFormattingWidth:"wide"})},F={lessThanXSeconds:{one:"\u0967 \u0938\u0947\u0915\u0902\u0921 \u0938\u0947 \u0915\u092E",other:"{{count}} \u0938\u0947\u0915\u0902\u0921 \u0938\u0947 \u0915\u092E"},xSeconds:{one:"\u0967 \u0938\u0947\u0915\u0902\u0921",other:"{{count}} \u0938\u0947\u0915\u0902\u0921"},halfAMinute:"\u0906\u0927\u093E \u092E\u093F\u0928\u091F",lessThanXMinutes:{one:"\u0967 \u092E\u093F\u0928\u091F \u0938\u0947 \u0915\u092E",other:"{{count}} \u092E\u093F\u0928\u091F \u0938\u0947 \u0915\u092E"},xMinutes:{one:"\u0967 \u092E\u093F\u0928\u091F",other:"{{count}} \u092E\u093F\u0928\u091F"},aboutXHours:{one:"\u0932\u0917\u092D\u0917 \u0967 \u0918\u0902\u091F\u093E",other:"\u0932\u0917\u092D\u0917 {{count}} \u0918\u0902\u091F\u0947"},xHours:{one:"\u0967 \u0918\u0902\u091F\u093E",other:"{{count}} \u0918\u0902\u091F\u0947"},xDays:{one:"\u0967 \u0926\u093F\u0928",other:"{{count}} \u0926\u093F\u0928"},aboutXWeeks:{one:"\u0932\u0917\u092D\u0917 \u0967 \u0938\u092A\u094D\u0924\u093E\u0939",other:"\u0932\u0917\u092D\u0917 {{count}} \u0938\u092A\u094D\u0924\u093E\u0939"},xWeeks:{one:"\u0967 \u0938\u092A\u094D\u0924\u093E\u0939",other:"{{count}} \u0938\u092A\u094D\u0924\u093E\u0939"},aboutXMonths:{one:"\u0932\u0917\u092D\u0917 \u0967 \u092E\u0939\u0940\u0928\u093E",other:"\u0932\u0917\u092D\u0917 {{count}} \u092E\u0939\u0940\u0928\u0947"},xMonths:{one:"\u0967 \u092E\u0939\u0940\u0928\u093E",other:"{{count}} \u092E\u0939\u0940\u0928\u0947"},aboutXYears:{one:"\u0932\u0917\u092D\u0917 \u0967 \u0935\u0930\u094D\u0937",other:"\u0932\u0917\u092D\u0917 {{count}} \u0935\u0930\u094D\u0937"},xYears:{one:"\u0967 \u0935\u0930\u094D\u0937",other:"{{count}} \u0935\u0930\u094D\u0937"},overXYears:{one:"\u0967 \u0935\u0930\u094D\u0937 \u0938\u0947 \u0905\u0927\u093F\u0915",other:"{{count}} \u0935\u0930\u094D\u0937 \u0938\u0947 \u0905\u0927\u093F\u0915"},almostXYears:{one:"\u0932\u0917\u092D\u0917 \u0967 \u0935\u0930\u094D\u0937",other:"\u0932\u0917\u092D\u0917 {{count}} \u0935\u0930\u094D\u0937"}},P=function C(G,H,J){var X,Y=F[G];if(typeof Y==="string")X=Y;else if(H===1)X=Y.one;else X=Y.other.replace("{{count}}",z(H));if(J!==null&&J!==void 0&&J.addSuffix)if(J.comparison&&J.comparison>0)return X+"\u092E\u0947 ";else return X+" \u092A\u0939\u0932\u0947";return X};function K(C){return function(){var G=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},H=G.width?String(G.width):C.defaultWidth,J=C.formats[H]||C.formats[C.defaultWidth];return J}}var k={full:"EEEE, do MMMM, y",long:"do MMMM, y",medium:"d MMM, y",short:"dd/MM/yyyy"},h={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},b={full:"{{date}} '\u0915\u094B' {{time}}",long:"{{date}} '\u0915\u094B' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},y={date:K({formats:k,defaultWidth:"full"}),time:K({formats:h,defaultWidth:"full"}),dateTime:K({formats:b,defaultWidth:"full"})},m={lastWeek:"'\u092A\u093F\u091B\u0932\u0947' eeee p",yesterday:"'\u0915\u0932' p",today:"'\u0906\u091C' p",tomorrow:"'\u0915\u0932' p",nextWeek:"eeee '\u0915\u094B' p",other:"P"},c=function C(G,H,J,X){return m[G]};function q(C){return function(G){var H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},J=H.width,X=J&&C.matchPatterns[J]||C.matchPatterns[C.defaultMatchWidth],Y=G.match(X);if(!Y)return null;var Z=Y[0],U=J&&C.parsePatterns[J]||C.parsePatterns[C.defaultParseWidth],$=Array.isArray(U)?d(U,function(T){return T.test(Z)}):p(U,function(T){return T.test(Z)}),I;I=C.valueCallback?C.valueCallback($):$,I=H.valueCallback?H.valueCallback(I):I;var XC=G.slice(Z.length);return{value:I,rest:XC}}}function p(C,G){for(var H in C)if(Object.prototype.hasOwnProperty.call(C,H)&&G(C[H]))return H;return}function d(C,G){for(var H=0;H<C.length;H++)if(G(C[H]))return H;return}function g(C){return function(G){var H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},J=G.match(C.matchPattern);if(!J)return null;var X=J[0],Y=G.match(C.parsePattern);if(!Y)return null;var Z=C.valueCallback?C.valueCallback(Y[0]):Y[0];Z=H.valueCallback?H.valueCallback(Z):Z;var U=G.slice(X.length);return{value:Z,rest:U}}}var u=/^[०१२३४५६७८९]+/i,l=/^[०१२३४५६७८९]+/i,i={narrow:/^(ईसा-पूर्व|ईस्वी)/i,abbreviated:/^(ईसा\.?\s?पूर्व\.?|ईसा\.?)/i,wide:/^(ईसा-पूर्व|ईसवी पूर्व|ईसवी सन|ईसवी)/i},n={any:[/^b/i,/^(a|c)/i]},s={narrow:/^[1234]/i,abbreviated:/^ति[1234]/i,wide:/^[1234](पहली|दूसरी|तीसरी|चौथी)? तिमाही/i},o={any:[/1/i,/2/i,/3/i,/4/i]},r={narrow:/^[जफ़माअप्मईजूनजुअगसिअक्तनदि]/i,abbreviated:/^(जन|फ़र|मार्च|अप्|मई|जून|जुल|अग|सित|अक्तू|नव|दिस)/i,wide:/^(जनवरी|फ़रवरी|मार्च|अप्रैल|मई|जून|जुलाई|अगस्त|सितंबर|अक्तूबर|नवंबर|दिसंबर)/i},a={narrow:[/^ज/i,/^फ़/i,/^मा/i,/^अप्/i,/^मई/i,/^जू/i,/^जु/i,/^अग/i,/^सि/i,/^अक्तू/i,/^न/i,/^दि/i],any:[/^जन/i,/^फ़/i,/^मा/i,/^अप्/i,/^मई/i,/^जू/i,/^जु/i,/^अग/i,/^सि/i,/^अक्तू/i,/^नव/i,/^दिस/i]},e={narrow:/^[रविसोममंगलबुधगुरुशुक्रशनि]/i,short:/^(रवि|सोम|मंगल|बुध|गुरु|शुक्र|शनि)/i,abbreviated:/^(रवि|सोम|मंगल|बुध|गुरु|शुक्र|शनि)/i,wide:/^(रविवार|सोमवार|मंगलवार|बुधवार|गुरुवार|शुक्रवार|शनिवार)/i},t={narrow:[/^रवि/i,/^सोम/i,/^मंगल/i,/^बुध/i,/^गुरु/i,/^शुक्र/i,/^शनि/i],any:[/^रवि/i,/^सोम/i,/^मंगल/i,/^बुध/i,/^गुरु/i,/^शुक्र/i,/^शनि/i]},CC={narrow:/^(पू|अ|म|द.\?|सु|दो|शा|रा)/i,any:/^(पूर्वाह्न|अपराह्न|म|द.\?|सु|दो|शा|रा)/i},GC={any:{am:/^पूर्वाह्न/i,pm:/^अपराह्न/i,midnight:/^मध्य/i,noon:/^दो/i,morning:/सु/i,afternoon:/दो/i,evening:/शा/i,night:/रा/i}},HC={ordinalNumber:g({matchPattern:u,parsePattern:l,valueCallback:N}),era:q({matchPatterns:i,defaultMatchWidth:"wide",parsePatterns:n,defaultParseWidth:"any"}),quarter:q({matchPatterns:s,defaultMatchWidth:"wide",parsePatterns:o,defaultParseWidth:"any",valueCallback:function C(G){return G+1}}),month:q({matchPatterns:r,defaultMatchWidth:"wide",parsePatterns:a,defaultParseWidth:"any"}),day:q({matchPatterns:e,defaultMatchWidth:"wide",parsePatterns:t,defaultParseWidth:"any"}),dayPeriod:q({matchPatterns:CC,defaultMatchWidth:"any",parsePatterns:GC,defaultParseWidth:"any"})},JC={code:"hi",formatDistance:P,formatLong:y,formatRelative:c,localize:v,match:HC,options:{weekStartsOn:0,firstWeekContainsDate:4}};window.dateFns=A(A({},window.dateFns),{},{locale:A(A({},(B=window.dateFns)===null||B===void 0?void 0:B.locale),{},{hi:JC})})})();

//# debugId=326DDD2044D3ACFF64756E2164756E21
