(()=>{var e={};e.id=610,e.ids=[610],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27892:(e,r,t)=>{Promise.resolve().then(t.bind(t,35130))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35130:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i});var a=t(60687),s=t(43210),d=t(85814),n=t.n(d);function i(){let[e,r]=(0,s.useState)([{id:"1",phoneNumber:"+33759215427",agent:"Mia",date:"May 29, 2025, 3:45 PM",duration:"2m 34s",status:"completed"},{id:"2",phoneNumber:"+14155552671",agent:"Support Agent",date:"May 28, 2025, 11:23 AM",duration:"5m 12s",status:"completed"},{id:"3",phoneNumber:"+61491570156",agent:"Mia",date:"May 27, 2025, 9:15 AM",duration:"1m 47s",status:"failed"},{id:"4",phoneNumber:"+447911123456",agent:"Support Agent",date:"May 26, 2025, 4:30 PM",duration:"3m 22s",status:"completed"}]),[t,d]=(0,s.useState)(""),[i,o]=(0,s.useState)("all"),l=e.filter(e=>{let r=e.phoneNumber.includes(t)||e.agent.toLowerCase().includes(t.toLowerCase()),a="all"===i||e.status===i;return r&&a}),c=e=>{switch(e){case"completed":return"bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";case"failed":return"bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400";case"in-progress":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400"}};return(0,a.jsxs)("div",{className:"p-6 max-w-7xl mx-auto",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-semibold",children:"Call History"}),(0,a.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"View and manage your past calls"})]}),(0,a.jsx)("div",{children:(0,a.jsxs)(n(),{href:"/dashboard",className:"bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md font-medium transition-colors flex items-center gap-2",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{d:"M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"})}),"New Call"]})})]}),(0,a.jsx)("div",{className:"bg-white dark:bg-[#1A1A1A] rounded-lg border border-gray-200 dark:border-gray-800 p-4 mb-6",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("label",{htmlFor:"search",className:"block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300",children:"Search"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none",children:(0,a.jsx)("svg",{className:"w-4 h-4 text-gray-500 dark:text-gray-400","aria-hidden":"true",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"})})}),(0,a.jsx)("input",{type:"search",id:"search",className:"block w-full p-2 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-white dark:bg-[#1A1A1A] dark:border-gray-700 dark:text-white focus:ring-indigo-500 focus:border-indigo-500",placeholder:"Search by phone number or agent...",value:t,onChange:e=>d(e.target.value)})]})]}),(0,a.jsxs)("div",{className:"w-full md:w-48",children:[(0,a.jsx)("label",{htmlFor:"status",className:"block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300",children:"Status"}),(0,a.jsxs)("select",{id:"status",className:"block w-full p-2 text-sm text-gray-900 border border-gray-300 rounded-lg bg-white dark:bg-[#1A1A1A] dark:border-gray-700 dark:text-white focus:ring-indigo-500 focus:border-indigo-500",value:i,onChange:e=>o(e.target.value),children:[(0,a.jsx)("option",{value:"all",children:"All Statuses"}),(0,a.jsx)("option",{value:"completed",children:"Completed"}),(0,a.jsx)("option",{value:"failed",children:"Failed"}),(0,a.jsx)("option",{value:"in-progress",children:"In Progress"})]})]})]})}),(0,a.jsx)("div",{className:"bg-white dark:bg-[#1A1A1A] rounded-lg border border-gray-200 dark:border-gray-800 overflow-hidden",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-800",children:[(0,a.jsx)("thead",{className:"bg-gray-50 dark:bg-[#222222]",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Phone Number"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Agent"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Date & Time"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Duration"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{scope:"col",className:"relative px-6 py-3",children:(0,a.jsx)("span",{className:"sr-only",children:"Actions"})})]})}),(0,a.jsx)("tbody",{className:"bg-white dark:bg-[#1A1A1A] divide-y divide-gray-200 dark:divide-gray-800",children:l.length>0?l.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50 dark:hover:bg-[#222222]",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:e.phoneNumber}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400",children:e.agent}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400",children:e.date}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400",children:e.duration}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${c(e.status)}`,children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:[(0,a.jsx)("button",{className:"text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300 mr-3",children:"View"}),(0,a.jsx)("button",{className:"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{d:"M6 10a2 2 0 11-4 0 2 2 0 014 0zM12 10a2 2 0 11-4 0 2 2 0 014 0zM16 12a2 2 0 100-4 2 2 0 000 4z"})})})]})]},e.id)):(0,a.jsx)("tr",{children:(0,a.jsx)("td",{colSpan:6,className:"px-6 py-10 text-center text-gray-500 dark:text-gray-400",children:"No calls found matching your criteria"})})})]})})}),(0,a.jsxs)("div",{className:"flex justify-between items-center mt-6",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-700 dark:text-gray-300",children:["Showing ",(0,a.jsx)("span",{className:"font-medium",children:l.length})," of ",(0,a.jsx)("span",{className:"font-medium",children:e.length})," calls"]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("button",{className:"px-3 py-1 bg-white dark:bg-[#222222] border border-gray-300 dark:border-gray-700 rounded-md text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800",children:"Previous"}),(0,a.jsx)("button",{className:"px-3 py-1 bg-indigo-600 text-white rounded-md text-sm hover:bg-indigo-700",children:"1"}),(0,a.jsx)("button",{className:"px-3 py-1 bg-white dark:bg-[#222222] border border-gray-300 dark:border-gray-700 rounded-md text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800",children:"2"}),(0,a.jsx)("button",{className:"px-3 py-1 bg-white dark:bg-[#222222] border border-gray-300 dark:border-gray-700 rounded-md text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800",children:"Next"})]})]})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},90540:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\history\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\quickcall\\frontend\\quickcall-frontend\\src\\app\\dashboard\\history\\page.tsx","default")},91508:(e,r,t)=>{Promise.resolve().then(t.bind(t,90540))},92413:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>p,tree:()=>l});var a=t(65239),s=t(48088),d=t(88170),n=t.n(d),i=t(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);t.d(r,o);let l={children:["",{children:["dashboard",{children:["history",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,90540)),"E:\\quickcall\\frontend\\quickcall-frontend\\src\\app\\dashboard\\history\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,63144)),"E:\\quickcall\\frontend\\quickcall-frontend\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"E:\\quickcall\\frontend\\quickcall-frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["E:\\quickcall\\frontend\\quickcall-frontend\\src\\app\\dashboard\\history\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/dashboard/history/page",pathname:"/dashboard/history",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[447,771,658,418,310],()=>t(92413));module.exports=a})();