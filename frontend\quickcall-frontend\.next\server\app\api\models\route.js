(()=>{var e={};e.id=552,e.ids=[552],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},57794:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>c,serverHooks:()=>g,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{GET:()=>p,POST:()=>u});var o=t(96559),n=t(48088),a=t(37719),i=t(32190);let d=[{id:"llama-3.1-8b",name:"Llama 3.1 8B",description:"Fast, efficient model for general-purpose tasks",contextWindow:8192,speed:"Very Fast"},{id:"llama-3.3-70b-versatile",name:"Llama 3.3 70B Versatile",description:"High-performance model with advanced reasoning capabilities",contextWindow:32768,speed:"Fast"},{id:"mixtral-8x7b",name:"Mixtral 8x7B",description:"Powerful mixture-of-experts model with strong multilingual support",contextWindow:32768,speed:"Medium"},{id:"gemma-7b",name:"Gemma 7B",description:"Lightweight model optimized for efficiency",contextWindow:8192,speed:"Very Fast"}],l="llama-3.3-70b-versatile";async function p(){try{return i.NextResponse.json({models:d,selectedModel:l})}catch(e){return console.error("Error getting models:",e),i.NextResponse.json({error:"Failed to get models"},{status:500})}}async function u(e){try{let{modelId:r}=await e.json();if(!r)return i.NextResponse.json({error:"Model ID is required"},{status:400});let t=d.find(e=>e.id===r);if(!t)return i.NextResponse.json({error:"Invalid model ID"},{status:400});return l=r,console.log(`Model updated to ${r}`),i.NextResponse.json({success:!0,selectedModel:l,message:`Model updated to ${t.name}`})}catch(e){return console.error("Error updating model:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let c=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/models/route",pathname:"/api/models",filename:"route",bundlePath:"app/api/models/route"},resolvedPagePath:"E:\\quickcall\\frontend\\quickcall-frontend\\src\\app\\api\\models\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:x,serverHooks:g}=c;function f(){return(0,a.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:x})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,580],()=>t(57794));module.exports=s})();