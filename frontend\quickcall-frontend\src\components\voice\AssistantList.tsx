"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Assistant, ConnectionDetails } from "@/hooks/useVoiceAssistants";
import { Play, Edit, Trash2, Loader2 } from "lucide-react";

interface AssistantListProps {
  assistants: Assistant[];
  selectedAssistant: Assistant | null;
  connectionDetails: ConnectionDetails | null;
  connectingAssistantId: string | null;
  onConnect: (assistant: Assistant) => void;
  onEdit: (assistant: Assistant) => void;
  onDelete: (id: string) => void;
}

const AssistantList: React.FC<AssistantListProps> = ({
  assistants,
  selectedAssistant,
  connectionDetails,
  connectingAssistantId,
  onConnect,
  onEdit,
  onDelete,
}) => {
  if (assistants.length === 0) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <p className="text-muted-foreground">
            No assistants created yet. Create your first assistant above.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">
        Your Assistants
      </h3>
      <div className="grid gap-4">
        {assistants.map((assistant) => {
          const isConnecting = connectingAssistantId === assistant.id;
          const isConnected = selectedAssistant?.id === assistant.id && connectionDetails;
          
          return (
            <Card
              key={assistant.id}
              className={isConnected ? 'ring-2 ring-primary' : ''}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-base">
                      {assistant.name}
                    </CardTitle>
                    <div className="flex items-center gap-2 mt-1">
                      {isConnected && (
                        <Badge variant="default" className="text-xs">
                          Connected
                        </Badge>
                      )}
                      {isConnecting && (
                        <Badge variant="secondary" className="text-xs">
                          Connecting...
                        </Badge>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onEdit(assistant)}
                      disabled={isConnecting}
                    >
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onDelete(assistant.id)}
                      disabled={isConnecting || isConnected}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <CardDescription className="text-sm mb-3">
                  {assistant.prompt.length > 100
                    ? `${assistant.prompt.substring(0, 100)}...`
                    : assistant.prompt
                  }
                </CardDescription>
                <Button
                  onClick={() => onConnect(assistant)}
                  disabled={isConnecting || isConnected}
                  className="w-full"
                  variant={isConnected ? "secondary" : "default"}
                >
                  {isConnecting ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Connecting...
                    </>
                  ) : isConnected ? (
                    "Connected"
                  ) : (
                    <>
                      <Play className="w-4 h-4 mr-2" />
                      Connect
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
};

export default AssistantList;
