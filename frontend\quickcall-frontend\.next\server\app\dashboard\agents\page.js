(()=>{var e={};e.id=480,e.ids=[480],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},50272:(e,r,t)=>{Promise.resolve().then(t.bind(t,56520))},56520:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\quickcall\\frontend\\quickcall-frontend\\src\\app\\dashboard\\agents\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63296:(e,r,t)=>{Promise.resolve().then(t.bind(t,81738))},78133:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>d.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>x,tree:()=>l});var a=t(65239),s=t(48088),n=t(88170),d=t.n(n),i=t(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);t.d(r,o);let l={children:["",{children:["dashboard",{children:["agents",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,56520)),"E:\\quickcall\\frontend\\quickcall-frontend\\src\\app\\dashboard\\agents\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,63144)),"E:\\quickcall\\frontend\\quickcall-frontend\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"E:\\quickcall\\frontend\\quickcall-frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["E:\\quickcall\\frontend\\quickcall-frontend\\src\\app\\dashboard\\agents\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/dashboard/agents/page",pathname:"/dashboard/agents",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},79551:e=>{"use strict";e.exports=require("url")},81738:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});var a=t(60687),s=t(43210);function n(){let[e,r]=(0,s.useState)([{id:"1",name:"Mia",prompt:"You are Mia, a friendly and professional customer service representative. Your goal is to assist callers with their inquiries, provide information about our products and services, and resolve any issues they may have in a helpful manner.",createdBy:"<EMAIL>",createdAt:"Dec 8, 2024, 10:35 AM"},{id:"2",name:"Support Agent",prompt:"You are a technical support specialist. Your goal is to help customers troubleshoot and resolve technical issues they are experiencing with our products or services. Be patient, clear, and thorough in your explanations.",createdBy:"<EMAIL>",createdAt:"Dec 3, 2024, 5:53 PM"}]),[t,n]=(0,s.useState)(!1),[d,i]=(0,s.useState)(!1),[o,l]=(0,s.useState)(null),[c,p]=(0,s.useState)(""),[x,m]=(0,s.useState)(""),[g,u]=(0,s.useState)(""),[h,y]=(0,s.useState)({type:"",text:""}),[b,v]=(0,s.useState)(""),f=[{id:"customer-service",name:"Customer Service",prompt:"You are a helpful customer service representative for our company. Your goal is to assist customers with their inquiries, provide information about our products and services, and resolve any issues they may have. Be polite, professional, and empathetic in your responses."},{id:"sales",name:"Sales Representative",prompt:"You are a knowledgeable sales representative for our company. Your goal is to understand the customer's needs, present our products/services that best match those needs, address any objections, and guide them toward making a purchase decision. Be persuasive but not pushy, and focus on how our solutions can solve their problems."},{id:"appointment",name:"Appointment Scheduler",prompt:"You are an appointment scheduling assistant. Your goal is to help callers schedule, reschedule, or cancel appointments. Collect necessary information such as name, contact details, preferred date and time, and reason for the appointment. Confirm all details before ending the call."}],k=e.filter(e=>e.name.toLowerCase().includes(g.toLowerCase())),w=e=>{i(!0),l(e),p(e.name),m(e.prompt),v(""),n(!0)},j=()=>{n(!1)},N=e=>{let r=f.find(r=>r.id===e);r&&(m(r.prompt),v(e))},A=t=>{r(e.filter(e=>e.id!==t)),y({type:"success",text:"Agent deleted successfully"}),setTimeout(()=>y({type:"",text:""}),3e3)};return(0,a.jsxs)("div",{className:"p-6 max-w-7xl mx-auto",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-semibold",children:"Agents"}),(0,a.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"Create and manage your AI agents"})]}),(0,a.jsx)("div",{className:"flex gap-4 items-center",children:(0,a.jsxs)("button",{onClick:()=>{i(!1),l(null),p(""),m(""),v(""),n(!0)},className:"bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors flex items-center gap-2",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z",clipRule:"evenodd"})}),"New Agent"]})})]}),h.text&&(0,a.jsx)("div",{className:`p-4 mb-6 rounded-md ${"success"===h.type?"bg-green-50 dark:bg-green-900/20 text-green-800 dark:text-green-200":"error"===h.type?"bg-red-50 dark:bg-red-900/20 text-red-800 dark:text-red-200":""}`,children:h.text}),(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none",children:(0,a.jsx)("svg",{className:"w-4 h-4 text-gray-500 dark:text-gray-400","aria-hidden":"true",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"})})}),(0,a.jsx)("input",{type:"search",className:"block w-full p-2 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-white dark:bg-[#1A1A1A] dark:border-gray-700 dark:text-white focus:ring-indigo-500 focus:border-indigo-500",placeholder:"Search agents...",value:g,onChange:e=>u(e.target.value)})]})}),(0,a.jsx)("div",{className:"bg-white dark:bg-[#1A1A1A] rounded-lg border border-gray-200 dark:border-gray-800 overflow-hidden",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-800",children:[(0,a.jsx)("thead",{className:"bg-gray-50 dark:bg-[#222222]",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Name"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Created by"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Created at"}),(0,a.jsx)("th",{scope:"col",className:"relative px-6 py-3",children:(0,a.jsx)("span",{className:"sr-only",children:"Actions"})})]})}),(0,a.jsx)("tbody",{className:"bg-white dark:bg-[#1A1A1A] divide-y divide-gray-200 dark:divide-gray-800",children:k.length>0?k.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50 dark:hover:bg-[#222222]",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:e.name}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400",children:e.createdBy}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400",children:e.createdAt}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:[(0,a.jsx)("button",{onClick:()=>w(e),className:"text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300 mr-3",children:"Edit"}),(0,a.jsx)("button",{onClick:()=>A(e.id),className:"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300",children:"Delete"})]})]},e.id)):(0,a.jsx)("tr",{children:(0,a.jsx)("td",{colSpan:4,className:"px-6 py-10 text-center text-gray-500 dark:text-gray-400",children:"No agents found matching your search"})})})]})}),t&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 dark:bg-opacity-70 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white dark:bg-[#1A1A1A] rounded-lg max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden flex flex-col",children:[(0,a.jsxs)("div",{className:"border-b border-gray-200 dark:border-gray-800 px-6 py-4 flex justify-between items-center",children:[(0,a.jsx)("h3",{className:"font-medium",children:d?"Edit Agent":"Create New Agent"}),(0,a.jsx)("button",{onClick:j,className:"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})]}),(0,a.jsx)("div",{className:"flex-1 overflow-y-auto",children:(0,a.jsxs)("div",{className:"p-6 grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"agent-name",className:"block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300",children:"Agent Name"}),(0,a.jsx)("input",{type:"text",id:"agent-name",value:c,onChange:e=>p(e.target.value),placeholder:"Enter agent name",className:"block w-full p-2 text-sm text-gray-900 border border-gray-300 rounded-md bg-white dark:bg-[#1A1A1A] dark:border-gray-700 dark:text-white focus:ring-indigo-500 focus:border-indigo-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"agent-prompt",className:"block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300",children:"Agent Prompt"}),(0,a.jsx)("textarea",{id:"agent-prompt",value:x,onChange:e=>m(e.target.value),placeholder:"Enter agent prompt...",rows:15,className:"block w-full p-4 text-sm text-gray-900 border border-gray-300 rounded-md bg-white dark:bg-[#1A1A1A] dark:border-gray-700 dark:text-white focus:ring-indigo-500 focus:border-indigo-500 font-mono"})]})]}),(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{className:"bg-white dark:bg-[#222222] rounded-lg border border-gray-200 dark:border-gray-800 overflow-hidden",children:[(0,a.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-800 px-4 py-3",children:(0,a.jsx)("h2",{className:"font-medium text-sm",children:"Template Library"})}),(0,a.jsxs)("div",{className:"p-4 space-y-3",children:[(0,a.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Choose a template to get started:"}),f.map(e=>(0,a.jsxs)("div",{className:`p-3 rounded-md cursor-pointer border text-sm ${b===e.id?"border-indigo-500 dark:border-indigo-400 bg-indigo-50 dark:bg-indigo-900/20":"border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800"}`,onClick:()=>N(e.id),children:[(0,a.jsx)("h3",{className:"font-medium mb-1",children:e.name}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:[e.prompt.substring(0,80),"..."]})]},e.id))]})]}),(0,a.jsxs)("div",{className:"bg-white dark:bg-[#222222] rounded-lg border border-gray-200 dark:border-gray-800 overflow-hidden",children:[(0,a.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-800 px-4 py-3",children:(0,a.jsx)("h2",{className:"font-medium text-sm",children:"Tips"})}),(0,a.jsx)("div",{className:"p-4",children:(0,a.jsxs)("ul",{className:"space-y-2 text-xs text-gray-600 dark:text-gray-300",children:[(0,a.jsxs)("li",{className:"flex items-start gap-2",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 text-indigo-500 dark:text-indigo-400 flex-shrink-0",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})}),(0,a.jsx)("span",{children:"Be specific about the AI's role and purpose"})]}),(0,a.jsxs)("li",{className:"flex items-start gap-2",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 text-indigo-500 dark:text-indigo-400 flex-shrink-0",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})}),(0,a.jsx)("span",{children:"Include guidelines for handling objections"})]}),(0,a.jsxs)("li",{className:"flex items-start gap-2",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 text-indigo-500 dark:text-indigo-400 flex-shrink-0",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})}),(0,a.jsx)("span",{children:"Define the tone and personality you want"})]})]})})]})]})]})}),(0,a.jsxs)("div",{className:"border-t border-gray-200 dark:border-gray-800 px-6 py-4 flex justify-end gap-3",children:[(0,a.jsx)("button",{onClick:j,className:"px-4 py-2 bg-white dark:bg-[#222222] border border-gray-300 dark:border-gray-700 rounded-md text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800",children:"Cancel"}),(0,a.jsx)("button",{onClick:()=>{if(!c.trim()){y({type:"error",text:"Please enter an agent name"}),setTimeout(()=>y({type:"",text:""}),3e3);return}if(!x.trim()){y({type:"error",text:"Please enter an agent prompt"}),setTimeout(()=>y({type:"",text:""}),3e3);return}if(d&&o)r(e.map(e=>e.id===o.id?{...e,name:c,prompt:x}:e)),y({type:"success",text:"Agent updated successfully"});else{let t={id:(e.length+1).toString(),name:c,prompt:x,createdBy:"<EMAIL>",createdAt:new Date().toLocaleString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"numeric",minute:"numeric",hour12:!0})};r([...e,t]),y({type:"success",text:"Agent created successfully"})}setTimeout(()=>y({type:"",text:""}),3e3),j()},className:"bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors",children:d?"Update Agent":"Create Agent"})]})]})})]})}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[447,771,658,418,310],()=>t(78133));module.exports=a})();