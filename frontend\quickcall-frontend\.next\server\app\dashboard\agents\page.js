/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/agents/page";
exports.ids = ["app/dashboard/agents/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fagents%2Fpage&page=%2Fdashboard%2Fagents%2Fpage&appPaths=%2Fdashboard%2Fagents%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fagents%2Fpage.tsx&appDir=E%3A%5Cquickcall%5Cfrontend%5Cquickcall-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cquickcall%5Cfrontend%5Cquickcall-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fagents%2Fpage&page=%2Fdashboard%2Fagents%2Fpage&appPaths=%2Fdashboard%2Fagents%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fagents%2Fpage.tsx&appDir=E%3A%5Cquickcall%5Cfrontend%5Cquickcall-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cquickcall%5Cfrontend%5Cquickcall-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/layout.tsx */ \"(rsc)/./src/app/dashboard/layout.tsx\"));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/agents/page.tsx */ \"(rsc)/./src/app/dashboard/agents/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: [\n        'agents',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module4, \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/agents/page\",\n        pathname: \"/dashboard/agents\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fagents%2Fpage&page=%2Fdashboard%2Fagents%2Fpage&appPaths=%2Fdashboard%2Fagents%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fagents%2Fpage.tsx&appDir=E%3A%5Cquickcall%5Cfrontend%5Cquickcall-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cquickcall%5Cfrontend%5Cquickcall-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cagents%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cagents%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/agents/page.tsx */ \"(rsc)/./src/app/dashboard/agents/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNxdWlja2NhbGwlNUMlNUNmcm9udGVuZCU1QyU1Q3F1aWNrY2FsbC1mcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2Rhc2hib2FyZCU1QyU1Q2FnZW50cyU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrTEFBdUgiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkU6XFxcXHF1aWNrY2FsbFxcXFxmcm9udGVuZFxcXFxxdWlja2NhbGwtZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxcYWdlbnRzXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cagents%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/layout.tsx */ \"(rsc)/./src/app/dashboard/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNxdWlja2NhbGwlNUMlNUNmcm9udGVuZCU1QyU1Q3F1aWNrY2FsbC1mcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2Rhc2hib2FyZCU1QyU1Q2xheW91dC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdLQUFpSCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxccXVpY2tjYWxsXFxcXGZyb250ZW5kXFxcXHF1aWNrY2FsbC1mcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXGRhc2hib2FyZFxcXFxsYXlvdXQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkU6XFxxdWlja2NhbGxcXGZyb250ZW5kXFxxdWlja2NhbGwtZnJvbnRlbmRcXHNyY1xcYXBwXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboard/agents/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/dashboard/agents/page.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\quickcall\\frontend\\quickcall-frontend\\src\\app\\dashboard\\agents\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/dashboard/layout.tsx":
/*!**************************************!*\
  !*** ./src/app/dashboard/layout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\quickcall\\frontend\\quickcall-frontend\\src\\app\\dashboard\\layout.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"cd2e41c6df15\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJFOlxccXVpY2tjYWxsXFxmcm9udGVuZFxccXVpY2tjYWxsLWZyb250ZW5kXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJjZDJlNDFjNmRmMTVcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"QuickCall - Voice AI Calling Platform\",\n    description: \"Make AI-powered calls with LiveKit and Groq\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().variable)} font-sans antialiased bg-white dark:bg-[#0F0F0F] text-black dark:text-white`,\n            children: children\n        }, void 0, false, {\n            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFJTUE7QUFGaUI7QUFPaEIsTUFBTUMsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUU7QUFFYSxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1I7SUFDQSxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7WUFBS0MsV0FBVyxHQUFHVCxrTEFBYyxDQUFDLDRFQUE0RSxDQUFDO3NCQUM3R0s7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIkU6XFxxdWlja2NhbGxcXGZyb250ZW5kXFxxdWlja2NhbGwtZnJvbnRlbmRcXHNyY1xcYXBwXFxsYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tIFwibmV4dFwiO1xuaW1wb3J0IHsgSW50ZXIgfSBmcm9tIFwibmV4dC9mb250L2dvb2dsZVwiO1xuaW1wb3J0IFwiLi9nbG9iYWxzLmNzc1wiO1xuXG5jb25zdCBpbnRlciA9IEludGVyKHtcbiAgc3Vic2V0czogW1wibGF0aW5cIl0sXG4gIHZhcmlhYmxlOiBcIi0tZm9udC1pbnRlclwiLFxufSk7XG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiBcIlF1aWNrQ2FsbCAtIFZvaWNlIEFJIENhbGxpbmcgUGxhdGZvcm1cIixcbiAgZGVzY3JpcHRpb246IFwiTWFrZSBBSS1wb3dlcmVkIGNhbGxzIHdpdGggTGl2ZUtpdCBhbmQgR3JvcVwiLFxufTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufTogUmVhZG9ubHk8e1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xufT4pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiZW5cIj5cbiAgICAgIDxib2R5IGNsYXNzTmFtZT17YCR7aW50ZXIudmFyaWFibGV9IGZvbnQtc2FucyBhbnRpYWxpYXNlZCBiZy13aGl0ZSBkYXJrOmJnLVsjMEYwRjBGXSB0ZXh0LWJsYWNrIGRhcms6dGV4dC13aGl0ZWB9PlxuICAgICAgICB7Y2hpbGRyZW59XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApO1xufVxuIl0sIm5hbWVzIjpbImludGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5IiwiY2xhc3NOYW1lIiwidmFyaWFibGUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cagents%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cagents%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/agents/page.tsx */ \"(ssr)/./src/app/dashboard/agents/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNxdWlja2NhbGwlNUMlNUNmcm9udGVuZCU1QyU1Q3F1aWNrY2FsbC1mcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2Rhc2hib2FyZCU1QyU1Q2FnZW50cyU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrTEFBdUgiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkU6XFxcXHF1aWNrY2FsbFxcXFxmcm9udGVuZFxcXFxxdWlja2NhbGwtZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxcYWdlbnRzXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cagents%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/layout.tsx */ \"(ssr)/./src/app/dashboard/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNxdWlja2NhbGwlNUMlNUNmcm9udGVuZCU1QyU1Q3F1aWNrY2FsbC1mcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2Rhc2hib2FyZCU1QyU1Q2xheW91dC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdLQUFpSCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxccXVpY2tjYWxsXFxcXGZyb250ZW5kXFxcXHF1aWNrY2FsbC1mcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXGRhc2hib2FyZFxcXFxsYXlvdXQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cquickcall%5C%5Cfrontend%5C%5Cquickcall-frontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/agents/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/dashboard/agents/page.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AgentsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction AgentsPage() {\n    const [agents, setAgents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: '1',\n            name: 'Mia',\n            prompt: 'You are Mia, a friendly and professional customer service representative. Your goal is to assist callers with their inquiries, provide information about our products and services, and resolve any issues they may have in a helpful manner.',\n            createdBy: '<EMAIL>',\n            createdAt: 'Dec 8, 2024, 10:35 AM'\n        },\n        {\n            id: '2',\n            name: 'Support Agent',\n            prompt: 'You are a technical support specialist. Your goal is to help customers troubleshoot and resolve technical issues they are experiencing with our products or services. Be patient, clear, and thorough in your explanations.',\n            createdBy: '<EMAIL>',\n            createdAt: 'Dec 3, 2024, 5:53 PM'\n        }\n    ]);\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isEditMode, setIsEditMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentAgent, setCurrentAgent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [newAgentName, setNewAgentName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newAgentPrompt, setNewAgentPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        type: '',\n        text: ''\n    });\n    const [selectedTemplate, setSelectedTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Sample templates\n    const templates = [\n        {\n            id: 'customer-service',\n            name: 'Customer Service',\n            prompt: 'You are a helpful customer service representative for our company. Your goal is to assist customers with their inquiries, provide information about our products and services, and resolve any issues they may have. Be polite, professional, and empathetic in your responses.'\n        },\n        {\n            id: 'sales',\n            name: 'Sales Representative',\n            prompt: 'You are a knowledgeable sales representative for our company. Your goal is to understand the customer\\'s needs, present our products/services that best match those needs, address any objections, and guide them toward making a purchase decision. Be persuasive but not pushy, and focus on how our solutions can solve their problems.'\n        },\n        {\n            id: 'appointment',\n            name: 'Appointment Scheduler',\n            prompt: 'You are an appointment scheduling assistant. Your goal is to help callers schedule, reschedule, or cancel appointments. Collect necessary information such as name, contact details, preferred date and time, and reason for the appointment. Confirm all details before ending the call.'\n        }\n    ];\n    const filteredAgents = agents.filter((agent)=>agent.name.toLowerCase().includes(searchTerm.toLowerCase()));\n    const openNewAgentModal = ()=>{\n        setIsEditMode(false);\n        setCurrentAgent(null);\n        setNewAgentName('');\n        setNewAgentPrompt('');\n        setSelectedTemplate('');\n        setIsModalOpen(true);\n    };\n    const openEditAgentModal = (agent)=>{\n        setIsEditMode(true);\n        setCurrentAgent(agent);\n        setNewAgentName(agent.name);\n        setNewAgentPrompt(agent.prompt);\n        setSelectedTemplate('');\n        setIsModalOpen(true);\n    };\n    const closeModal = ()=>{\n        setIsModalOpen(false);\n    };\n    const applyTemplate = (templateId)=>{\n        const template = templates.find((t)=>t.id === templateId);\n        if (template) {\n            setNewAgentPrompt(template.prompt);\n            setSelectedTemplate(templateId);\n        }\n    };\n    const saveAgent = ()=>{\n        if (!newAgentName.trim()) {\n            setMessage({\n                type: 'error',\n                text: 'Please enter an agent name'\n            });\n            setTimeout(()=>setMessage({\n                    type: '',\n                    text: ''\n                }), 3000);\n            return;\n        }\n        if (!newAgentPrompt.trim()) {\n            setMessage({\n                type: 'error',\n                text: 'Please enter an agent prompt'\n            });\n            setTimeout(()=>setMessage({\n                    type: '',\n                    text: ''\n                }), 3000);\n            return;\n        }\n        if (isEditMode && currentAgent) {\n            // Update existing agent\n            const updatedAgents = agents.map((agent)=>agent.id === currentAgent.id ? {\n                    ...agent,\n                    name: newAgentName,\n                    prompt: newAgentPrompt\n                } : agent);\n            setAgents(updatedAgents);\n            setMessage({\n                type: 'success',\n                text: 'Agent updated successfully'\n            });\n        } else {\n            // Create new agent\n            const newAgent = {\n                id: (agents.length + 1).toString(),\n                name: newAgentName,\n                prompt: newAgentPrompt,\n                createdBy: '<EMAIL>',\n                createdAt: new Date().toLocaleString('en-US', {\n                    year: 'numeric',\n                    month: 'short',\n                    day: 'numeric',\n                    hour: 'numeric',\n                    minute: 'numeric',\n                    hour12: true\n                })\n            };\n            setAgents([\n                ...agents,\n                newAgent\n            ]);\n            setMessage({\n                type: 'success',\n                text: 'Agent created successfully'\n            });\n        }\n        setTimeout(()=>setMessage({\n                type: '',\n                text: ''\n            }), 3000);\n        closeModal();\n    };\n    const deleteAgent = (id)=>{\n        const updatedAgents = agents.filter((agent)=>agent.id !== id);\n        setAgents(updatedAgents);\n        setMessage({\n            type: 'success',\n            text: 'Agent deleted successfully'\n        });\n        setTimeout(()=>setMessage({\n                type: '',\n                text: ''\n            }), 3000);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6 max-w-7xl mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-semibold\",\n                                children: \"Agents\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 dark:text-gray-400\",\n                                children: \"Create and manage your AI agents\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-4 items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: openNewAgentModal,\n                            className: \"bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    className: \"h-4 w-4\",\n                                    viewBox: \"0 0 20 20\",\n                                    fill: \"currentColor\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, this),\n                                \"New Agent\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                lineNumber: 155,\n                columnNumber: 7\n            }, this),\n            message.text && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `p-4 mb-6 rounded-md ${message.type === 'success' ? 'bg-green-50 dark:bg-green-900/20 text-green-800 dark:text-green-200' : message.type === 'error' ? 'bg-red-50 dark:bg-red-900/20 text-red-800 dark:text-red-200' : ''}`,\n                children: message.text\n            }, void 0, false, {\n                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                lineNumber: 175,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-4 h-4 text-gray-500 dark:text-gray-400\",\n                                \"aria-hidden\": \"true\",\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                fill: \"none\",\n                                viewBox: \"0 0 20 20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    stroke: \"currentColor\",\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: \"2\",\n                                    d: \"m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"search\",\n                            className: \"block w-full p-2 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-white dark:bg-[#1A1A1A] dark:border-gray-700 dark:text-white focus:ring-indigo-500 focus:border-indigo-500\",\n                            placeholder: \"Search agents...\",\n                            value: searchTerm,\n                            onChange: (e)=>setSearchTerm(e.target.value)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                    lineNumber: 182,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                lineNumber: 181,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-[#1A1A1A] rounded-lg border border-gray-200 dark:border-gray-800 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"min-w-full divide-y divide-gray-200 dark:divide-gray-800\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            className: \"bg-gray-50 dark:bg-[#222222]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        scope: \"col\",\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\",\n                                        children: \"Name\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        scope: \"col\",\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\",\n                                        children: \"Created by\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        scope: \"col\",\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\",\n                                        children: \"Created at\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        scope: \"col\",\n                                        className: \"relative px-6 py-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"sr-only\",\n                                            children: \"Actions\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            className: \"bg-white dark:bg-[#1A1A1A] divide-y divide-gray-200 dark:divide-gray-800\",\n                            children: filteredAgents.length > 0 ? filteredAgents.map((agent)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    className: \"hover:bg-gray-50 dark:hover:bg-[#222222]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                            children: agent.name\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\",\n                                            children: agent.createdBy\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\",\n                                            children: agent.createdAt\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>openEditAgentModal(agent),\n                                                    className: \"text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300 mr-3\",\n                                                    children: \"Edit\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>deleteAgent(agent.id),\n                                                    className: \"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300\",\n                                                    children: \"Delete\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, agent.id, true, {\n                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 17\n                                }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    colSpan: 4,\n                                    className: \"px-6 py-10 text-center text-gray-500 dark:text-gray-400\",\n                                    children: \"No agents found matching your search\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                    lineNumber: 200,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                lineNumber: 199,\n                columnNumber: 7\n            }, this),\n            isModalOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 dark:bg-opacity-70 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-[#1A1A1A] rounded-lg max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-b border-gray-200 dark:border-gray-800 px-6 py-4 flex justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-medium\",\n                                    children: isEditMode ? 'Edit Agent' : 'Create New Agent'\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: closeModal,\n                                    className: \"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        className: \"h-5 w-5\",\n                                        viewBox: \"0 0 20 20\",\n                                        fill: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-y-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"lg:col-span-2 space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"agent-name\",\n                                                        className: \"block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300\",\n                                                        children: \"Agent Name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        id: \"agent-name\",\n                                                        value: newAgentName,\n                                                        onChange: (e)=>setNewAgentName(e.target.value),\n                                                        placeholder: \"Enter agent name\",\n                                                        className: \"block w-full p-2 text-sm text-gray-900 border border-gray-300 rounded-md bg-white dark:bg-[#1A1A1A] dark:border-gray-700 dark:text-white focus:ring-indigo-500 focus:border-indigo-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"agent-prompt\",\n                                                        className: \"block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300\",\n                                                        children: \"Agent Prompt\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                                        lineNumber: 288,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        id: \"agent-prompt\",\n                                                        value: newAgentPrompt,\n                                                        onChange: (e)=>setNewAgentPrompt(e.target.value),\n                                                        placeholder: \"Enter agent prompt...\",\n                                                        rows: 15,\n                                                        className: \"block w-full p-4 text-sm text-gray-900 border border-gray-300 rounded-md bg-white dark:bg-[#1A1A1A] dark:border-gray-700 dark:text-white focus:ring-indigo-500 focus:border-indigo-500 font-mono\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white dark:bg-[#222222] rounded-lg border border-gray-200 dark:border-gray-800 overflow-hidden\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border-b border-gray-200 dark:border-gray-800 px-4 py-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"font-medium text-sm\",\n                                                            children: \"Template Library\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                                            lineNumber: 304,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                                        lineNumber: 303,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4 space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                children: \"Choose a template to get started:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                                                lineNumber: 307,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            templates.map((template)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: `p-3 rounded-md cursor-pointer border text-sm ${selectedTemplate === template.id ? 'border-indigo-500 dark:border-indigo-400 bg-indigo-50 dark:bg-indigo-900/20' : 'border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800'}`,\n                                                                    onClick: ()=>applyTemplate(template.id),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"font-medium mb-1\",\n                                                                            children: template.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                                                            lineNumber: 315,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                            children: [\n                                                                                template.prompt.substring(0, 80),\n                                                                                \"...\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                                                            lineNumber: 316,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, template.id, true, {\n                                                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                                                    lineNumber: 310,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white dark:bg-[#222222] rounded-lg border border-gray-200 dark:border-gray-800 overflow-hidden\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border-b border-gray-200 dark:border-gray-800 px-4 py-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"font-medium text-sm\",\n                                                            children: \"Tips\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                                            lineNumber: 324,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                                        lineNumber: 323,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"space-y-2 text-xs text-gray-600 dark:text-gray-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    className: \"flex items-start gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                                            className: \"h-4 w-4 text-indigo-500 dark:text-indigo-400 flex-shrink-0\",\n                                                                            viewBox: \"0 0 20 20\",\n                                                                            fill: \"currentColor\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                fillRule: \"evenodd\",\n                                                                                d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\",\n                                                                                clipRule: \"evenodd\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                                                                lineNumber: 330,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                                                            lineNumber: 329,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Be specific about the AI's role and purpose\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                                                            lineNumber: 332,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                                                    lineNumber: 328,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    className: \"flex items-start gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                                            className: \"h-4 w-4 text-indigo-500 dark:text-indigo-400 flex-shrink-0\",\n                                                                            viewBox: \"0 0 20 20\",\n                                                                            fill: \"currentColor\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                fillRule: \"evenodd\",\n                                                                                d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\",\n                                                                                clipRule: \"evenodd\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                                                                lineNumber: 336,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                                                            lineNumber: 335,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Include guidelines for handling objections\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                                                            lineNumber: 338,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                                                    lineNumber: 334,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    className: \"flex items-start gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                                            className: \"h-4 w-4 text-indigo-500 dark:text-indigo-400 flex-shrink-0\",\n                                                                            viewBox: \"0 0 20 20\",\n                                                                            fill: \"currentColor\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                fillRule: \"evenodd\",\n                                                                                d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\",\n                                                                                clipRule: \"evenodd\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                                                                lineNumber: 342,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                                                            lineNumber: 341,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Define the tone and personality you want\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                                                            lineNumber: 344,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                                                    lineNumber: 340,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                                            lineNumber: 327,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-gray-200 dark:border-gray-800 px-6 py-4 flex justify-end gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: closeModal,\n                                    className: \"px-4 py-2 bg-white dark:bg-[#222222] border border-gray-300 dark:border-gray-700 rounded-md text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: saveAgent,\n                                    className: \"bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors\",\n                                    children: isEditMode ? 'Update Agent' : 'Create Agent'\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                                    lineNumber: 359,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                            lineNumber: 352,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                    lineNumber: 260,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n                lineNumber: 259,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\agents\\\\page.tsx\",\n        lineNumber: 153,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/agents/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/layout.tsx":
/*!**************************************!*\
  !*** ./src/app/dashboard/layout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Sidebar */ \"(ssr)/./src/components/ui/Sidebar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction DashboardLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Sidebar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1 overflow-auto\",\n                children: children\n            }, void 0, false, {\n                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2Rhc2hib2FyZC9sYXlvdXQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFFMEI7QUFDb0I7QUFFL0IsU0FBU0UsZ0JBQWdCLEVBQ3RDQyxRQUFRLEVBR1I7SUFDQSxxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUNKLDhEQUFPQTs7Ozs7MEJBQ1IsOERBQUNLO2dCQUFLRCxXQUFVOzBCQUNiRjs7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIkU6XFxxdWlja2NhbGxcXGZyb250ZW5kXFxxdWlja2NhbGwtZnJvbnRlbmRcXHNyY1xcYXBwXFxkYXNoYm9hcmRcXGxheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IFNpZGViYXIgZnJvbSAnQC9jb21wb25lbnRzL3VpL1NpZGViYXInO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBEYXNoYm9hcmRMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IFJlYWRvbmx5PHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn0+KSB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGgtc2NyZWVuXCI+XG4gICAgICA8U2lkZWJhciAvPlxuICAgICAgPG1haW4gY2xhc3NOYW1lPVwiZmxleC0xIG92ZXJmbG93LWF1dG9cIj5cbiAgICAgICAge2NoaWxkcmVufVxuICAgICAgPC9tYWluPlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiU2lkZWJhciIsIkRhc2hib2FyZExheW91dCIsImNoaWxkcmVuIiwiZGl2IiwiY2xhc3NOYW1lIiwibWFpbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Sidebar.tsx":
/*!***************************************!*\
  !*** ./src/components/ui/Sidebar.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst Sidebar = ()=>{\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const isActive = (path)=>{\n        return pathname === path || pathname?.startsWith(path + '/');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full w-64 bg-white dark:bg-[#1A1A1A] border-r border-gray-200 dark:border-gray-800 flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-gray-200 dark:border-gray-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-xl font-bold text-indigo-600 dark:text-indigo-400\",\n                    children: \"QuickCall\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"flex-1 p-4 overflow-y-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"px-2 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-2\",\n                                children: \"Main\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/dashboard\",\n                                            className: `flex items-center p-2 rounded-md ${isActive('/dashboard') && !isActive('/dashboard/prompt') && !isActive('/dashboard/agents') && !isActive('/dashboard/history') && !isActive('/dashboard/knowledge') && !isActive('/dashboard/numbers') && !isActive('/dashboard/settings') ? 'bg-indigo-50 dark:bg-indigo-900/20 text-indigo-600 dark:text-indigo-400' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800'}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-5 w-5 mr-3\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    fill: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                                                        lineNumber: 32,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                                                    lineNumber: 31,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Dashboard\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                                                    lineNumber: 34,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                                            lineNumber: 27,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                                        lineNumber: 26,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/dashboard/agents\",\n                                            className: `flex items-center p-2 rounded-md ${isActive('/dashboard/agents') ? 'bg-indigo-50 dark:bg-indigo-900/20 text-indigo-600 dark:text-indigo-400' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800'}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-5 w-5 mr-3\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    fill: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                                                        lineNumber: 43,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                                                    lineNumber: 42,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Agents\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                                                    lineNumber: 45,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                                            lineNumber: 38,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                                        lineNumber: 37,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/dashboard/history\",\n                                            className: `flex items-center p-2 rounded-md ${isActive('/dashboard/history') ? 'bg-indigo-50 dark:bg-indigo-900/20 text-indigo-600 dark:text-indigo-400' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800'}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-5 w-5 mr-3\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    fill: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                                                        lineNumber: 54,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                                                    lineNumber: 53,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Call History\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                                                    lineNumber: 56,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/dashboard/processes\",\n                                            className: `flex items-center p-2 rounded-md ${isActive('/dashboard/processes') ? 'bg-indigo-50 dark:bg-indigo-900/20 text-indigo-600 dark:text-indigo-400' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800'}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-5 w-5 mr-3\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    fill: \"currentColor\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M13 7H7v6h6V7z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                                                            lineNumber: 65,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M7 2a1 1 0 012 0v1h2V2a1 1 0 112 0v1h2a2 2 0 012 2v2h1a1 1 0 110 2h-1v2h1a1 1 0 110 2h-1v2a2 2 0 01-2 2h-2v1a1 1 0 11-2 0v-1H9v1a1 1 0 11-2 0v-1H5a2 2 0 01-2-2v-2H2a1 1 0 110-2h1V9H2a1 1 0 010-2h1V5a2 2 0 012-2h2V2zM5 5h10v10H5V5z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                                                            lineNumber: 66,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                                                    lineNumber: 64,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Call Processes\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                                                    lineNumber: 68,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                                            lineNumber: 60,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"px-2 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-2\",\n                                children: \"Resources\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center p-2 rounded-md text-gray-400 dark:text-gray-600 cursor-not-allowed\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-5 w-5 mr-3\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    fill: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                                                        lineNumber: 83,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                                                    lineNumber: 82,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Knowledge Base\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                                                    lineNumber: 85,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-2 text-xs bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-400 px-1.5 py-0.5 rounded\",\n                                                    children: \"Coming Soon\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                                                    lineNumber: 86,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center p-2 rounded-md text-gray-400 dark:text-gray-600 cursor-not-allowed\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-5 w-5 mr-3\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    fill: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                                                        lineNumber: 92,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                                                    lineNumber: 91,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Phone Numbers\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-2 text-xs bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-400 px-1.5 py-0.5 rounded\",\n                                                    children: \"Coming Soon\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                                                    lineNumber: 95,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"px-2 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-2\",\n                                children: \"Configuration\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"space-y-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/dashboard/settings\",\n                                        className: `flex items-center p-2 rounded-md ${isActive('/dashboard/settings') ? 'bg-indigo-50 dark:bg-indigo-900/20 text-indigo-600 dark:text-indigo-400' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800'}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                className: \"h-5 w-5 mr-3\",\n                                                viewBox: \"0 0 20 20\",\n                                                fill: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium\",\n                                                children: \"Settings\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t border-gray-200 dark:border-gray-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 rounded-full bg-indigo-100 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                className: \"h-4 w-4\",\n                                viewBox: \"0 0 20 20\",\n                                fill: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm font-medium\",\n                                    children: \"User Name\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                    children: \"<EMAIL>\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\ui\\\\Sidebar.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Sidebar);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Sidebar.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fagents%2Fpage&page=%2Fdashboard%2Fagents%2Fpage&appPaths=%2Fdashboard%2Fagents%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fagents%2Fpage.tsx&appDir=E%3A%5Cquickcall%5Cfrontend%5Cquickcall-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cquickcall%5Cfrontend%5Cquickcall-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();