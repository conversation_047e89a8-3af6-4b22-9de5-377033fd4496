/**
 * WARNING: This file was auto-generated by svgr. Do not edit.
 */
import * as React from 'react';
import type { SVGProps } from 'react';
/**
 * @internal
 */
const SvgLockLockedIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" width={16} height={16} fill="none" {...props}>
    <path
      fill="currentcolor"
      fillRule="evenodd"
      d="M4 6.104V4a4 4 0 1 1 8 0v2.104c1.154.326 2 1.387 2 2.646v4.5A2.75 2.75 0 0 1 11.25 16h-6.5A2.75 2.75 0 0 1 2 13.25v-4.5c0-1.259.846-2.32 2-2.646ZM5.5 4a2.5 2.5 0 0 1 5 0v2h-5V4Z"
      clipRule="evenodd"
    />
  </svg>
);
export default SvgLockLockedIcon;
