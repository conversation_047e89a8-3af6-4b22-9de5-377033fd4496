/* LiveKit Components Styles */
@import "@livekit/components-styles";

/* Custom styles for voice assistant */
.agent-visualizer {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: transparent;
}

.agent-visualizer .lk-audio-visualizer {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  height: 100%;
}

.agent-visualizer .lk-audio-visualizer-bar {
  background: linear-gradient(to top, #3b82f6, #60a5fa);
  border-radius: 2px;
  transition: height 0.1s ease;
}

/* Dark mode styles */
.dark .agent-visualizer .lk-audio-visualizer-bar {
  background: linear-gradient(to top, #60a5fa, #93c5fd);
}

/* LiveKit Room styles */
.lk-room-container {
  background: transparent;
}

.lk-control-bar {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding: 8px;
}

.dark .lk-control-bar {
  background: rgba(255, 255, 255, 0.1);
}

/* Voice assistant specific styles */
.voice-assistant-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  padding: 20px;
}

.voice-status {
  margin-top: 16px;
  font-weight: 500;
  text-align: center;
}

/* Animation for connecting state */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.connecting {
  animation: pulse 2s infinite;
}
