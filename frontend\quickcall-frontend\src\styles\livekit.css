/* LiveKit Components Custom Styles for Tailwind */

/* Base LiveKit component overrides */
.lk-button {
  @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background;
}

.lk-button-primary {
  @apply bg-primary text-primary-foreground hover:bg-primary/90;
}

.lk-button-secondary {
  @apply bg-secondary text-secondary-foreground hover:bg-secondary/80;
}

/* Audio visualizer styles */
.agent-visualizer {
  @apply flex items-center justify-center h-full bg-transparent;
}

.lk-audio-visualizer {
  @apply flex items-center justify-center gap-1 h-full;
}

.lk-audio-visualizer-bar {
  @apply bg-gradient-to-t from-blue-500 to-blue-400 rounded-sm transition-all duration-100 ease-in-out;
  min-width: 4px;
}

/* Dark mode audio visualizer */
.dark .lk-audio-visualizer-bar {
  @apply bg-gradient-to-t from-blue-400 to-blue-300;
}

/* LiveKit Room styles */
.lk-room-container {
  @apply bg-transparent;
}

.lk-control-bar {
  @apply bg-black/10 rounded-lg p-2;
}

.dark .lk-control-bar {
  @apply bg-white/10;
}

/* Voice assistant specific styles */
.voice-assistant-container {
  @apply flex flex-col items-center justify-center min-h-[300px] p-5;
}

.voice-status {
  @apply mt-4 font-medium text-center;
}

/* LiveKit participant styles */
.lk-participant {
  @apply relative;
}

.lk-participant-tile {
  @apply rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-800;
}

/* LiveKit focus layout */
.lk-focus-layout {
  @apply grid gap-2;
}

.lk-focus-layout .lk-focused-participant {
  @apply col-span-full;
}

/* Animation for connecting state */
.connecting {
  @apply animate-pulse;
}

/* Custom loading spinner */
.lk-spinner {
  @apply animate-spin rounded-full border-2 border-gray-300 border-t-blue-600;
}
