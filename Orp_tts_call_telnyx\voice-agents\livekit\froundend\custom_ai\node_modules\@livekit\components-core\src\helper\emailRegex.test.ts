import { describe, test } from 'vitest';
import { createEmailRegExp } from './emailRegex';

const fixtures = [
  '<EMAIL>',
  'foo@bar',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  'test@255.255.255.255',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>--jxalpdlp',
  '<EMAIL>',
  '!#$%&amp;`*+/=?^`{|}~@livekit.io',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '"\\a"@livekit.io',
  '""@livekit.io',
  '"test"@livekit.io',
  '"\\""@livekit.io',
  '<EMAIL>',
  '<EMAIL>-uk',
  'a@a.b.c.d.e.f.g.h.i.j.k.l.m.n.o.p.q.r.s.t.u.v.w.x.y.z.a.b.c.d.e.f.g.h.i.j.k.l.m.n.o.p.q.r.s.t.u.v.w.x.y.z.a.b.c.d.e.f.g.h.i.j.k.l.m.n.o.p.q.r.s.t.u.v.w.x.y.z.a.b.c.d.e.f.g.h.i.j.k.l.m.n.o.p.q.r.s.t.u.v.w.x.y.z.a.b.c.d.e.f.g.h.i.j.k.l.m.n.o.p.q.r.s.t.u.v',
  '<EMAIL>',
  '<EMAIL>',
  'foo@[IPv6:2001:db8::2]',
];

const fixturesNot = [
  '@',
  '@io',
  '@livekit.io',
  'test..livekit.io',
  '<EMAIL>',
  '<EMAIL>.',
  '.<EMAIL>',
  'livekit@<EMAIL>',
  'mailto:<EMAIL>',
  'foo.example.com',
  '<EMAIL>',
];

describe('Email regex tests', () => {
  test('extract', (t) => {
    for (const fixture of fixtures) {
      t.expect((createEmailRegExp().exec(`foo ${fixture} bar`) || [])[0]).toBe(fixture);
    }

    t.expect(createEmailRegExp().exec('mailto:<EMAIL>')?.[0]).toBe('<EMAIL>');
  });

  test('exact', (t) => {
    for (const fixture of fixtures) {
      t.expect(createEmailRegExp({ exact: true }).test(fixture)).toBeTruthy();
    }
  });

  test('failures', (t) => {
    for (const fixture of fixturesNot) {
      t.expect(createEmailRegExp({ exact: true }).test(fixture)).toBeFalsy();
    }
  });
});
