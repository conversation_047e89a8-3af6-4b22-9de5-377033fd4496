(()=>{var e={};e.id=646,e.ids=[646],e.modules={2704:(e,r,t)=>{Promise.resolve().then(t.bind(t,53560))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28454:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d});var s=t(60687),a=t(43210);function d(){let[e,r]=(0,a.useState)([{id:"1",title:"Product Catalog 2025",description:"Complete catalog of products and services with pricing",type:"pdf",dateAdded:"May 15, 2025",size:"4.2 MB"},{id:"2",title:"Customer FAQ",description:"Frequently asked questions and answers for customer support",type:"text",dateAdded:"May 12, 2025"},{id:"3",title:"Company Website",description:"Main company website with product information",type:"url",dateAdded:"May 10, 2025"},{id:"4",title:"Technical Documentation",description:"Technical specifications and implementation details",type:"pdf",dateAdded:"May 5, 2025",size:"8.7 MB"}]),[t,d]=(0,a.useState)(""),[i,o]=(0,a.useState)(!1),[n,l]=(0,a.useState)({type:"",text:""}),c=e.filter(e=>e.title.toLowerCase().includes(t.toLowerCase())||e.description.toLowerCase().includes(t.toLowerCase())),x=e=>{switch(e){case"pdf":return(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-red-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"})});case"text":return(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-blue-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})});case"url":return(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-green-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"})});default:return(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-gray-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"})})}};return(0,s.jsxs)("div",{className:"p-6 max-w-7xl mx-auto",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-semibold",children:"Knowledge Base"}),(0,s.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"Manage your AI agent's knowledge sources"})]}),(0,s.jsx)("div",{children:(0,s.jsx)("button",{onClick:()=>{o(!0),setTimeout(()=>{o(!1),l({type:"success",text:"File uploaded successfully"}),setTimeout(()=>{l({type:"",text:""})},3e3)},1500)},disabled:i,className:"bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md font-medium transition-colors flex items-center gap-2 disabled:opacity-50",children:i?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("svg",{className:"animate-spin h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Uploading..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z",clipRule:"evenodd"})}),"Upload File"]})})})]}),n.text&&(0,s.jsx)("div",{className:`p-4 mb-6 rounded-md ${"success"===n.type?"bg-green-50 dark:bg-green-900/20 text-green-800 dark:text-green-200":"error"===n.type?"bg-red-50 dark:bg-red-900/20 text-red-800 dark:text-red-200":""}`,children:n.text}),(0,s.jsx)("div",{className:"bg-white dark:bg-[#1A1A1A] rounded-lg border border-gray-200 dark:border-gray-800 p-4 mb-6",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none",children:(0,s.jsx)("svg",{className:"w-4 h-4 text-gray-500 dark:text-gray-400","aria-hidden":"true",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 20 20",children:(0,s.jsx)("path",{stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"})})}),(0,s.jsx)("input",{type:"search",className:"block w-full p-2 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-white dark:bg-[#1A1A1A] dark:border-gray-700 dark:text-white focus:ring-indigo-500 focus:border-indigo-500",placeholder:"Search knowledge base...",value:t,onChange:e=>d(e.target.value)})]})}),(0,s.jsxs)("div",{className:"bg-white dark:bg-[#1A1A1A] rounded-lg border border-gray-200 dark:border-gray-800 overflow-hidden",children:[(0,s.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-800 px-6 py-4",children:(0,s.jsx)("h2",{className:"font-medium",children:"Knowledge Sources"})}),(0,s.jsx)("div",{className:"divide-y divide-gray-200 dark:divide-gray-800",children:c.length>0?c.map(e=>(0,s.jsx)("div",{className:"p-4 hover:bg-gray-50 dark:hover:bg-[#222222] transition-colors",children:(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)("div",{className:"flex-shrink-0 mr-4",children:x(e.type)}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("h3",{className:"text-sm font-medium",children:e.title}),(0,s.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:e.description}),(0,s.jsxs)("div",{className:"mt-1 flex items-center text-xs text-gray-500 dark:text-gray-400",children:[(0,s.jsxs)("span",{children:["Added ",e.dateAdded]}),e.size&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("span",{className:"mx-2",children:"•"}),(0,s.jsx)("span",{children:e.size})]}),(0,s.jsx)("span",{className:"mx-2",children:"•"}),(0,s.jsx)("span",{className:"capitalize",children:e.type})]})]}),(0,s.jsx)("div",{className:"flex-shrink-0 ml-4",children:(0,s.jsx)("button",{className:"text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{d:"M6 10a2 2 0 11-4 0 2 2 0 014 0zM12 10a2 2 0 11-4 0 2 2 0 014 0zM16 12a2 2 0 100-4 2 2 0 000 4z"})})})})]})},e.id)):(0,s.jsx)("div",{className:"p-8 text-center text-gray-500 dark:text-gray-400",children:"No knowledge sources found matching your search"})})]}),(0,s.jsxs)("div",{className:"bg-white dark:bg-[#1A1A1A] rounded-lg border border-gray-200 dark:border-gray-800 overflow-hidden mt-8",children:[(0,s.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-800 px-6 py-4",children:(0,s.jsx)("h2",{className:"font-medium",children:"Add Knowledge Source"})}),(0,s.jsx)("div",{className:"p-6",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,s.jsx)("div",{className:"p-4 border border-gray-200 dark:border-gray-700 rounded-lg cursor-pointer hover:border-indigo-500 dark:hover:border-indigo-500 transition-colors",children:(0,s.jsxs)("div",{className:"flex flex-col items-center text-center",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-indigo-100 dark:bg-indigo-900/30 rounded-full flex items-center justify-center text-indigo-600 dark:text-indigo-400 mb-3",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"})})}),(0,s.jsx)("h3",{className:"font-medium mb-1",children:"Upload File"}),(0,s.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"PDF, Word, or text files"})]})}),(0,s.jsx)("div",{className:"p-4 border border-gray-200 dark:border-gray-700 rounded-lg cursor-pointer hover:border-indigo-500 dark:hover:border-indigo-500 transition-colors",children:(0,s.jsxs)("div",{className:"flex flex-col items-center text-center",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-indigo-100 dark:bg-indigo-900/30 rounded-full flex items-center justify-center text-indigo-600 dark:text-indigo-400 mb-3",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"})})}),(0,s.jsx)("h3",{className:"font-medium mb-1",children:"Add Website"}),(0,s.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Add a URL to crawl"})]})}),(0,s.jsx)("div",{className:"p-4 border border-gray-200 dark:border-gray-700 rounded-lg cursor-pointer hover:border-indigo-500 dark:hover:border-indigo-500 transition-colors",children:(0,s.jsxs)("div",{className:"flex flex-col items-center text-center",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-indigo-100 dark:bg-indigo-900/30 rounded-full flex items-center justify-center text-indigo-600 dark:text-indigo-400 mb-3",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})})}),(0,s.jsx)("h3",{className:"font-medium mb-1",children:"Create Text"}),(0,s.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Write or paste text"})]})})]})})]})]})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},49597:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>h,tree:()=>l});var s=t(65239),a=t(48088),d=t(88170),i=t.n(d),o=t(30893),n={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);t.d(r,n);let l={children:["",{children:["dashboard",{children:["knowledge",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,53560)),"E:\\quickcall\\frontend\\quickcall-frontend\\src\\app\\dashboard\\knowledge\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,63144)),"E:\\quickcall\\frontend\\quickcall-frontend\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"E:\\quickcall\\frontend\\quickcall-frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["E:\\quickcall\\frontend\\quickcall-frontend\\src\\app\\dashboard\\knowledge\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/knowledge/page",pathname:"/dashboard/knowledge",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},53560:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\knowledge\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\quickcall\\frontend\\quickcall-frontend\\src\\app\\dashboard\\knowledge\\page.tsx","default")},60848:(e,r,t)=>{Promise.resolve().then(t.bind(t,28454))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,771,658,418,310],()=>t(49597));module.exports=s})();