"use client";

import { DisconnectB<PERSON>on, AgentState } from "@livekit/components-react";
import { But<PERSON> } from "@/components/ui/button";
import { Mi<PERSON>, MicOff, Phone, PhoneOff } from "lucide-react";

interface ControlBarProps {
  onConnectButtonClicked: () => void;
  agentState: AgentState;
}

const ControlBar: React.FC<ControlBarProps> = ({
  onConnectButtonClicked,
  agentState,
}) => {
  const isConnected = agentState !== "disconnected";
  
  return (
    <div className="flex items-center justify-center gap-4 p-4 rounded-lg bg-muted/50">
      {isConnected ? (
        <>
          <div className="text-sm font-medium text-muted-foreground">
            Status: <span className="capitalize">{agentState}</span>
          </div>
          <DisconnectButton>
            <Button variant="destructive" size="sm">
              <PhoneOff className="w-4 h-4 mr-2" />
              Disconnect
            </Button>
          </DisconnectButton>
        </>
      ) : (
        <Button
          onClick={onConnectButtonClicked}
          variant="default"
          size="sm"
        >
          <Phone className="w-4 h-4 mr-2" />
          Connect
        </Button>
      )}
    </div>
  );
};

export default ControlBar;
