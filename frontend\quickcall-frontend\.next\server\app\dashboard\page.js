(()=>{var e={};e.id=105,e.ids=[105],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4780:(e,t,r)=>{"use strict";r.d(t,{cn:()=>i});var a=r(49384),s=r(82348);function i(...e){return(0,s.QP)((0,a.$)(e))}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21811:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var a=r(65239),s=r(48088),i=r(88170),n=r.n(i),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,80559)),"E:\\quickcall\\frontend\\quickcall-frontend\\src\\app\\dashboard\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,63144)),"E:\\quickcall\\frontend\\quickcall-frontend\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"E:\\quickcall\\frontend\\quickcall-frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["E:\\quickcall\\frontend\\quickcall-frontend\\src\\app\\dashboard\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,t,r)=>{"use strict";r.d(t,{$:()=>l});var a=r(60687);r(43210);var s=r(8730),i=r(24224),n=r(4780);let o=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:r,asChild:i=!1,...l}){let d=i?s.DX:"button";return(0,a.jsx)(d,{"data-slot":"button",className:(0,n.cn)(o({variant:t,size:r,className:e})),...l})}},32721:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>w});var a=r(60687),s=r(43210),i=r(16189),n=r(44493),o=r(29523),l=r(89667),d=r(34729),c=r(96834),u=r(85763),m=r(91821),p=r(62688);let h=(0,p.A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]),x=(0,p.A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);var v=r(41862),g=r(48340);let f=(0,p.A)("mic",[["path",{d:"M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z",key:"131961"}],["path",{d:"M19 10v2a7 7 0 0 1-14 0v-2",key:"1vc78b"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]]);var b=r(97840);let j=(0,p.A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),y={testTTS:async e=>{try{let t=await fetch("http://localhost:9090/api/tts/test",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({text:e.text,voice:e.voice})});if(!t.ok){let e=await t.json();throw Error(e.error||`HTTP error! status: ${t.status}`)}let r=await t.blob();return URL.createObjectURL(r)}catch(e){throw console.error("Error in ttsService.testTTS:",e),e}}};function w(){let[e,t]=(0,s.useState)("1"),[r,p]=(0,s.useState)("elise"),[w,k]=(0,s.useState)("llama-3.3-70b-versatile"),[N,A]=(0,s.useState)(!1),[P,S]=(0,s.useState)(""),[T,_]=(0,s.useState)(null),[C,E]=(0,s.useState)(null),[q,F]=(0,s.useState)(""),[$,z]=(0,s.useState)(!1),[B,O]=(0,s.useState)(""),[R,G]=(0,s.useState)("Hello, this is a test of the text-to-speech system. How does this voice sound?"),[M,Z]=(0,s.useState)("elise"),[Y,U]=(0,s.useState)(null),[V,L]=(0,s.useState)(!1),[W,X]=(0,s.useState)(""),H=(0,s.useRef)(null),[J,D]=(0,s.useState)([{id:"1",name:"Mia",description:"General purpose assistant",systemPrompt:`# You are Mia, a helpful and friendly AI assistant

You are designed to be helpful, harmless, and honest in all your interactions. Your primary goal is to assist the caller with any questions or tasks they might have.

## Guidelines for your conversation:
- Be friendly and conversational, but professional
- Provide comprehensive and accurate information
- Ask clarifying questions when needed
- Avoid making assumptions about the caller
- Be respectful of the caller's time
- Offer additional help when appropriate

You have a wide range of knowledge and can help with many topics including technology, science, history, culture, and more.`,isActive:!1},{id:"2",name:"Support Agent",description:"Customer support specialist",systemPrompt:`# You are a Technical Support Specialist

You are a knowledgeable and patient technical support agent. Your goal is to help customers troubleshoot and resolve their technical issues efficiently and effectively.

## Guidelines for your conversation:
- Begin by identifying yourself and asking how you can help
- Listen carefully to the customer's issue
- Ask relevant diagnostic questions to understand the problem
- Provide clear, step-by-step instructions
- Confirm whether the solution worked
- Offer additional assistance if needed

You have expertise in common technical issues related to software, hardware, networking, and account management. Always prioritize customer satisfaction and problem resolution.`,isActive:!1},{id:"3",name:"Sales Agent",description:"Product sales specialist",systemPrompt:`# You are a Professional Sales Consultant

You are a knowledgeable and persuasive sales consultant. Your goal is to understand customer needs and recommend appropriate products or services that provide genuine value.

## Guidelines for your conversation:
- Begin with a warm greeting and introduction
- Ask open-ended questions to understand customer needs
- Listen actively and acknowledge customer responses
- Present relevant product features and benefits
- Address objections professionally
- Guide the customer toward a decision without being pushy
- Thank the customer for their time

You have deep product knowledge and excellent communication skills. Focus on building rapport and trust rather than pushing for an immediate sale.`,isActive:!1}]),I=[{id:"elise",name:"Elise",description:"Professional female voice"},{id:"laila",name:"Laila",description:"Confident female voice"},{id:"tara",name:"Tara",description:"Warm female voice"}];(0,i.useRouter)();let K=async e=>{try{_(e),S("");let t=J.find(t=>t.id===e);if(!t)throw Error("Agent not found");let r=await fetch("http://localhost:9090/dynamic_agent/start",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({agent_name:t.name,llm_prompt:t.systemPrompt})});if(!r.ok){let e=await r.json();throw Error(e.error||`HTTP error! status: ${r.status}`)}let a=await r.json();console.log("Agent activated:",a),D(J.map(t=>t.id===e?{...t,isActive:!0}:t))}catch(e){console.error("Error activating agent:",e),S(`Failed to activate agent: ${e instanceof Error?e.message:"Unknown error"}`)}finally{_(null)}},Q=async e=>{try{E(e),S("");let t=await fetch("http://localhost:9090/api/agents/deactivate",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({agent_id:e})});if(!t.ok){let e=await t.json();throw Error(e.error||`HTTP error! status: ${t.status}`)}let r=await t.json();console.log("Agent deactivated:",r),D(J.map(t=>t.id===e?{...t,isActive:!1}:t))}catch(e){console.error("Error deactivating agent:",e),S(`Failed to deactivate agent: ${e instanceof Error?e.message:"Unknown error"}`)}finally{E(null)}},ee=async()=>{if(!q)return void O("Please enter a phone number.");let e=J.filter(e=>e.isActive);if(0===e.length)return void O("No active agents found. Please activate an agent first.");let t=e[0],r=t.name,a=t.id;z(!0),O(""),S("");try{let e=await fetch("http://localhost:9090/make_call",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({agent_name_for_dispatch:r,agent_id:a,phone_number:q})}),t=await e.json();if(!e.ok)throw Error(t.error||t.message||`HTTP error! status: ${e.status}`);O(`Call dispatched successfully using agent "${r}"! Output: ${"string"==typeof t.output?t.output:JSON.stringify(t.output)}`),F("")}catch(t){console.error("Error making call:",t);let e=t instanceof Error?t.message:"Unknown error";O(`Failed to make call: ${e}`),S(`Failed to make call: ${e}`)}finally{z(!1)}},et=async()=>{if(!R)return void X("Please enter text to test");try{L(!0),X(""),Y&&(URL.revokeObjectURL(Y),U(null));let e=await y.testTTS({text:R,voice:M});U(e),setTimeout(()=>{H.current&&H.current.play().catch(e=>console.error("Auto-play failed:",e))},100)}catch(e){console.error("Error testing voice:",e),X(`Failed to test voice: ${e instanceof Error?e.message:"Unknown error"}`)}finally{L(!1)}},er=J.filter(e=>e.isActive);return(0,a.jsxs)("div",{className:"p-6 max-w-7xl mx-auto space-y-8",children:[(0,a.jsx)("div",{className:"flex justify-between items-center",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:"Outbound Calls"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Make automated phone calls with AI agents"})]})}),P&&(0,a.jsxs)(m.Fc,{variant:"destructive",children:[(0,a.jsx)(h,{className:"h-4 w-4"}),(0,a.jsx)(m.TN,{children:P})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,a.jsx)("div",{className:"lg:col-span-2",children:(0,a.jsxs)(u.tU,{defaultValue:"single",className:"w-full",children:[(0,a.jsxs)(u.j7,{className:"grid w-full grid-cols-4",children:[(0,a.jsx)(u.Xi,{value:"single",children:"Single Call"}),(0,a.jsx)(u.Xi,{value:"batch",children:"Batch Calls"}),(0,a.jsx)(u.Xi,{value:"agents",children:"Agents"}),(0,a.jsx)(u.Xi,{value:"voice-test",children:"Voice Test"})]}),(0,a.jsx)(u.av,{value:"single",className:"space-y-6",children:(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsx)(n.ZB,{children:"Make a Call"})}),(0,a.jsxs)(n.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Phone Number"}),(0,a.jsx)(l.p,{type:"tel",value:q,onChange:e=>F(e.target.value),placeholder:"+****************",disabled:$})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Select Active Agent"}),er.length>0?(0,a.jsx)("div",{className:"grid gap-3",children:er.map(r=>(0,a.jsx)("div",{onClick:()=>t(r.id),className:`p-4 rounded-lg border-2 cursor-pointer transition-all ${e===r.id?"border-primary bg-primary/5":"border-border hover:border-primary/50"}`,children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium",children:r.name}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:r.description})]}),e===r.id&&(0,a.jsx)(x,{className:"h-5 w-5 text-primary"})]})},r.id))}):(0,a.jsx)(m.Fc,{children:(0,a.jsx)(m.TN,{children:"No active agents. Please activate an agent in the Agents tab."})})]}),(0,a.jsx)(o.$,{onClick:ee,disabled:$||!e||!q||0===er.length,className:"w-full",size:"lg",children:$?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(v.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Dispatching Call..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(g.A,{className:"w-4 h-4 mr-2"}),"Dispatch Call"]})}),B&&(0,a.jsx)(m.Fc,{variant:B.startsWith("Failed")?"destructive":"default",children:(0,a.jsx)(m.TN,{children:B})})]})]})}),(0,a.jsx)(u.av,{value:"batch",children:(0,a.jsx)(n.Zp,{children:(0,a.jsx)(n.Wu,{className:"py-8",children:(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)("p",{className:"text-muted-foreground",children:"Batch calling functionality coming soon"})})})})}),(0,a.jsxs)(u.av,{value:"agents",className:"space-y-6",children:[(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsx)(n.ZB,{children:"Agent Management"})}),(0,a.jsx)(n.Wu,{children:(0,a.jsx)("div",{className:"grid gap-4",children:J.map(e=>(0,a.jsx)("div",{className:"p-4 border rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,a.jsx)("h3",{className:"font-medium",children:e.name}),(0,a.jsx)(c.E,{variant:e.isActive?"default":"secondary",children:e.isActive?"Active":"Inactive"})]}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:e.description})]}),e.isActive?(0,a.jsx)(o.$,{variant:"destructive",size:"sm",onClick:()=>Q(e.id),disabled:C===e.id,children:C===e.id?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(v.A,{className:"w-4 h-4 mr-1 animate-spin"}),"Stopping..."]}):"Stop"}):(0,a.jsx)(o.$,{variant:"default",size:"sm",onClick:()=>K(e.id),disabled:T===e.id,children:T===e.id?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(v.A,{className:"w-4 h-4 mr-1 animate-spin"}),"Starting..."]}):"Start"})]})},e.id))})})]}),(0,a.jsx)(m.Fc,{children:(0,a.jsx)(m.TN,{children:'Start an agent to make it available for calls. Active agents will appear in the "Select Active Agent" section when making calls.'})})]}),(0,a.jsx)(u.av,{value:"voice-test",className:"space-y-6",children:(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsx)(n.ZB,{children:"Voice Testing"})}),(0,a.jsxs)(n.Wu,{className:"space-y-6",children:[W&&(0,a.jsxs)(m.Fc,{variant:"destructive",children:[(0,a.jsx)(h,{className:"h-4 w-4"}),(0,a.jsx)(m.TN,{children:W})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Test Text"}),(0,a.jsx)(d.T,{value:R,onChange:e=>G(e.target.value),placeholder:"Enter text to convert to speech",rows:4})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Voice"}),(0,a.jsx)("div",{className:"grid gap-3",children:I.map(e=>(0,a.jsx)("div",{onClick:()=>Z(e.id),className:`p-3 rounded-lg border-2 cursor-pointer transition-all ${M===e.id?"border-primary bg-primary/5":"border-border hover:border-primary/50"}`,children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-sm",children:e.name}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:e.description})]}),M===e.id&&(0,a.jsx)(x,{className:"h-4 w-4 text-primary"})]})},e.id))})]}),Y&&(0,a.jsx)("div",{className:"p-4 bg-muted rounded-lg",children:(0,a.jsx)("audio",{ref:H,src:Y,controls:!0,className:"w-full"})}),(0,a.jsxs)("div",{className:"flex gap-3",children:[(0,a.jsx)(o.$,{onClick:et,disabled:V||!R,className:"flex-1",children:V?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(v.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Generating..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(f,{className:"w-4 h-4 mr-2"}),"Generate Audio"]})}),Y&&(0,a.jsxs)(o.$,{variant:"outline",onClick:()=>{H.current&&H.current.play().catch(e=>console.error("Play failed:",e))},children:[(0,a.jsx)(b.A,{className:"w-4 h-4 mr-2"}),"Play"]})]})]})]})})]})}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(f,{className:"w-4 h-4"}),"Voice Settings"]})}),(0,a.jsx)(n.Wu,{className:"space-y-3",children:I.map(e=>(0,a.jsx)("div",{onClick:()=>p(e.id),className:`p-3 rounded-lg border cursor-pointer transition-all ${r===e.id?"border-primary bg-primary/5":"border-border hover:border-primary/50"}`,children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-sm",children:e.name}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:e.description})]}),r===e.id&&(0,a.jsx)(x,{className:"h-4 w-4 text-primary"})]})},e.id))})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(j,{className:"w-4 h-4"}),"Model"]})}),(0,a.jsx)(n.Wu,{className:"space-y-3",children:[{id:"llama-3.3-70b-versatile",name:"Llama 3.3 70B",description:"High-performance large language model"},{id:"gpt-4o",name:"GPT-4o",description:"Powerful multimodal model"},{id:"claude-3.5-sonnet",name:"Claude 3.5 Sonnet",description:"Fast and capable model"},{id:"mixtral-8x7b",name:"Mixtral 8x7B",description:"Powerful mixture-of-experts model"}].map(e=>(0,a.jsx)("div",{onClick:()=>k(e.id),className:`p-3 rounded-lg border cursor-pointer transition-all ${w===e.id?"border-primary bg-primary/5":"border-border hover:border-primary/50"}`,children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-sm",children:e.name}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:e.description})]}),w===e.id&&(0,a.jsx)(x,{className:"h-4 w-4 text-primary"})]})},e.id))})]})]})]})]})}},33873:e=>{"use strict";e.exports=require("path")},34729:(e,t,r)=>{"use strict";r.d(t,{T:()=>i});var a=r(60687);r(43210);var s=r(4780);function i({className:e,...t}){return(0,a.jsx)("textarea",{"data-slot":"textarea",className:(0,s.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...t})}},35132:(e,t,r)=>{Promise.resolve().then(r.bind(r,32721))},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>l,Wu:()=>d,ZB:()=>o,Zp:()=>i,aR:()=>n});var a=r(60687);r(43210);var s=r(4780);function i({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function n({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function o({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",e),...t})}function l({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",e),...t})}function d({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",e),...t})}},48700:(e,t,r)=>{Promise.resolve().then(r.bind(r,80559))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},80559:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\quickcall\\frontend\\quickcall-frontend\\src\\app\\dashboard\\page.tsx","default")},85763:(e,t,r)=>{"use strict";r.d(t,{Xi:()=>l,av:()=>d,j7:()=>o,tU:()=>n});var a=r(60687);r(43210);var s=r(60901),i=r(4780);function n({className:e,...t}){return(0,a.jsx)(s.bL,{"data-slot":"tabs",className:(0,i.cn)("flex flex-col gap-2",e),...t})}function o({className:e,...t}){return(0,a.jsx)(s.B8,{"data-slot":"tabs-list",className:(0,i.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",e),...t})}function l({className:e,...t}){return(0,a.jsx)(s.l9,{"data-slot":"tabs-trigger",className:(0,i.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...t})}function d({className:e,...t}){return(0,a.jsx)(s.UC,{"data-slot":"tabs-content",className:(0,i.cn)("flex-1 outline-none",e),...t})}},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var a=r(60687);r(43210);var s=r(4780);function i({className:e,type:t,...r}){return(0,a.jsx)("input",{type:t,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},91821:(e,t,r)=>{"use strict";r.d(t,{Fc:()=>o,TN:()=>l});var a=r(60687);r(43210);var s=r(24224),i=r(4780);let n=(0,s.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,...r}){return(0,a.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,i.cn)(n({variant:t}),e),...r})}function l({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"alert-description",className:(0,i.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",e),...t})}},96834:(e,t,r)=>{"use strict";r.d(t,{E:()=>l});var a=r(60687);r(43210);var s=r(8730),i=r(24224),n=r(4780);let o=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,asChild:r=!1,...i}){let l=r?s.DX:"span";return(0,a.jsx)(l,{"data-slot":"badge",className:(0,n.cn)(o({variant:t}),e),...i})}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[447,771,658,418,670,310],()=>r(21811));module.exports=a})();