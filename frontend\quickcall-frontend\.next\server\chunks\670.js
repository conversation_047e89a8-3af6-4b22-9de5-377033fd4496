"use strict";exports.id=670,exports.ids=[670],exports.modules={43:(e,r,t)=>{t.d(r,{jH:()=>a});var o=t(43210);t(60687);var n=o.createContext(void 0);function a(e){let r=o.useContext(n);return e||r||"ltr"}},8730:(e,r,t)=>{t.d(r,{DX:()=>i,TL:()=>l});var o=t(43210),n=t(98599),a=t(60687);function l(e){let r=function(e){let r=o.forwardRef((e,r)=>{let{children:t,...a}=e;if(o.isValidElement(t)){var l;let e,i,s=(l=t,(i=(e=Object.getOwnPropertyDescriptor(l.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(i=(e=Object.getOwnPropertyDescriptor(l,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref),c=function(e,r){let t={...r};for(let o in r){let n=e[o],a=r[o];/^on[A-Z]/.test(o)?n&&a?t[o]=(...e)=>{let r=a(...e);return n(...e),r}:n&&(t[o]=n):"style"===o?t[o]={...n,...a}:"className"===o&&(t[o]=[n,a].filter(Boolean).join(" "))}return{...e,...t}}(a,t.props);return t.type!==o.Fragment&&(c.ref=r?(0,n.t)(r,s):s),o.cloneElement(t,c)}return o.Children.count(t)>1?o.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=o.forwardRef((e,t)=>{let{children:n,...l}=e,i=o.Children.toArray(n),s=i.find(c);if(s){let e=s.props.children,n=i.map(r=>r!==s?r:o.Children.count(e)>1?o.Children.only(null):o.isValidElement(e)?e.props.children:null);return(0,a.jsx)(r,{...l,ref:t,children:o.isValidElement(e)?o.cloneElement(e,void 0,n):null})}return(0,a.jsx)(r,{...l,ref:t,children:n})});return t.displayName=`${e}.Slot`,t}var i=l("Slot"),s=Symbol("radix.slottable");function c(e){return o.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===s}},9510:(e,r,t)=>{t.d(r,{N:()=>s});var o=t(43210),n=t(11273),a=t(98599),l=t(8730),i=t(60687);function s(e){let r=e+"CollectionProvider",[t,s]=(0,n.A)(r),[c,d]=t(r,{collectionRef:{current:null},itemMap:new Map}),u=e=>{let{scope:r,children:t}=e,n=o.useRef(null),a=o.useRef(new Map).current;return(0,i.jsx)(c,{scope:r,itemMap:a,collectionRef:n,children:t})};u.displayName=r;let m=e+"CollectionSlot",p=(0,l.TL)(m),f=o.forwardRef((e,r)=>{let{scope:t,children:o}=e,n=d(m,t),l=(0,a.s)(r,n.collectionRef);return(0,i.jsx)(p,{ref:l,children:o})});f.displayName=m;let b=e+"CollectionItemSlot",g="data-radix-collection-item",h=(0,l.TL)(b),v=o.forwardRef((e,r)=>{let{scope:t,children:n,...l}=e,s=o.useRef(null),c=(0,a.s)(r,s),u=d(b,t);return o.useEffect(()=>(u.itemMap.set(s,{ref:s,...l}),()=>void u.itemMap.delete(s))),(0,i.jsx)(h,{...{[g]:""},ref:c,children:n})});return v.displayName=b,[{Provider:u,Slot:f,ItemSlot:v},function(r){let t=d(e+"CollectionConsumer",r);return o.useCallback(()=>{let e=t.collectionRef.current;if(!e)return[];let r=Array.from(e.querySelectorAll(`[${g}]`));return Array.from(t.itemMap.values()).sort((e,t)=>r.indexOf(e.ref.current)-r.indexOf(t.ref.current))},[t.collectionRef,t.itemMap])},s]}var c=new WeakMap;function d(e,r){if("at"in Array.prototype)return Array.prototype.at.call(e,r);let t=function(e,r){let t=e.length,o=u(r),n=o>=0?o:t+o;return n<0||n>=t?-1:n}(e,r);return -1===t?void 0:e[t]}function u(e){return e!=e||0===e?0:Math.trunc(e)}},11273:(e,r,t)=>{t.d(r,{A:()=>a});var o=t(43210),n=t(60687);function a(e,r=[]){let t=[],l=()=>{let r=t.map(e=>o.createContext(e));return function(t){let n=t?.[e]||r;return o.useMemo(()=>({[`__scope${e}`]:{...t,[e]:n}}),[t,n])}};return l.scopeName=e,[function(r,a){let l=o.createContext(a),i=t.length;t=[...t,a];let s=r=>{let{scope:t,children:a,...s}=r,c=t?.[e]?.[i]||l,d=o.useMemo(()=>s,Object.values(s));return(0,n.jsx)(c.Provider,{value:d,children:a})};return s.displayName=r+"Provider",[s,function(t,n){let s=n?.[e]?.[i]||l,c=o.useContext(s);if(c)return c;if(void 0!==a)return a;throw Error(`\`${t}\` must be used within \`${r}\``)}]},function(...e){let r=e[0];if(1===e.length)return r;let t=()=>{let t=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let n=t.reduce((r,{useScope:t,scopeName:o})=>{let n=t(e)[`__scope${o}`];return{...r,...n}},{});return o.useMemo(()=>({[`__scope${r.scopeName}`]:n}),[n])}};return t.scopeName=r.scopeName,t}(l,...r)]}},13495:(e,r,t)=>{t.d(r,{c:()=>n});var o=t(43210);function n(e){let r=o.useRef(e);return o.useEffect(()=>{r.current=e}),o.useMemo(()=>(...e)=>r.current?.(...e),[])}},14163:(e,r,t)=>{t.d(r,{hO:()=>s,sG:()=>i});var o=t(43210),n=t(51215),a=t(8730),l=t(60687),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,r)=>{let t=(0,a.TL)(`Primitive.${r}`),n=o.forwardRef((e,o)=>{let{asChild:n,...a}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(n?t:r,{...a,ref:o})});return n.displayName=`Primitive.${r}`,{...e,[r]:n}},{});function s(e,r){e&&n.flushSync(()=>e.dispatchEvent(r))}},24224:(e,r,t)=>{t.d(r,{F:()=>l});var o=t(49384);let n=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=o.$,l=(e,r)=>t=>{var o;if((null==r?void 0:r.variants)==null)return a(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:l,defaultVariants:i}=r,s=Object.keys(l).map(e=>{let r=null==t?void 0:t[e],o=null==i?void 0:i[e];if(null===r)return null;let a=n(r)||n(o);return l[e][a]}),c=t&&Object.entries(t).reduce((e,r)=>{let[t,o]=r;return void 0===o||(e[t]=o),e},{});return a(e,s,null==r||null==(o=r.compoundVariants)?void 0:o.reduce((e,r)=>{let{class:t,className:o,...n}=r;return Object.entries(n).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...i,...c}[r]):({...i,...c})[r]===t})?[...e,t,o]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}},41862:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},48340:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(62688).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},49384:(e,r,t)=>{t.d(r,{$:()=>o});function o(){for(var e,r,t=0,o="",n=arguments.length;t<n;t++)(e=arguments[t])&&(r=function e(r){var t,o,n="";if("string"==typeof r||"number"==typeof r)n+=r;else if("object"==typeof r)if(Array.isArray(r)){var a=r.length;for(t=0;t<a;t++)r[t]&&(o=e(r[t]))&&(n&&(n+=" "),n+=o)}else for(o in r)r[o]&&(n&&(n+=" "),n+=o);return n}(e))&&(o&&(o+=" "),o+=r);return o}},60901:(e,r,t)=>{t.d(r,{UC:()=>Q,B8:()=>Z,bL:()=>H,l9:()=>X});var o=t(43210),n=t(70569),a=t(11273),l=t(9510),i=t(98599),s=t(96963),c=t(14163),d=t(13495),u=t(65551),m=t(43),p=t(60687),f="rovingFocusGroup.onEntryFocus",b={bubbles:!1,cancelable:!0},g="RovingFocusGroup",[h,v,w]=(0,l.N)(g),[y,x]=(0,a.A)(g,[w]),[k,N]=y(g),z=o.forwardRef((e,r)=>(0,p.jsx)(h.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(h.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(A,{...e,ref:r})})}));z.displayName=g;var A=o.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:t,orientation:a,loop:l=!1,dir:s,currentTabStopId:h,defaultCurrentTabStopId:w,onCurrentTabStopIdChange:y,onEntryFocus:x,preventScrollOnEntryFocus:N=!1,...z}=e,A=o.useRef(null),j=(0,i.s)(r,A),C=(0,m.jH)(s),[R,E]=(0,u.i)({prop:h,defaultProp:w??null,onChange:y,caller:g}),[I,T]=o.useState(!1),S=(0,d.c)(x),O=v(t),$=o.useRef(!1),[P,D]=o.useState(0);return o.useEffect(()=>{let e=A.current;if(e)return e.addEventListener(f,S),()=>e.removeEventListener(f,S)},[S]),(0,p.jsx)(k,{scope:t,orientation:a,dir:C,loop:l,currentTabStopId:R,onItemFocus:o.useCallback(e=>E(e),[E]),onItemShiftTab:o.useCallback(()=>T(!0),[]),onFocusableItemAdd:o.useCallback(()=>D(e=>e+1),[]),onFocusableItemRemove:o.useCallback(()=>D(e=>e-1),[]),children:(0,p.jsx)(c.sG.div,{tabIndex:I||0===P?-1:0,"data-orientation":a,...z,ref:j,style:{outline:"none",...e.style},onMouseDown:(0,n.m)(e.onMouseDown,()=>{$.current=!0}),onFocus:(0,n.m)(e.onFocus,e=>{let r=!$.current;if(e.target===e.currentTarget&&r&&!I){let r=new CustomEvent(f,b);if(e.currentTarget.dispatchEvent(r),!r.defaultPrevented){let e=O().filter(e=>e.focusable);M([e.find(e=>e.active),e.find(e=>e.id===R),...e].filter(Boolean).map(e=>e.ref.current),N)}}$.current=!1}),onBlur:(0,n.m)(e.onBlur,()=>T(!1))})})}),j="RovingFocusGroupItem",C=o.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:t,focusable:a=!0,active:l=!1,tabStopId:i,children:d,...u}=e,m=(0,s.B)(),f=i||m,b=N(j,t),g=b.currentTabStopId===f,w=v(t),{onFocusableItemAdd:y,onFocusableItemRemove:x,currentTabStopId:k}=b;return o.useEffect(()=>{if(a)return y(),()=>x()},[a,y,x]),(0,p.jsx)(h.ItemSlot,{scope:t,id:f,focusable:a,active:l,children:(0,p.jsx)(c.sG.span,{tabIndex:g?0:-1,"data-orientation":b.orientation,...u,ref:r,onMouseDown:(0,n.m)(e.onMouseDown,e=>{a?b.onItemFocus(f):e.preventDefault()}),onFocus:(0,n.m)(e.onFocus,()=>b.onItemFocus(f)),onKeyDown:(0,n.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void b.onItemShiftTab();if(e.target!==e.currentTarget)return;let r=function(e,r,t){var o;let n=(o=e.key,"rtl"!==t?o:"ArrowLeft"===o?"ArrowRight":"ArrowRight"===o?"ArrowLeft":o);if(!("vertical"===r&&["ArrowLeft","ArrowRight"].includes(n))&&!("horizontal"===r&&["ArrowUp","ArrowDown"].includes(n)))return R[n]}(e,b.orientation,b.dir);if(void 0!==r){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let t=w().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===r)t.reverse();else if("prev"===r||"next"===r){"prev"===r&&t.reverse();let o=t.indexOf(e.currentTarget);t=b.loop?function(e,r){return e.map((t,o)=>e[(r+o)%e.length])}(t,o+1):t.slice(o+1)}setTimeout(()=>M(t))}}),children:"function"==typeof d?d({isCurrentTabStop:g,hasTabStop:null!=k}):d})})});C.displayName=j;var R={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function M(e,r=!1){let t=document.activeElement;for(let o of e)if(o===t||(o.focus({preventScroll:r}),document.activeElement!==t))return}var E=t(66156),I=e=>{let{present:r,children:t}=e,n=function(e){var r,t;let[n,a]=o.useState(),l=o.useRef(null),i=o.useRef(e),s=o.useRef("none"),[c,d]=(r=e?"mounted":"unmounted",t={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},o.useReducer((e,r)=>t[e][r]??e,r));return o.useEffect(()=>{let e=T(l.current);s.current="mounted"===c?e:"none"},[c]),(0,E.N)(()=>{let r=l.current,t=i.current;if(t!==e){let o=s.current,n=T(r);e?d("MOUNT"):"none"===n||r?.display==="none"?d("UNMOUNT"):t&&o!==n?d("ANIMATION_OUT"):d("UNMOUNT"),i.current=e}},[e,d]),(0,E.N)(()=>{if(n){let e,r=n.ownerDocument.defaultView??window,t=t=>{let o=T(l.current).includes(t.animationName);if(t.target===n&&o&&(d("ANIMATION_END"),!i.current)){let t=n.style.animationFillMode;n.style.animationFillMode="forwards",e=r.setTimeout(()=>{"forwards"===n.style.animationFillMode&&(n.style.animationFillMode=t)})}},o=e=>{e.target===n&&(s.current=T(l.current))};return n.addEventListener("animationstart",o),n.addEventListener("animationcancel",t),n.addEventListener("animationend",t),()=>{r.clearTimeout(e),n.removeEventListener("animationstart",o),n.removeEventListener("animationcancel",t),n.removeEventListener("animationend",t)}}d("ANIMATION_END")},[n,d]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:o.useCallback(e=>{l.current=e?getComputedStyle(e):null,a(e)},[])}}(r),a="function"==typeof t?t({present:n.isPresent}):o.Children.only(t),l=(0,i.s)(n.ref,function(e){let r=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,t=r&&"isReactWarning"in r&&r.isReactWarning;return t?e.ref:(t=(r=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(a));return"function"==typeof t||n.isPresent?o.cloneElement(a,{ref:l}):null};function T(e){return e?.animationName||"none"}I.displayName="Presence";var S="Tabs",[O,$]=(0,a.A)(S,[x]),P=x(),[D,F]=O(S),L=o.forwardRef((e,r)=>{let{__scopeTabs:t,value:o,onValueChange:n,defaultValue:a,orientation:l="horizontal",dir:i,activationMode:d="automatic",...f}=e,b=(0,m.jH)(i),[g,h]=(0,u.i)({prop:o,onChange:n,defaultProp:a??"",caller:S});return(0,p.jsx)(D,{scope:t,baseId:(0,s.B)(),value:g,onValueChange:h,orientation:l,dir:b,activationMode:d,children:(0,p.jsx)(c.sG.div,{dir:b,"data-orientation":l,...f,ref:r})})});L.displayName=S;var _="TabsList",G=o.forwardRef((e,r)=>{let{__scopeTabs:t,loop:o=!0,...n}=e,a=F(_,t),l=P(t);return(0,p.jsx)(z,{asChild:!0,...l,orientation:a.orientation,dir:a.dir,loop:o,children:(0,p.jsx)(c.sG.div,{role:"tablist","aria-orientation":a.orientation,...n,ref:r})})});G.displayName=_;var U="TabsTrigger",W=o.forwardRef((e,r)=>{let{__scopeTabs:t,value:o,disabled:a=!1,...l}=e,i=F(U,t),s=P(t),d=B(i.baseId,o),u=q(i.baseId,o),m=o===i.value;return(0,p.jsx)(C,{asChild:!0,...s,focusable:!a,active:m,children:(0,p.jsx)(c.sG.button,{type:"button",role:"tab","aria-selected":m,"aria-controls":u,"data-state":m?"active":"inactive","data-disabled":a?"":void 0,disabled:a,id:d,...l,ref:r,onMouseDown:(0,n.m)(e.onMouseDown,e=>{a||0!==e.button||!1!==e.ctrlKey?e.preventDefault():i.onValueChange(o)}),onKeyDown:(0,n.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&i.onValueChange(o)}),onFocus:(0,n.m)(e.onFocus,()=>{let e="manual"!==i.activationMode;m||a||!e||i.onValueChange(o)})})})});W.displayName=U;var K="TabsContent",V=o.forwardRef((e,r)=>{let{__scopeTabs:t,value:n,forceMount:a,children:l,...i}=e,s=F(K,t),d=B(s.baseId,n),u=q(s.baseId,n),m=n===s.value,f=o.useRef(m);return o.useEffect(()=>{let e=requestAnimationFrame(()=>f.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,p.jsx)(I,{present:a||m,children:({present:t})=>(0,p.jsx)(c.sG.div,{"data-state":m?"active":"inactive","data-orientation":s.orientation,role:"tabpanel","aria-labelledby":d,hidden:!t,id:u,tabIndex:0,...i,ref:r,style:{...e.style,animationDuration:f.current?"0s":void 0},children:t&&l})})});function B(e,r){return`${e}-trigger-${r}`}function q(e,r){return`${e}-content-${r}`}V.displayName=K;var H=L,Z=G,X=W,Q=V},62688:(e,r,t)=>{t.d(r,{A:()=>u});var o=t(43210);let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase()),l=e=>{let r=a(e);return r.charAt(0).toUpperCase()+r.slice(1)},i=(...e)=>e.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim(),s=e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,o.forwardRef)(({color:e="currentColor",size:r=24,strokeWidth:t=2,absoluteStrokeWidth:n,className:a="",children:l,iconNode:d,...u},m)=>(0,o.createElement)("svg",{ref:m,...c,width:r,height:r,stroke:e,strokeWidth:n?24*Number(t)/Number(r):t,className:i("lucide",a),...!l&&!s(u)&&{"aria-hidden":"true"},...u},[...d.map(([e,r])=>(0,o.createElement)(e,r)),...Array.isArray(l)?l:[l]])),u=(e,r)=>{let t=(0,o.forwardRef)(({className:t,...a},s)=>(0,o.createElement)(d,{ref:s,iconNode:r,className:i(`lucide-${n(l(e))}`,`lucide-${e}`,t),...a}));return t.displayName=l(e),t}},65551:(e,r,t)=>{t.d(r,{i:()=>i});var o,n=t(43210),a=t(66156),l=(o||(o=t.t(n,2)))[" useInsertionEffect ".trim().toString()]||a.N;function i({prop:e,defaultProp:r,onChange:t=()=>{},caller:o}){let[a,i,s]=function({defaultProp:e,onChange:r}){let[t,o]=n.useState(e),a=n.useRef(t),i=n.useRef(r);return l(()=>{i.current=r},[r]),n.useEffect(()=>{a.current!==t&&(i.current?.(t),a.current=t)},[t,a]),[t,o,i]}({defaultProp:r,onChange:t}),c=void 0!==e,d=c?e:a;{let r=n.useRef(void 0!==e);n.useEffect(()=>{let e=r.current;if(e!==c){let r=c?"controlled":"uncontrolled";console.warn(`${o} is changing from ${e?"controlled":"uncontrolled"} to ${r}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}r.current=c},[c,o])}return[d,n.useCallback(r=>{if(c){let t="function"==typeof r?r(e):r;t!==e&&s.current?.(t)}else i(r)},[c,e,i,s])]}Symbol("RADIX:SYNC_STATE")},66156:(e,r,t)=>{t.d(r,{N:()=>n});var o=t(43210),n=globalThis?.document?o.useLayoutEffect:()=>{}},70569:(e,r,t)=>{t.d(r,{m:()=>o});function o(e,r,{checkForDefaultPrevented:t=!0}={}){return function(o){if(e?.(o),!1===t||!o.defaultPrevented)return r?.(o)}}},82348:(e,r,t)=>{t.d(r,{QP:()=>ec});let o=e=>{let r=i(e),{conflictingClassGroups:t,conflictingClassGroupModifiers:o}=e;return{getClassGroupId:e=>{let t=e.split("-");return""===t[0]&&1!==t.length&&t.shift(),n(t,r)||l(e)},getConflictingClassGroupIds:(e,r)=>{let n=t[e]||[];return r&&o[e]?[...n,...o[e]]:n}}},n=(e,r)=>{if(0===e.length)return r.classGroupId;let t=e[0],o=r.nextPart.get(t),a=o?n(e.slice(1),o):void 0;if(a)return a;if(0===r.validators.length)return;let l=e.join("-");return r.validators.find(({validator:e})=>e(l))?.classGroupId},a=/^\[(.+)\]$/,l=e=>{if(a.test(e)){let r=a.exec(e)[1],t=r?.substring(0,r.indexOf(":"));if(t)return"arbitrary.."+t}},i=e=>{let{theme:r,classGroups:t}=e,o={nextPart:new Map,validators:[]};for(let e in t)s(t[e],o,e,r);return o},s=(e,r,t,o)=>{e.forEach(e=>{if("string"==typeof e){(""===e?r:c(r,e)).classGroupId=t;return}if("function"==typeof e)return d(e)?void s(e(o),r,t,o):void r.validators.push({validator:e,classGroupId:t});Object.entries(e).forEach(([e,n])=>{s(n,c(r,e),t,o)})})},c=(e,r)=>{let t=e;return r.split("-").forEach(e=>{t.nextPart.has(e)||t.nextPart.set(e,{nextPart:new Map,validators:[]}),t=t.nextPart.get(e)}),t},d=e=>e.isThemeGetter,u=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let r=0,t=new Map,o=new Map,n=(n,a)=>{t.set(n,a),++r>e&&(r=0,o=t,t=new Map)};return{get(e){let r=t.get(e);return void 0!==r?r:void 0!==(r=o.get(e))?(n(e,r),r):void 0},set(e,r){t.has(e)?t.set(e,r):n(e,r)}}},m=e=>{let{prefix:r,experimentalParseClassName:t}=e,o=e=>{let r,t=[],o=0,n=0,a=0;for(let l=0;l<e.length;l++){let i=e[l];if(0===o&&0===n){if(":"===i){t.push(e.slice(a,l)),a=l+1;continue}if("/"===i){r=l;continue}}"["===i?o++:"]"===i?o--:"("===i?n++:")"===i&&n--}let l=0===t.length?e:e.substring(a),i=p(l);return{modifiers:t,hasImportantModifier:i!==l,baseClassName:i,maybePostfixModifierPosition:r&&r>a?r-a:void 0}};if(r){let e=r+":",t=o;o=r=>r.startsWith(e)?t(r.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:r,maybePostfixModifierPosition:void 0}}if(t){let e=o;o=r=>t({className:r,parseClassName:e})}return o},p=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,f=e=>{let r=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let t=[],o=[];return e.forEach(e=>{"["===e[0]||r[e]?(t.push(...o.sort(),e),o=[]):o.push(e)}),t.push(...o.sort()),t}},b=e=>({cache:u(e.cacheSize),parseClassName:m(e),sortModifiers:f(e),...o(e)}),g=/\s+/,h=(e,r)=>{let{parseClassName:t,getClassGroupId:o,getConflictingClassGroupIds:n,sortModifiers:a}=r,l=[],i=e.trim().split(g),s="";for(let e=i.length-1;e>=0;e-=1){let r=i[e],{isExternal:c,modifiers:d,hasImportantModifier:u,baseClassName:m,maybePostfixModifierPosition:p}=t(r);if(c){s=r+(s.length>0?" "+s:s);continue}let f=!!p,b=o(f?m.substring(0,p):m);if(!b){if(!f||!(b=o(m))){s=r+(s.length>0?" "+s:s);continue}f=!1}let g=a(d).join(":"),h=u?g+"!":g,v=h+b;if(l.includes(v))continue;l.push(v);let w=n(b,f);for(let e=0;e<w.length;++e){let r=w[e];l.push(h+r)}s=r+(s.length>0?" "+s:s)}return s};function v(){let e,r,t=0,o="";for(;t<arguments.length;)(e=arguments[t++])&&(r=w(e))&&(o&&(o+=" "),o+=r);return o}let w=e=>{let r;if("string"==typeof e)return e;let t="";for(let o=0;o<e.length;o++)e[o]&&(r=w(e[o]))&&(t&&(t+=" "),t+=r);return t},y=e=>{let r=r=>r[e]||[];return r.isThemeGetter=!0,r},x=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,k=/^\((?:(\w[\w-]*):)?(.+)\)$/i,N=/^\d+\/\d+$/,z=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,A=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,j=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,C=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,R=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,M=e=>N.test(e),E=e=>!!e&&!Number.isNaN(Number(e)),I=e=>!!e&&Number.isInteger(Number(e)),T=e=>e.endsWith("%")&&E(e.slice(0,-1)),S=e=>z.test(e),O=()=>!0,$=e=>A.test(e)&&!j.test(e),P=()=>!1,D=e=>C.test(e),F=e=>R.test(e),L=e=>!G(e)&&!q(e),_=e=>ee(e,en,P),G=e=>x.test(e),U=e=>ee(e,ea,$),W=e=>ee(e,el,E),K=e=>ee(e,et,P),V=e=>ee(e,eo,F),B=e=>ee(e,es,D),q=e=>k.test(e),H=e=>er(e,ea),Z=e=>er(e,ei),X=e=>er(e,et),Q=e=>er(e,en),Y=e=>er(e,eo),J=e=>er(e,es,!0),ee=(e,r,t)=>{let o=x.exec(e);return!!o&&(o[1]?r(o[1]):t(o[2]))},er=(e,r,t=!1)=>{let o=k.exec(e);return!!o&&(o[1]?r(o[1]):t)},et=e=>"position"===e||"percentage"===e,eo=e=>"image"===e||"url"===e,en=e=>"length"===e||"size"===e||"bg-size"===e,ea=e=>"length"===e,el=e=>"number"===e,ei=e=>"family-name"===e,es=e=>"shadow"===e;Symbol.toStringTag;let ec=function(e,...r){let t,o,n,a=function(i){return o=(t=b(r.reduce((e,r)=>r(e),e()))).cache.get,n=t.cache.set,a=l,l(i)};function l(e){let r=o(e);if(r)return r;let a=h(e,t);return n(e,a),a}return function(){return a(v.apply(null,arguments))}}(()=>{let e=y("color"),r=y("font"),t=y("text"),o=y("font-weight"),n=y("tracking"),a=y("leading"),l=y("breakpoint"),i=y("container"),s=y("spacing"),c=y("radius"),d=y("shadow"),u=y("inset-shadow"),m=y("text-shadow"),p=y("drop-shadow"),f=y("blur"),b=y("perspective"),g=y("aspect"),h=y("ease"),v=y("animate"),w=()=>["auto","avoid","all","avoid-page","page","left","right","column"],x=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],k=()=>[...x(),q,G],N=()=>["auto","hidden","clip","visible","scroll"],z=()=>["auto","contain","none"],A=()=>[q,G,s],j=()=>[M,"full","auto",...A()],C=()=>[I,"none","subgrid",q,G],R=()=>["auto",{span:["full",I,q,G]},I,q,G],$=()=>[I,"auto",q,G],P=()=>["auto","min","max","fr",q,G],D=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],F=()=>["start","end","center","stretch","center-safe","end-safe"],ee=()=>["auto",...A()],er=()=>[M,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...A()],et=()=>[e,q,G],eo=()=>[...x(),X,K,{position:[q,G]}],en=()=>["no-repeat",{repeat:["","x","y","space","round"]}],ea=()=>["auto","cover","contain",Q,_,{size:[q,G]}],el=()=>[T,H,U],ei=()=>["","none","full",c,q,G],es=()=>["",E,H,U],ec=()=>["solid","dashed","dotted","double"],ed=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],eu=()=>[E,T,X,K],em=()=>["","none",f,q,G],ep=()=>["none",E,q,G],ef=()=>["none",E,q,G],eb=()=>[E,q,G],eg=()=>[M,"full",...A()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[S],breakpoint:[S],color:[O],container:[S],"drop-shadow":[S],ease:["in","out","in-out"],font:[L],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[S],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[S],shadow:[S],spacing:["px",E],text:[S],"text-shadow":[S],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",M,G,q,g]}],container:["container"],columns:[{columns:[E,G,q,i]}],"break-after":[{"break-after":w()}],"break-before":[{"break-before":w()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:k()}],overflow:[{overflow:N()}],"overflow-x":[{"overflow-x":N()}],"overflow-y":[{"overflow-y":N()}],overscroll:[{overscroll:z()}],"overscroll-x":[{"overscroll-x":z()}],"overscroll-y":[{"overscroll-y":z()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:j()}],"inset-x":[{"inset-x":j()}],"inset-y":[{"inset-y":j()}],start:[{start:j()}],end:[{end:j()}],top:[{top:j()}],right:[{right:j()}],bottom:[{bottom:j()}],left:[{left:j()}],visibility:["visible","invisible","collapse"],z:[{z:[I,"auto",q,G]}],basis:[{basis:[M,"full","auto",i,...A()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[E,M,"auto","initial","none",G]}],grow:[{grow:["",E,q,G]}],shrink:[{shrink:["",E,q,G]}],order:[{order:[I,"first","last","none",q,G]}],"grid-cols":[{"grid-cols":C()}],"col-start-end":[{col:R()}],"col-start":[{"col-start":$()}],"col-end":[{"col-end":$()}],"grid-rows":[{"grid-rows":C()}],"row-start-end":[{row:R()}],"row-start":[{"row-start":$()}],"row-end":[{"row-end":$()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":P()}],"auto-rows":[{"auto-rows":P()}],gap:[{gap:A()}],"gap-x":[{"gap-x":A()}],"gap-y":[{"gap-y":A()}],"justify-content":[{justify:[...D(),"normal"]}],"justify-items":[{"justify-items":[...F(),"normal"]}],"justify-self":[{"justify-self":["auto",...F()]}],"align-content":[{content:["normal",...D()]}],"align-items":[{items:[...F(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...F(),{baseline:["","last"]}]}],"place-content":[{"place-content":D()}],"place-items":[{"place-items":[...F(),"baseline"]}],"place-self":[{"place-self":["auto",...F()]}],p:[{p:A()}],px:[{px:A()}],py:[{py:A()}],ps:[{ps:A()}],pe:[{pe:A()}],pt:[{pt:A()}],pr:[{pr:A()}],pb:[{pb:A()}],pl:[{pl:A()}],m:[{m:ee()}],mx:[{mx:ee()}],my:[{my:ee()}],ms:[{ms:ee()}],me:[{me:ee()}],mt:[{mt:ee()}],mr:[{mr:ee()}],mb:[{mb:ee()}],ml:[{ml:ee()}],"space-x":[{"space-x":A()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":A()}],"space-y-reverse":["space-y-reverse"],size:[{size:er()}],w:[{w:[i,"screen",...er()]}],"min-w":[{"min-w":[i,"screen","none",...er()]}],"max-w":[{"max-w":[i,"screen","none","prose",{screen:[l]},...er()]}],h:[{h:["screen","lh",...er()]}],"min-h":[{"min-h":["screen","lh","none",...er()]}],"max-h":[{"max-h":["screen","lh",...er()]}],"font-size":[{text:["base",t,H,U]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[o,q,W]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",T,G]}],"font-family":[{font:[Z,G,r]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[n,q,G]}],"line-clamp":[{"line-clamp":[E,"none",q,W]}],leading:[{leading:[a,...A()]}],"list-image":[{"list-image":["none",q,G]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",q,G]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:et()}],"text-color":[{text:et()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ec(),"wavy"]}],"text-decoration-thickness":[{decoration:[E,"from-font","auto",q,U]}],"text-decoration-color":[{decoration:et()}],"underline-offset":[{"underline-offset":[E,"auto",q,G]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:A()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",q,G]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",q,G]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:eo()}],"bg-repeat":[{bg:en()}],"bg-size":[{bg:ea()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},I,q,G],radial:["",q,G],conic:[I,q,G]},Y,V]}],"bg-color":[{bg:et()}],"gradient-from-pos":[{from:el()}],"gradient-via-pos":[{via:el()}],"gradient-to-pos":[{to:el()}],"gradient-from":[{from:et()}],"gradient-via":[{via:et()}],"gradient-to":[{to:et()}],rounded:[{rounded:ei()}],"rounded-s":[{"rounded-s":ei()}],"rounded-e":[{"rounded-e":ei()}],"rounded-t":[{"rounded-t":ei()}],"rounded-r":[{"rounded-r":ei()}],"rounded-b":[{"rounded-b":ei()}],"rounded-l":[{"rounded-l":ei()}],"rounded-ss":[{"rounded-ss":ei()}],"rounded-se":[{"rounded-se":ei()}],"rounded-ee":[{"rounded-ee":ei()}],"rounded-es":[{"rounded-es":ei()}],"rounded-tl":[{"rounded-tl":ei()}],"rounded-tr":[{"rounded-tr":ei()}],"rounded-br":[{"rounded-br":ei()}],"rounded-bl":[{"rounded-bl":ei()}],"border-w":[{border:es()}],"border-w-x":[{"border-x":es()}],"border-w-y":[{"border-y":es()}],"border-w-s":[{"border-s":es()}],"border-w-e":[{"border-e":es()}],"border-w-t":[{"border-t":es()}],"border-w-r":[{"border-r":es()}],"border-w-b":[{"border-b":es()}],"border-w-l":[{"border-l":es()}],"divide-x":[{"divide-x":es()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":es()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ec(),"hidden","none"]}],"divide-style":[{divide:[...ec(),"hidden","none"]}],"border-color":[{border:et()}],"border-color-x":[{"border-x":et()}],"border-color-y":[{"border-y":et()}],"border-color-s":[{"border-s":et()}],"border-color-e":[{"border-e":et()}],"border-color-t":[{"border-t":et()}],"border-color-r":[{"border-r":et()}],"border-color-b":[{"border-b":et()}],"border-color-l":[{"border-l":et()}],"divide-color":[{divide:et()}],"outline-style":[{outline:[...ec(),"none","hidden"]}],"outline-offset":[{"outline-offset":[E,q,G]}],"outline-w":[{outline:["",E,H,U]}],"outline-color":[{outline:et()}],shadow:[{shadow:["","none",d,J,B]}],"shadow-color":[{shadow:et()}],"inset-shadow":[{"inset-shadow":["none",u,J,B]}],"inset-shadow-color":[{"inset-shadow":et()}],"ring-w":[{ring:es()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:et()}],"ring-offset-w":[{"ring-offset":[E,U]}],"ring-offset-color":[{"ring-offset":et()}],"inset-ring-w":[{"inset-ring":es()}],"inset-ring-color":[{"inset-ring":et()}],"text-shadow":[{"text-shadow":["none",m,J,B]}],"text-shadow-color":[{"text-shadow":et()}],opacity:[{opacity:[E,q,G]}],"mix-blend":[{"mix-blend":[...ed(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ed()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[E]}],"mask-image-linear-from-pos":[{"mask-linear-from":eu()}],"mask-image-linear-to-pos":[{"mask-linear-to":eu()}],"mask-image-linear-from-color":[{"mask-linear-from":et()}],"mask-image-linear-to-color":[{"mask-linear-to":et()}],"mask-image-t-from-pos":[{"mask-t-from":eu()}],"mask-image-t-to-pos":[{"mask-t-to":eu()}],"mask-image-t-from-color":[{"mask-t-from":et()}],"mask-image-t-to-color":[{"mask-t-to":et()}],"mask-image-r-from-pos":[{"mask-r-from":eu()}],"mask-image-r-to-pos":[{"mask-r-to":eu()}],"mask-image-r-from-color":[{"mask-r-from":et()}],"mask-image-r-to-color":[{"mask-r-to":et()}],"mask-image-b-from-pos":[{"mask-b-from":eu()}],"mask-image-b-to-pos":[{"mask-b-to":eu()}],"mask-image-b-from-color":[{"mask-b-from":et()}],"mask-image-b-to-color":[{"mask-b-to":et()}],"mask-image-l-from-pos":[{"mask-l-from":eu()}],"mask-image-l-to-pos":[{"mask-l-to":eu()}],"mask-image-l-from-color":[{"mask-l-from":et()}],"mask-image-l-to-color":[{"mask-l-to":et()}],"mask-image-x-from-pos":[{"mask-x-from":eu()}],"mask-image-x-to-pos":[{"mask-x-to":eu()}],"mask-image-x-from-color":[{"mask-x-from":et()}],"mask-image-x-to-color":[{"mask-x-to":et()}],"mask-image-y-from-pos":[{"mask-y-from":eu()}],"mask-image-y-to-pos":[{"mask-y-to":eu()}],"mask-image-y-from-color":[{"mask-y-from":et()}],"mask-image-y-to-color":[{"mask-y-to":et()}],"mask-image-radial":[{"mask-radial":[q,G]}],"mask-image-radial-from-pos":[{"mask-radial-from":eu()}],"mask-image-radial-to-pos":[{"mask-radial-to":eu()}],"mask-image-radial-from-color":[{"mask-radial-from":et()}],"mask-image-radial-to-color":[{"mask-radial-to":et()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":x()}],"mask-image-conic-pos":[{"mask-conic":[E]}],"mask-image-conic-from-pos":[{"mask-conic-from":eu()}],"mask-image-conic-to-pos":[{"mask-conic-to":eu()}],"mask-image-conic-from-color":[{"mask-conic-from":et()}],"mask-image-conic-to-color":[{"mask-conic-to":et()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:eo()}],"mask-repeat":[{mask:en()}],"mask-size":[{mask:ea()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",q,G]}],filter:[{filter:["","none",q,G]}],blur:[{blur:em()}],brightness:[{brightness:[E,q,G]}],contrast:[{contrast:[E,q,G]}],"drop-shadow":[{"drop-shadow":["","none",p,J,B]}],"drop-shadow-color":[{"drop-shadow":et()}],grayscale:[{grayscale:["",E,q,G]}],"hue-rotate":[{"hue-rotate":[E,q,G]}],invert:[{invert:["",E,q,G]}],saturate:[{saturate:[E,q,G]}],sepia:[{sepia:["",E,q,G]}],"backdrop-filter":[{"backdrop-filter":["","none",q,G]}],"backdrop-blur":[{"backdrop-blur":em()}],"backdrop-brightness":[{"backdrop-brightness":[E,q,G]}],"backdrop-contrast":[{"backdrop-contrast":[E,q,G]}],"backdrop-grayscale":[{"backdrop-grayscale":["",E,q,G]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[E,q,G]}],"backdrop-invert":[{"backdrop-invert":["",E,q,G]}],"backdrop-opacity":[{"backdrop-opacity":[E,q,G]}],"backdrop-saturate":[{"backdrop-saturate":[E,q,G]}],"backdrop-sepia":[{"backdrop-sepia":["",E,q,G]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":A()}],"border-spacing-x":[{"border-spacing-x":A()}],"border-spacing-y":[{"border-spacing-y":A()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",q,G]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[E,"initial",q,G]}],ease:[{ease:["linear","initial",h,q,G]}],delay:[{delay:[E,q,G]}],animate:[{animate:["none",v,q,G]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[b,q,G]}],"perspective-origin":[{"perspective-origin":k()}],rotate:[{rotate:ep()}],"rotate-x":[{"rotate-x":ep()}],"rotate-y":[{"rotate-y":ep()}],"rotate-z":[{"rotate-z":ep()}],scale:[{scale:ef()}],"scale-x":[{"scale-x":ef()}],"scale-y":[{"scale-y":ef()}],"scale-z":[{"scale-z":ef()}],"scale-3d":["scale-3d"],skew:[{skew:eb()}],"skew-x":[{"skew-x":eb()}],"skew-y":[{"skew-y":eb()}],transform:[{transform:[q,G,"","none","gpu","cpu"]}],"transform-origin":[{origin:k()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:eg()}],"translate-x":[{"translate-x":eg()}],"translate-y":[{"translate-y":eg()}],"translate-z":[{"translate-z":eg()}],"translate-none":["translate-none"],accent:[{accent:et()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:et()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",q,G]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":A()}],"scroll-mx":[{"scroll-mx":A()}],"scroll-my":[{"scroll-my":A()}],"scroll-ms":[{"scroll-ms":A()}],"scroll-me":[{"scroll-me":A()}],"scroll-mt":[{"scroll-mt":A()}],"scroll-mr":[{"scroll-mr":A()}],"scroll-mb":[{"scroll-mb":A()}],"scroll-ml":[{"scroll-ml":A()}],"scroll-p":[{"scroll-p":A()}],"scroll-px":[{"scroll-px":A()}],"scroll-py":[{"scroll-py":A()}],"scroll-ps":[{"scroll-ps":A()}],"scroll-pe":[{"scroll-pe":A()}],"scroll-pt":[{"scroll-pt":A()}],"scroll-pr":[{"scroll-pr":A()}],"scroll-pb":[{"scroll-pb":A()}],"scroll-pl":[{"scroll-pl":A()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",q,G]}],fill:[{fill:["none",...et()]}],"stroke-w":[{stroke:[E,H,U,W]}],stroke:[{stroke:["none",...et()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})},96963:(e,r,t)=>{t.d(r,{B:()=>s});var o,n=t(43210),a=t(66156),l=(o||(o=t.t(n,2)))[" useId ".trim().toString()]||(()=>void 0),i=0;function s(e){let[r,t]=n.useState(l());return(0,a.N)(()=>{e||t(e=>e??String(i++))},[e]),e||(r?`radix-${r}`:"")}},97840:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(62688).A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])},98599:(e,r,t)=>{t.d(r,{s:()=>l,t:()=>a});var o=t(43210);function n(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}function a(...e){return r=>{let t=!1,o=e.map(e=>{let o=n(e,r);return t||"function"!=typeof o||(t=!0),o});if(t)return()=>{for(let r=0;r<o.length;r++){let t=o[r];"function"==typeof t?t():n(e[r],null)}}}}function l(...e){return o.useCallback(a(...e),e)}}};