(()=>{var $;function U(B){return U=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(G){return typeof G}:function(G){return G&&typeof Symbol=="function"&&G.constructor===Symbol&&G!==Symbol.prototype?"symbol":typeof G},U(B)}function K(B,G){var C=Object.keys(B);if(Object.getOwnPropertySymbols){var H=Object.getOwnPropertySymbols(B);G&&(H=H.filter(function(J){return Object.getOwnPropertyDescriptor(B,J).enumerable})),C.push.apply(C,H)}return C}function Q(B){for(var G=1;G<arguments.length;G++){var C=arguments[G]!=null?arguments[G]:{};G%2?K(Object(C),!0).forEach(function(H){x(B,H,C[H])}):Object.getOwnPropertyDescriptors?Object.defineProperties(B,Object.getOwnPropertyDescriptors(C)):K(Object(C)).forEach(function(H){Object.defineProperty(B,H,Object.getOwnPropertyDescriptor(C,H))})}return B}function x(B,G,C){if(G=N(G),G in B)Object.defineProperty(B,G,{value:C,enumerable:!0,configurable:!0,writable:!0});else B[G]=C;return B}function N(B){var G=z(B,"string");return U(G)=="symbol"?G:String(G)}function z(B,G){if(U(B)!="object"||!B)return B;var C=B[Symbol.toPrimitive];if(C!==void 0){var H=C.call(B,G||"default");if(U(H)!="object")return H;throw new TypeError("@@toPrimitive must return a primitive value.")}return(G==="string"?String:Number)(B)}var W=Object.defineProperty,GB=function B(G,C){for(var H in C)W(G,H,{get:C[H],enumerable:!0,configurable:!0,set:function J(X){return C[H]=function(){return X}}})},D={lessThanXSeconds:{one:"nas lugha na diog",other:"nas lugha na {{count}} diogan"},xSeconds:{one:"1 diog",two:"2 dhiog",twenty:"20 diog",other:"{{count}} diogan"},halfAMinute:"leth mhionaid",lessThanXMinutes:{one:"nas lugha na mionaid",other:"nas lugha na {{count}} mionaidean"},xMinutes:{one:"1 mionaid",two:"2 mhionaid",twenty:"20 mionaid",other:"{{count}} mionaidean"},aboutXHours:{one:"mu uair de th\xECde",other:"mu {{count}} uairean de th\xECde"},xHours:{one:"1 uair de th\xECde",two:"2 uair de th\xECde",twenty:"20 uair de th\xECde",other:"{{count}} uairean de th\xECde"},xDays:{one:"1 l\xE0",other:"{{count}} l\xE0"},aboutXWeeks:{one:"mu 1 seachdain",other:"mu {{count}} seachdainean"},xWeeks:{one:"1 seachdain",other:"{{count}} seachdainean"},aboutXMonths:{one:"mu mh\xECos",other:"mu {{count}} m\xECosan"},xMonths:{one:"1 m\xECos",other:"{{count}} m\xECosan"},aboutXYears:{one:"mu bhliadhna",other:"mu {{count}} bliadhnaichean"},xYears:{one:"1 bhliadhna",other:"{{count}} bliadhna"},overXYears:{one:"c\xF2rr is bliadhna",other:"c\xF2rr is {{count}} bliadhnaichean"},almostXYears:{one:"cha mh\xF2r bliadhna",other:"cha mh\xF2r {{count}} bliadhnaichean"}},S=function B(G,C,H){var J,X=D[G];if(typeof X==="string")J=X;else if(C===1)J=X.one;else if(C===2&&!!X.two)J=X.two;else if(C===20&&!!X.twenty)J=X.twenty;else J=X.other.replace("{{count}}",String(C));if(H!==null&&H!==void 0&&H.addSuffix)if(H.comparison&&H.comparison>0)return"ann an "+J;else return"o chionn "+J;return J};function A(B){return function(){var G=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},C=G.width?String(G.width):B.defaultWidth,H=B.formats[C]||B.formats[B.defaultWidth];return H}}var M={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},R={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},L={full:"{{date}} 'aig' {{time}}",long:"{{date}} 'aig' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},V={date:A({formats:M,defaultWidth:"full"}),time:A({formats:R,defaultWidth:"full"}),dateTime:A({formats:L,defaultWidth:"full"})},j={lastWeek:"'mu dheireadh' eeee 'aig' p",yesterday:"'an-d\xE8 aig' p",today:"'an-diugh aig' p",tomorrow:"'a-m\xE0ireach aig' p",nextWeek:"eeee 'aig' p",other:"P"},w=function B(G,C,H,J){return j[G]};function I(B){return function(G,C){var H=C!==null&&C!==void 0&&C.context?String(C.context):"standalone",J;if(H==="formatting"&&B.formattingValues){var X=B.defaultFormattingWidth||B.defaultWidth,Y=C!==null&&C!==void 0&&C.width?String(C.width):X;J=B.formattingValues[Y]||B.formattingValues[X]}else{var Z=B.defaultWidth,q=C!==null&&C!==void 0&&C.width?String(C.width):B.defaultWidth;J=B.values[q]||B.values[Z]}var T=B.argumentCallback?B.argumentCallback(G):G;return J[T]}}var _={narrow:["R","A"],abbreviated:["RC","AD"],wide:["ro Chr\xECosta","anno domini"]},f={narrow:["1","2","3","4"],abbreviated:["C1","C2","C3","C4"],wide:["a' chiad chairteal","an d\xE0rna cairteal","an treas cairteal","an ceathramh cairteal"]},F={narrow:["F","G","M","G","C","\xD2","I","L","S","D","S","D"],abbreviated:["Faoi","Gear","M\xE0rt","Gibl","C\xE8it","\xD2gmh","Iuch","L\xF9n","Sult","D\xE0mh","Samh","D\xF9bh"],wide:["Am Faoilleach","An Gearran","Am M\xE0rt","An Giblean","An C\xE8itean","An t-\xD2gmhios","An t-Iuchar","An L\xF9nastal","An t-Sultain","An D\xE0mhair","An t-Samhain","An D\xF9bhlachd"]},v={narrow:["D","L","M","C","A","H","S"],short:["D\xF2","Lu","M\xE0","Ci","Ar","Ha","Sa"],abbreviated:["Did","Dil","Dim","Dic","Dia","Dih","Dis"],wide:["Did\xF2mhnaich","Diluain","Dim\xE0irt","Diciadain","Diardaoin","Dihaoine","Disathairne"]},P={narrow:{am:"m",pm:"f",midnight:"m.o.",noon:"m.l.",morning:"madainn",afternoon:"feasgar",evening:"feasgar",night:"oidhche"},abbreviated:{am:"M.",pm:"F.",midnight:"meadhan oidhche",noon:"meadhan l\xE0",morning:"madainn",afternoon:"feasgar",evening:"feasgar",night:"oidhche"},wide:{am:"m.",pm:"f.",midnight:"meadhan oidhche",noon:"meadhan l\xE0",morning:"madainn",afternoon:"feasgar",evening:"feasgar",night:"oidhche"}},k={narrow:{am:"m",pm:"f",midnight:"m.o.",noon:"m.l.",morning:"sa mhadainn",afternoon:"feasgar",evening:"feasgar",night:"air an oidhche"},abbreviated:{am:"M.",pm:"F.",midnight:"meadhan oidhche",noon:"meadhan l\xE0",morning:"sa mhadainn",afternoon:"feasgar",evening:"feasgar",night:"air an oidhche"},wide:{am:"m.",pm:"f.",midnight:"meadhan oidhche",noon:"meadhan l\xE0",morning:"sa mhadainn",afternoon:"feasgar",evening:"feasgar",night:"air an oidhche"}},h=function B(G){var C=Number(G),H=C%100;if(H>20||H<10)switch(H%10){case 1:return C+"d";case 2:return C+"na"}if(H===12)return C+"na";return C+"mh"},b={ordinalNumber:h,era:I({values:_,defaultWidth:"wide"}),quarter:I({values:f,defaultWidth:"wide",argumentCallback:function B(G){return G-1}}),month:I({values:F,defaultWidth:"wide"}),day:I({values:v,defaultWidth:"wide"}),dayPeriod:I({values:P,defaultWidth:"wide",formattingValues:k,defaultFormattingWidth:"wide"})};function O(B){return function(G){var C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},H=C.width,J=H&&B.matchPatterns[H]||B.matchPatterns[B.defaultMatchWidth],X=G.match(J);if(!X)return null;var Y=X[0],Z=H&&B.parsePatterns[H]||B.parsePatterns[B.defaultParseWidth],q=Array.isArray(Z)?m(Z,function(E){return E.test(Y)}):y(Z,function(E){return E.test(Y)}),T;T=B.valueCallback?B.valueCallback(q):q,T=C.valueCallback?C.valueCallback(T):T;var CB=G.slice(Y.length);return{value:T,rest:CB}}}function y(B,G){for(var C in B)if(Object.prototype.hasOwnProperty.call(B,C)&&G(B[C]))return C;return}function m(B,G){for(var C=0;C<B.length;C++)if(G(B[C]))return C;return}function c(B){return function(G){var C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},H=G.match(B.matchPattern);if(!H)return null;var J=H[0],X=G.match(B.parsePattern);if(!X)return null;var Y=B.valueCallback?B.valueCallback(X[0]):X[0];Y=C.valueCallback?C.valueCallback(Y):Y;var Z=G.slice(J.length);return{value:Y,rest:Z}}}var p=/^(\d+)(d|na|tr|mh)?/i,d=/\d+/i,g={narrow:/^(r|a)/i,abbreviated:/^(r\.?\s?c\.?|r\.?\s?a\.?\s?c\.?|a\.?\s?d\.?|a\.?\s?c\.?)/i,wide:/^(ro Chrìosta|ron aois choitchinn|anno domini|aois choitcheann)/i},u={any:[/^b/i,/^(a|c)/i]},l={narrow:/^[1234]/i,abbreviated:/^c[1234]/i,wide:/^[1234](cd|na|tr|mh)? cairteal/i},i={any:[/1/i,/2/i,/3/i,/4/i]},n={narrow:/^[fgmcòilsd]/i,abbreviated:/^(faoi|gear|màrt|gibl|cèit|ògmh|iuch|lùn|sult|dàmh|samh|dùbh)/i,wide:/^(am faoilleach|an gearran|am màrt|an giblean|an cèitean|an t-Ògmhios|an t-Iuchar|an lùnastal|an t-Sultain|an dàmhair|an t-Samhain|an dùbhlachd)/i},s={narrow:[/^f/i,/^g/i,/^m/i,/^g/i,/^c/i,/^ò/i,/^i/i,/^l/i,/^s/i,/^d/i,/^s/i,/^d/i],any:[/^fa/i,/^ge/i,/^mà/i,/^gi/i,/^c/i,/^ò/i,/^i/i,/^l/i,/^su/i,/^d/i,/^sa/i,/^d/i]},o={narrow:/^[dlmcahs]/i,short:/^(dò|lu|mà|ci|ar|ha|sa)/i,abbreviated:/^(did|dil|dim|dic|dia|dih|dis)/i,wide:/^(didòmhnaich|diluain|dimàirt|diciadain|diardaoin|dihaoine|disathairne)/i},r={narrow:[/^d/i,/^l/i,/^m/i,/^c/i,/^a/i,/^h/i,/^s/i],any:[/^d/i,/^l/i,/^m/i,/^c/i,/^a/i,/^h/i,/^s/i]},a={narrow:/^(a|p|mi|n|(san|aig) (madainn|feasgar|feasgar|oidhche))/i,any:/^([ap]\.?\s?m\.?|meadhan oidhche|meadhan là|(san|aig) (madainn|feasgar|feasgar|oidhche))/i},e={any:{am:/^m/i,pm:/^f/i,midnight:/^meadhan oidhche/i,noon:/^meadhan là/i,morning:/sa mhadainn/i,afternoon:/feasgar/i,evening:/feasgar/i,night:/air an oidhche/i}},t={ordinalNumber:c({matchPattern:p,parsePattern:d,valueCallback:function B(G){return parseInt(G,10)}}),era:O({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:u,defaultParseWidth:"any"}),quarter:O({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any",valueCallback:function B(G){return G+1}}),month:O({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),day:O({matchPatterns:o,defaultMatchWidth:"wide",parsePatterns:r,defaultParseWidth:"any"}),dayPeriod:O({matchPatterns:a,defaultMatchWidth:"any",parsePatterns:e,defaultParseWidth:"any"})},BB={code:"gd",formatDistance:S,formatLong:V,formatRelative:w,localize:b,match:t,options:{weekStartsOn:0,firstWeekContainsDate:1}};window.dateFns=Q(Q({},window.dateFns),{},{locale:Q(Q({},($=window.dateFns)===null||$===void 0?void 0:$.locale),{},{gd:BB})})})();

//# debugId=CECF3C6888148D1164756E2164756E21
