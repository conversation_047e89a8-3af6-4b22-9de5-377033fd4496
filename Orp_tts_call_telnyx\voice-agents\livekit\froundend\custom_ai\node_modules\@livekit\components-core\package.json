{"name": "@livekit/components-core", "version": "0.12.1", "license": "Apache-2.0", "author": "LiveKit", "repository": {"type": "git", "url": "https://github.com/livekit/components-js.git", "directory": "/packages/core"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "sideEffects": false, "files": ["dist", "src"], "typings": "dist/index.d.ts", "dependencies": {"@floating-ui/dom": "1.6.11", "loglevel": "1.9.1", "rxjs": "7.8.1"}, "peerDependencies": {"livekit-client": "^2.8.1", "tslib": "^2.6.2"}, "devDependencies": {"@livekit/protocol": "^1.23.0", "@microsoft/api-extractor": "^7.36.0", "@size-limit/file": "^11.0.2", "@size-limit/webpack": "^11.0.2", "size-limit": "^11.0.2", "tsup": "^8.0.0", "typescript": "5.6.2", "vitest": "^2.0.0", "@livekit/components-styles": "1.1.4", "eslint-config-lk-custom": "0.1.3"}, "engines": {"node": ">=18"}, "publishConfig": {"access": "public"}, "scripts": {"build": "tsup --onSuccess \"tsc --declaration --emitDeclarationOnly\"", "dev": "tsup --watch --onSuccess \"tsc --declaration --emitDeclarationOnly\"", "lint": "eslint -f unix \"src/**/*.{ts,tsx}\"", "test": "vitest --run", "test:watch": "vitest", "size": "size-limit", "api-check": "echo \"🚧 api-checks for core are disabled!\" #TODO cleanup core api then activate api checks. api-extractor run --typescript-compiler-folder ../../node_modules/typescript", "api-extractor": "api-extractor run --local --typescript-compiler-folder ../../node_modules/typescript --verbose"}}