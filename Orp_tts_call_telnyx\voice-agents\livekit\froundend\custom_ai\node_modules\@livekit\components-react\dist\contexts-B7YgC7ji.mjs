import { RoomEvent as b, ParticipantEvent as y, setLogLevel as Nn, LogLevel as pt, setLogExtension as $n, Track as D, Room as ht, TrackEvent as Nt, compareVersions as Un } from "livekit-client";
import * as M from "react";
const Ge = Math.min, ue = Math.max, ke = Math.round, te = (e) => ({
  x: e,
  y: e
}), Fn = {
  left: "right",
  right: "left",
  bottom: "top",
  top: "bottom"
}, jn = {
  start: "end",
  end: "start"
};
function vt(e, t, n) {
  return ue(e, Ge(t, n));
}
function Ne(e, t) {
  return typeof e == "function" ? e(t) : e;
}
function oe(e) {
  return e.split("-")[0];
}
function $e(e) {
  return e.split("-")[1];
}
function $t(e) {
  return e === "x" ? "y" : "x";
}
function Ut(e) {
  return e === "y" ? "height" : "width";
}
function Ue(e) {
  return ["top", "bottom"].includes(oe(e)) ? "y" : "x";
}
function Ft(e) {
  return $t(Ue(e));
}
function Wn(e, t, n) {
  n === void 0 && (n = !1);
  const r = $e(e), i = Ft(e), o = Ut(i);
  let s = i === "x" ? r === (n ? "end" : "start") ? "right" : "left" : r === "start" ? "bottom" : "top";
  return t.reference[o] > t.floating[o] && (s = Oe(s)), [s, Oe(s)];
}
function Bn(e) {
  const t = Oe(e);
  return [Ke(e), t, Ke(t)];
}
function Ke(e) {
  return e.replace(/start|end/g, (t) => jn[t]);
}
function Vn(e, t, n) {
  const r = ["left", "right"], i = ["right", "left"], o = ["top", "bottom"], s = ["bottom", "top"];
  switch (e) {
    case "top":
    case "bottom":
      return n ? t ? i : r : t ? r : i;
    case "left":
    case "right":
      return t ? o : s;
    default:
      return [];
  }
}
function Hn(e, t, n, r) {
  const i = $e(e);
  let o = Vn(oe(e), n === "start", r);
  return i && (o = o.map((s) => s + "-" + i), t && (o = o.concat(o.map(Ke)))), o;
}
function Oe(e) {
  return e.replace(/left|right|bottom|top/g, (t) => Fn[t]);
}
function zn(e) {
  return {
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
    ...e
  };
}
function Yn(e) {
  return typeof e != "number" ? zn(e) : {
    top: e,
    right: e,
    bottom: e,
    left: e
  };
}
function Le(e) {
  const {
    x: t,
    y: n,
    width: r,
    height: i
  } = e;
  return {
    width: r,
    height: i,
    top: n,
    left: t,
    right: t + r,
    bottom: n + i,
    x: t,
    y: n
  };
}
function mt(e, t, n) {
  let {
    reference: r,
    floating: i
  } = e;
  const o = Ue(t), s = Ft(t), a = Ut(s), c = oe(t), u = o === "y", l = r.x + r.width / 2 - i.width / 2, f = r.y + r.height / 2 - i.height / 2, h = r[a] / 2 - i[a] / 2;
  let d;
  switch (c) {
    case "top":
      d = {
        x: l,
        y: r.y - i.height
      };
      break;
    case "bottom":
      d = {
        x: l,
        y: r.y + r.height
      };
      break;
    case "right":
      d = {
        x: r.x + r.width,
        y: f
      };
      break;
    case "left":
      d = {
        x: r.x - i.width,
        y: f
      };
      break;
    default:
      d = {
        x: r.x,
        y: r.y
      };
  }
  switch ($e(t)) {
    case "start":
      d[s] -= h * (n && u ? -1 : 1);
      break;
    case "end":
      d[s] += h * (n && u ? -1 : 1);
      break;
  }
  return d;
}
const Gn = async (e, t, n) => {
  const {
    placement: r = "bottom",
    strategy: i = "absolute",
    middleware: o = [],
    platform: s
  } = n, a = o.filter(Boolean), c = await (s.isRTL == null ? void 0 : s.isRTL(t));
  let u = await s.getElementRects({
    reference: e,
    floating: t,
    strategy: i
  }), {
    x: l,
    y: f
  } = mt(u, r, c), h = r, d = {}, m = 0;
  for (let p = 0; p < a.length; p++) {
    const {
      name: g,
      fn: v
    } = a[p], {
      x: E,
      y: P,
      data: L,
      reset: C
    } = await v({
      x: l,
      y: f,
      initialPlacement: r,
      placement: h,
      strategy: i,
      middlewareData: d,
      rects: u,
      platform: s,
      elements: {
        reference: e,
        floating: t
      }
    });
    l = E ?? l, f = P ?? f, d = {
      ...d,
      [g]: {
        ...d[g],
        ...L
      }
    }, C && m <= 50 && (m++, typeof C == "object" && (C.placement && (h = C.placement), C.rects && (u = C.rects === !0 ? await s.getElementRects({
      reference: e,
      floating: t,
      strategy: i
    }) : C.rects), {
      x: l,
      y: f
    } = mt(u, h, c)), p = -1);
  }
  return {
    x: l,
    y: f,
    placement: h,
    strategy: i,
    middlewareData: d
  };
};
async function jt(e, t) {
  var n;
  t === void 0 && (t = {});
  const {
    x: r,
    y: i,
    platform: o,
    rects: s,
    elements: a,
    strategy: c
  } = e, {
    boundary: u = "clippingAncestors",
    rootBoundary: l = "viewport",
    elementContext: f = "floating",
    altBoundary: h = !1,
    padding: d = 0
  } = Ne(t, e), m = Yn(d), g = a[h ? f === "floating" ? "reference" : "floating" : f], v = Le(await o.getClippingRect({
    element: (n = await (o.isElement == null ? void 0 : o.isElement(g))) == null || n ? g : g.contextElement || await (o.getDocumentElement == null ? void 0 : o.getDocumentElement(a.floating)),
    boundary: u,
    rootBoundary: l,
    strategy: c
  })), E = f === "floating" ? {
    ...s.floating,
    x: r,
    y: i
  } : s.reference, P = await (o.getOffsetParent == null ? void 0 : o.getOffsetParent(a.floating)), L = await (o.isElement == null ? void 0 : o.isElement(P)) ? await (o.getScale == null ? void 0 : o.getScale(P)) || {
    x: 1,
    y: 1
  } : {
    x: 1,
    y: 1
  }, C = Le(o.convertOffsetParentRelativeRectToViewportRelativeRect ? await o.convertOffsetParentRelativeRectToViewportRelativeRect({
    elements: a,
    rect: E,
    offsetParent: P,
    strategy: c
  }) : E);
  return {
    top: (v.top - C.top + m.top) / L.y,
    bottom: (C.bottom - v.bottom + m.bottom) / L.y,
    left: (v.left - C.left + m.left) / L.x,
    right: (C.right - v.right + m.right) / L.x
  };
}
const Kn = function(e) {
  return e === void 0 && (e = {}), {
    name: "flip",
    options: e,
    async fn(t) {
      var n, r;
      const {
        placement: i,
        middlewareData: o,
        rects: s,
        initialPlacement: a,
        platform: c,
        elements: u
      } = t, {
        mainAxis: l = !0,
        crossAxis: f = !0,
        fallbackPlacements: h,
        fallbackStrategy: d = "bestFit",
        fallbackAxisSideDirection: m = "none",
        flipAlignment: p = !0,
        ...g
      } = Ne(e, t);
      if ((n = o.arrow) != null && n.alignmentOffset)
        return {};
      const v = oe(i), E = oe(a) === a, P = await (c.isRTL == null ? void 0 : c.isRTL(u.floating)), L = h || (E || !p ? [Oe(a)] : Bn(a));
      !h && m !== "none" && L.push(...Hn(a, p, m, P));
      const C = [a, ...L], ie = await jt(t, g), K = [];
      let T = ((r = o.flip) == null ? void 0 : r.overflows) || [];
      if (l && K.push(ie[v]), f) {
        const S = Wn(i, s, P);
        K.push(ie[S[0]], ie[S[1]]);
      }
      if (T = [...T, {
        placement: i,
        overflows: K
      }], !K.every((S) => S <= 0)) {
        var x, I;
        const S = (((x = o.flip) == null ? void 0 : x.index) || 0) + 1, V = C[S];
        if (V)
          return {
            data: {
              index: S,
              overflows: T
            },
            reset: {
              placement: V
            }
          };
        let X = (I = T.filter((Z) => Z.overflows[0] <= 0).sort((Z, ce) => Z.overflows[1] - ce.overflows[1])[0]) == null ? void 0 : I.placement;
        if (!X)
          switch (d) {
            case "bestFit": {
              var w;
              const Z = (w = T.map((ce) => [ce.placement, ce.overflows.filter((ge) => ge > 0).reduce((ge, Rn) => ge + Rn, 0)]).sort((ce, ge) => ce[1] - ge[1])[0]) == null ? void 0 : w[0];
              Z && (X = Z);
              break;
            }
            case "initialPlacement":
              X = a;
              break;
          }
        if (i !== X)
          return {
            reset: {
              placement: X
            }
          };
      }
      return {};
    }
  };
};
async function Qn(e, t) {
  const {
    placement: n,
    platform: r,
    elements: i
  } = e, o = await (r.isRTL == null ? void 0 : r.isRTL(i.floating)), s = oe(n), a = $e(n), c = Ue(n) === "y", u = ["left", "top"].includes(s) ? -1 : 1, l = o && c ? -1 : 1, f = Ne(t, e);
  let {
    mainAxis: h,
    crossAxis: d,
    alignmentAxis: m
  } = typeof f == "number" ? {
    mainAxis: f,
    crossAxis: 0,
    alignmentAxis: null
  } : {
    mainAxis: 0,
    crossAxis: 0,
    alignmentAxis: null,
    ...f
  };
  return a && typeof m == "number" && (d = a === "end" ? m * -1 : m), c ? {
    x: d * l,
    y: h * u
  } : {
    x: h * u,
    y: d * l
  };
}
const Jn = function(e) {
  return e === void 0 && (e = 0), {
    name: "offset",
    options: e,
    async fn(t) {
      var n, r;
      const {
        x: i,
        y: o,
        placement: s,
        middlewareData: a
      } = t, c = await Qn(t, e);
      return s === ((n = a.offset) == null ? void 0 : n.placement) && (r = a.arrow) != null && r.alignmentOffset ? {} : {
        x: i + c.x,
        y: o + c.y,
        data: {
          ...c,
          placement: s
        }
      };
    }
  };
}, qn = function(e) {
  return e === void 0 && (e = {}), {
    name: "shift",
    options: e,
    async fn(t) {
      const {
        x: n,
        y: r,
        placement: i
      } = t, {
        mainAxis: o = !0,
        crossAxis: s = !1,
        limiter: a = {
          fn: (g) => {
            let {
              x: v,
              y: E
            } = g;
            return {
              x: v,
              y: E
            };
          }
        },
        ...c
      } = Ne(e, t), u = {
        x: n,
        y: r
      }, l = await jt(t, c), f = Ue(oe(i)), h = $t(f);
      let d = u[h], m = u[f];
      if (o) {
        const g = h === "y" ? "top" : "left", v = h === "y" ? "bottom" : "right", E = d + l[g], P = d - l[v];
        d = vt(E, d, P);
      }
      if (s) {
        const g = f === "y" ? "top" : "left", v = f === "y" ? "bottom" : "right", E = m + l[g], P = m - l[v];
        m = vt(E, m, P);
      }
      const p = a.fn({
        ...t,
        [h]: d,
        [f]: m
      });
      return {
        ...p,
        data: {
          x: p.x - n,
          y: p.y - r
        }
      };
    }
  };
};
function Fe() {
  return typeof window < "u";
}
function ve(e) {
  return Wt(e) ? (e.nodeName || "").toLowerCase() : "#document";
}
function U(e) {
  var t;
  return (e == null || (t = e.ownerDocument) == null ? void 0 : t.defaultView) || window;
}
function J(e) {
  var t;
  return (t = (Wt(e) ? e.ownerDocument : e.document) || window.document) == null ? void 0 : t.documentElement;
}
function Wt(e) {
  return Fe() ? e instanceof Node || e instanceof U(e).Node : !1;
}
function H(e) {
  return Fe() ? e instanceof Element || e instanceof U(e).Element : !1;
}
function G(e) {
  return Fe() ? e instanceof HTMLElement || e instanceof U(e).HTMLElement : !1;
}
function gt(e) {
  return !Fe() || typeof ShadowRoot > "u" ? !1 : e instanceof ShadowRoot || e instanceof U(e).ShadowRoot;
}
function Ee(e) {
  const {
    overflow: t,
    overflowX: n,
    overflowY: r,
    display: i
  } = z(e);
  return /auto|scroll|overlay|hidden|clip/.test(t + r + n) && !["inline", "contents"].includes(i);
}
function Xn(e) {
  return ["table", "td", "th"].includes(ve(e));
}
function je(e) {
  return [":popover-open", ":modal"].some((t) => {
    try {
      return e.matches(t);
    } catch {
      return !1;
    }
  });
}
function rt(e) {
  const t = it(), n = H(e) ? z(e) : e;
  return n.transform !== "none" || n.perspective !== "none" || (n.containerType ? n.containerType !== "normal" : !1) || !t && (n.backdropFilter ? n.backdropFilter !== "none" : !1) || !t && (n.filter ? n.filter !== "none" : !1) || ["transform", "perspective", "filter"].some((r) => (n.willChange || "").includes(r)) || ["paint", "layout", "strict", "content"].some((r) => (n.contain || "").includes(r));
}
function Zn(e) {
  let t = ne(e);
  for (; G(t) && !de(t); ) {
    if (rt(t))
      return t;
    if (je(t))
      return null;
    t = ne(t);
  }
  return null;
}
function it() {
  return typeof CSS > "u" || !CSS.supports ? !1 : CSS.supports("-webkit-backdrop-filter", "none");
}
function de(e) {
  return ["html", "body", "#document"].includes(ve(e));
}
function z(e) {
  return U(e).getComputedStyle(e);
}
function We(e) {
  return H(e) ? {
    scrollLeft: e.scrollLeft,
    scrollTop: e.scrollTop
  } : {
    scrollLeft: e.scrollX,
    scrollTop: e.scrollY
  };
}
function ne(e) {
  if (ve(e) === "html")
    return e;
  const t = (
    // Step into the shadow DOM of the parent of a slotted node.
    e.assignedSlot || // DOM Element detected.
    e.parentNode || // ShadowRoot detected.
    gt(e) && e.host || // Fallback.
    J(e)
  );
  return gt(t) ? t.host : t;
}
function Bt(e) {
  const t = ne(e);
  return de(t) ? e.ownerDocument ? e.ownerDocument.body : e.body : G(t) && Ee(t) ? t : Bt(t);
}
function Vt(e, t, n) {
  var r;
  t === void 0 && (t = []);
  const i = Bt(e), o = i === ((r = e.ownerDocument) == null ? void 0 : r.body), s = U(i);
  return o ? (Qe(s), t.concat(s, s.visualViewport || [], Ee(i) ? i : [], [])) : t.concat(i, Vt(i, []));
}
function Qe(e) {
  return e.parent && Object.getPrototypeOf(e.parent) ? e.frameElement : null;
}
function Ht(e) {
  const t = z(e);
  let n = parseFloat(t.width) || 0, r = parseFloat(t.height) || 0;
  const i = G(e), o = i ? e.offsetWidth : n, s = i ? e.offsetHeight : r, a = ke(n) !== o || ke(r) !== s;
  return a && (n = o, r = s), {
    width: n,
    height: r,
    $: a
  };
}
function zt(e) {
  return H(e) ? e : e.contextElement;
}
function le(e) {
  const t = zt(e);
  if (!G(t))
    return te(1);
  const n = t.getBoundingClientRect(), {
    width: r,
    height: i,
    $: o
  } = Ht(t);
  let s = (o ? ke(n.width) : n.width) / r, a = (o ? ke(n.height) : n.height) / i;
  return (!s || !Number.isFinite(s)) && (s = 1), (!a || !Number.isFinite(a)) && (a = 1), {
    x: s,
    y: a
  };
}
const er = /* @__PURE__ */ te(0);
function Yt(e) {
  const t = U(e);
  return !it() || !t.visualViewport ? er : {
    x: t.visualViewport.offsetLeft,
    y: t.visualViewport.offsetTop
  };
}
function tr(e, t, n) {
  return t === void 0 && (t = !1), !n || t && n !== U(e) ? !1 : t;
}
function we(e, t, n, r) {
  t === void 0 && (t = !1), n === void 0 && (n = !1);
  const i = e.getBoundingClientRect(), o = zt(e);
  let s = te(1);
  t && (r ? H(r) && (s = le(r)) : s = le(e));
  const a = tr(o, n, r) ? Yt(o) : te(0);
  let c = (i.left + a.x) / s.x, u = (i.top + a.y) / s.y, l = i.width / s.x, f = i.height / s.y;
  if (o) {
    const h = U(o), d = r && H(r) ? U(r) : r;
    let m = h, p = Qe(m);
    for (; p && r && d !== m; ) {
      const g = le(p), v = p.getBoundingClientRect(), E = z(p), P = v.left + (p.clientLeft + parseFloat(E.paddingLeft)) * g.x, L = v.top + (p.clientTop + parseFloat(E.paddingTop)) * g.y;
      c *= g.x, u *= g.y, l *= g.x, f *= g.y, c += P, u += L, m = U(p), p = Qe(m);
    }
  }
  return Le({
    width: l,
    height: f,
    x: c,
    y: u
  });
}
function nr(e) {
  let {
    elements: t,
    rect: n,
    offsetParent: r,
    strategy: i
  } = e;
  const o = i === "fixed", s = J(r), a = t ? je(t.floating) : !1;
  if (r === s || a && o)
    return n;
  let c = {
    scrollLeft: 0,
    scrollTop: 0
  }, u = te(1);
  const l = te(0), f = G(r);
  if ((f || !f && !o) && ((ve(r) !== "body" || Ee(s)) && (c = We(r)), G(r))) {
    const h = we(r);
    u = le(r), l.x = h.x + r.clientLeft, l.y = h.y + r.clientTop;
  }
  return {
    width: n.width * u.x,
    height: n.height * u.y,
    x: n.x * u.x - c.scrollLeft * u.x + l.x,
    y: n.y * u.y - c.scrollTop * u.y + l.y
  };
}
function rr(e) {
  return Array.from(e.getClientRects());
}
function Je(e, t) {
  const n = We(e).scrollLeft;
  return t ? t.left + n : we(J(e)).left + n;
}
function ir(e) {
  const t = J(e), n = We(e), r = e.ownerDocument.body, i = ue(t.scrollWidth, t.clientWidth, r.scrollWidth, r.clientWidth), o = ue(t.scrollHeight, t.clientHeight, r.scrollHeight, r.clientHeight);
  let s = -n.scrollLeft + Je(e);
  const a = -n.scrollTop;
  return z(r).direction === "rtl" && (s += ue(t.clientWidth, r.clientWidth) - i), {
    width: i,
    height: o,
    x: s,
    y: a
  };
}
function or(e, t) {
  const n = U(e), r = J(e), i = n.visualViewport;
  let o = r.clientWidth, s = r.clientHeight, a = 0, c = 0;
  if (i) {
    o = i.width, s = i.height;
    const u = it();
    (!u || u && t === "fixed") && (a = i.offsetLeft, c = i.offsetTop);
  }
  return {
    width: o,
    height: s,
    x: a,
    y: c
  };
}
function sr(e, t) {
  const n = we(e, !0, t === "fixed"), r = n.top + e.clientTop, i = n.left + e.clientLeft, o = G(e) ? le(e) : te(1), s = e.clientWidth * o.x, a = e.clientHeight * o.y, c = i * o.x, u = r * o.y;
  return {
    width: s,
    height: a,
    x: c,
    y: u
  };
}
function bt(e, t, n) {
  let r;
  if (t === "viewport")
    r = or(e, n);
  else if (t === "document")
    r = ir(J(e));
  else if (H(t))
    r = sr(t, n);
  else {
    const i = Yt(e);
    r = {
      ...t,
      x: t.x - i.x,
      y: t.y - i.y
    };
  }
  return Le(r);
}
function Gt(e, t) {
  const n = ne(e);
  return n === t || !H(n) || de(n) ? !1 : z(n).position === "fixed" || Gt(n, t);
}
function ar(e, t) {
  const n = t.get(e);
  if (n)
    return n;
  let r = Vt(e, []).filter((a) => H(a) && ve(a) !== "body"), i = null;
  const o = z(e).position === "fixed";
  let s = o ? ne(e) : e;
  for (; H(s) && !de(s); ) {
    const a = z(s), c = rt(s);
    !c && a.position === "fixed" && (i = null), (o ? !c && !i : !c && a.position === "static" && !!i && ["absolute", "fixed"].includes(i.position) || Ee(s) && !c && Gt(e, s)) ? r = r.filter((l) => l !== s) : i = a, s = ne(s);
  }
  return t.set(e, r), r;
}
function cr(e) {
  let {
    element: t,
    boundary: n,
    rootBoundary: r,
    strategy: i
  } = e;
  const s = [...n === "clippingAncestors" ? je(t) ? [] : ar(t, this._c) : [].concat(n), r], a = s[0], c = s.reduce((u, l) => {
    const f = bt(t, l, i);
    return u.top = ue(f.top, u.top), u.right = Ge(f.right, u.right), u.bottom = Ge(f.bottom, u.bottom), u.left = ue(f.left, u.left), u;
  }, bt(t, a, i));
  return {
    width: c.right - c.left,
    height: c.bottom - c.top,
    x: c.left,
    y: c.top
  };
}
function ur(e) {
  const {
    width: t,
    height: n
  } = Ht(e);
  return {
    width: t,
    height: n
  };
}
function lr(e, t, n) {
  const r = G(t), i = J(t), o = n === "fixed", s = we(e, !0, o, t);
  let a = {
    scrollLeft: 0,
    scrollTop: 0
  };
  const c = te(0);
  if (r || !r && !o)
    if ((ve(t) !== "body" || Ee(i)) && (a = We(t)), r) {
      const d = we(t, !0, o, t);
      c.x = d.x + t.clientLeft, c.y = d.y + t.clientTop;
    } else i && (c.x = Je(i));
  let u = 0, l = 0;
  if (i && !r && !o) {
    const d = i.getBoundingClientRect();
    l = d.top + a.scrollTop, u = d.left + a.scrollLeft - // RTL <body> scrollbar.
    Je(i, d);
  }
  const f = s.left + a.scrollLeft - c.x - u, h = s.top + a.scrollTop - c.y - l;
  return {
    x: f,
    y: h,
    width: s.width,
    height: s.height
  };
}
function He(e) {
  return z(e).position === "static";
}
function yt(e, t) {
  if (!G(e) || z(e).position === "fixed")
    return null;
  if (t)
    return t(e);
  let n = e.offsetParent;
  return J(e) === n && (n = n.ownerDocument.body), n;
}
function Kt(e, t) {
  const n = U(e);
  if (je(e))
    return n;
  if (!G(e)) {
    let i = ne(e);
    for (; i && !de(i); ) {
      if (H(i) && !He(i))
        return i;
      i = ne(i);
    }
    return n;
  }
  let r = yt(e, t);
  for (; r && Xn(r) && He(r); )
    r = yt(r, t);
  return r && de(r) && He(r) && !rt(r) ? n : r || Zn(e) || n;
}
const fr = async function(e) {
  const t = this.getOffsetParent || Kt, n = this.getDimensions, r = await n(e.floating);
  return {
    reference: lr(e.reference, await t(e.floating), e.strategy),
    floating: {
      x: 0,
      y: 0,
      width: r.width,
      height: r.height
    }
  };
};
function dr(e) {
  return z(e).direction === "rtl";
}
const pr = {
  convertOffsetParentRelativeRectToViewportRelativeRect: nr,
  getDocumentElement: J,
  getClippingRect: cr,
  getOffsetParent: Kt,
  getElementRects: fr,
  getClientRects: rr,
  getDimensions: ur,
  getScale: le,
  isElement: H,
  isRTL: dr
}, hr = Jn, vr = qn, mr = Kn, gr = (e, t, n) => {
  const r = /* @__PURE__ */ new Map(), i = {
    platform: pr,
    ...n
  }, o = {
    ...i.platform,
    _c: r
  };
  return Gn(e, t, {
    ...i,
    platform: o
  });
};
var br = typeof globalThis < "u" ? globalThis : typeof window < "u" ? window : typeof global < "u" ? global : typeof self < "u" ? self : {};
function yr(e) {
  return e && e.__esModule && Object.prototype.hasOwnProperty.call(e, "default") ? e.default : e;
}
var Qt = { exports: {} };
(function(e) {
  (function(t, n) {
    e.exports ? e.exports = n() : t.log = n();
  })(br, function() {
    var t = function() {
    }, n = "undefined", r = typeof window !== n && typeof window.navigator !== n && /Trident\/|MSIE /.test(window.navigator.userAgent), i = [
      "trace",
      "debug",
      "info",
      "warn",
      "error"
    ], o = {}, s = null;
    function a(p, g) {
      var v = p[g];
      if (typeof v.bind == "function")
        return v.bind(p);
      try {
        return Function.prototype.bind.call(v, p);
      } catch {
        return function() {
          return Function.prototype.apply.apply(v, [p, arguments]);
        };
      }
    }
    function c() {
      console.log && (console.log.apply ? console.log.apply(console, arguments) : Function.prototype.apply.apply(console.log, [console, arguments])), console.trace && console.trace();
    }
    function u(p) {
      return p === "debug" && (p = "log"), typeof console === n ? !1 : p === "trace" && r ? c : console[p] !== void 0 ? a(console, p) : console.log !== void 0 ? a(console, "log") : t;
    }
    function l() {
      for (var p = this.getLevel(), g = 0; g < i.length; g++) {
        var v = i[g];
        this[v] = g < p ? t : this.methodFactory(v, p, this.name);
      }
      if (this.log = this.debug, typeof console === n && p < this.levels.SILENT)
        return "No console available for logging";
    }
    function f(p) {
      return function() {
        typeof console !== n && (l.call(this), this[p].apply(this, arguments));
      };
    }
    function h(p, g, v) {
      return u(p) || f.apply(this, arguments);
    }
    function d(p, g) {
      var v = this, E, P, L, C = "loglevel";
      typeof p == "string" ? C += ":" + p : typeof p == "symbol" && (C = void 0);
      function ie(w) {
        var S = (i[w] || "silent").toUpperCase();
        if (!(typeof window === n || !C)) {
          try {
            window.localStorage[C] = S;
            return;
          } catch {
          }
          try {
            window.document.cookie = encodeURIComponent(C) + "=" + S + ";";
          } catch {
          }
        }
      }
      function K() {
        var w;
        if (!(typeof window === n || !C)) {
          try {
            w = window.localStorage[C];
          } catch {
          }
          if (typeof w === n)
            try {
              var S = window.document.cookie, V = encodeURIComponent(C), X = S.indexOf(V + "=");
              X !== -1 && (w = /^([^;]+)/.exec(
                S.slice(X + V.length + 1)
              )[1]);
            } catch {
            }
          return v.levels[w] === void 0 && (w = void 0), w;
        }
      }
      function T() {
        if (!(typeof window === n || !C)) {
          try {
            window.localStorage.removeItem(C);
          } catch {
          }
          try {
            window.document.cookie = encodeURIComponent(C) + "=; expires=Thu, 01 Jan 1970 00:00:00 UTC";
          } catch {
          }
        }
      }
      function x(w) {
        var S = w;
        if (typeof S == "string" && v.levels[S.toUpperCase()] !== void 0 && (S = v.levels[S.toUpperCase()]), typeof S == "number" && S >= 0 && S <= v.levels.SILENT)
          return S;
        throw new TypeError("log.setLevel() called with invalid level: " + w);
      }
      v.name = p, v.levels = {
        TRACE: 0,
        DEBUG: 1,
        INFO: 2,
        WARN: 3,
        ERROR: 4,
        SILENT: 5
      }, v.methodFactory = g || h, v.getLevel = function() {
        return L ?? P ?? E;
      }, v.setLevel = function(w, S) {
        return L = x(w), S !== !1 && ie(L), l.call(v);
      }, v.setDefaultLevel = function(w) {
        P = x(w), K() || v.setLevel(w, !1);
      }, v.resetLevel = function() {
        L = null, T(), l.call(v);
      }, v.enableAll = function(w) {
        v.setLevel(v.levels.TRACE, w);
      }, v.disableAll = function(w) {
        v.setLevel(v.levels.SILENT, w);
      }, v.rebuild = function() {
        if (s !== v && (E = x(s.getLevel())), l.call(v), s === v)
          for (var w in o)
            o[w].rebuild();
      }, E = x(
        s ? s.getLevel() : "WARN"
      );
      var I = K();
      I != null && (L = x(I)), l.call(v);
    }
    s = new d(), s.getLogger = function(g) {
      if (typeof g != "symbol" && typeof g != "string" || g === "")
        throw new TypeError("You must supply a name when creating a logger.");
      var v = o[g];
      return v || (v = o[g] = new d(
        g,
        s.methodFactory
      )), v;
    };
    var m = typeof window !== n ? window.log : void 0;
    return s.noConflict = function() {
      return typeof window !== n && window.log === s && (window.log = m), s;
    }, s.getLoggers = function() {
      return o;
    }, s.default = s, s;
  });
})(Qt);
var wr = Qt.exports;
const Sr = /* @__PURE__ */ yr(wr);
var qe = function(e, t) {
  return qe = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(n, r) {
    n.__proto__ = r;
  } || function(n, r) {
    for (var i in r) Object.prototype.hasOwnProperty.call(r, i) && (n[i] = r[i]);
  }, qe(e, t);
};
function q(e, t) {
  if (typeof t != "function" && t !== null)
    throw new TypeError("Class extends value " + String(t) + " is not a constructor or null");
  qe(e, t);
  function n() {
    this.constructor = e;
  }
  e.prototype = t === null ? Object.create(t) : (n.prototype = t.prototype, new n());
}
function xr(e, t, n, r) {
  function i(o) {
    return o instanceof n ? o : new n(function(s) {
      s(o);
    });
  }
  return new (n || (n = Promise))(function(o, s) {
    function a(l) {
      try {
        u(r.next(l));
      } catch (f) {
        s(f);
      }
    }
    function c(l) {
      try {
        u(r.throw(l));
      } catch (f) {
        s(f);
      }
    }
    function u(l) {
      l.done ? o(l.value) : i(l.value).then(a, c);
    }
    u((r = r.apply(e, t || [])).next());
  });
}
function Jt(e, t) {
  var n = { label: 0, sent: function() {
    if (o[0] & 1) throw o[1];
    return o[1];
  }, trys: [], ops: [] }, r, i, o, s = Object.create((typeof Iterator == "function" ? Iterator : Object).prototype);
  return s.next = a(0), s.throw = a(1), s.return = a(2), typeof Symbol == "function" && (s[Symbol.iterator] = function() {
    return this;
  }), s;
  function a(u) {
    return function(l) {
      return c([u, l]);
    };
  }
  function c(u) {
    if (r) throw new TypeError("Generator is already executing.");
    for (; s && (s = 0, u[0] && (n = 0)), n; ) try {
      if (r = 1, i && (o = u[0] & 2 ? i.return : u[0] ? i.throw || ((o = i.return) && o.call(i), 0) : i.next) && !(o = o.call(i, u[1])).done) return o;
      switch (i = 0, o && (u = [u[0] & 2, o.value]), u[0]) {
        case 0:
        case 1:
          o = u;
          break;
        case 4:
          return n.label++, { value: u[1], done: !1 };
        case 5:
          n.label++, i = u[1], u = [0];
          continue;
        case 7:
          u = n.ops.pop(), n.trys.pop();
          continue;
        default:
          if (o = n.trys, !(o = o.length > 0 && o[o.length - 1]) && (u[0] === 6 || u[0] === 2)) {
            n = 0;
            continue;
          }
          if (u[0] === 3 && (!o || u[1] > o[0] && u[1] < o[3])) {
            n.label = u[1];
            break;
          }
          if (u[0] === 6 && n.label < o[1]) {
            n.label = o[1], o = u;
            break;
          }
          if (o && n.label < o[2]) {
            n.label = o[2], n.ops.push(u);
            break;
          }
          o[2] && n.ops.pop(), n.trys.pop();
          continue;
      }
      u = t.call(e, n);
    } catch (l) {
      u = [6, l], i = 0;
    } finally {
      r = o = 0;
    }
    if (u[0] & 5) throw u[1];
    return { value: u[0] ? u[1] : void 0, done: !0 };
  }
}
function pe(e) {
  var t = typeof Symbol == "function" && Symbol.iterator, n = t && e[t], r = 0;
  if (n) return n.call(e);
  if (e && typeof e.length == "number") return {
    next: function() {
      return e && r >= e.length && (e = void 0), { value: e && e[r++], done: !e };
    }
  };
  throw new TypeError(t ? "Object is not iterable." : "Symbol.iterator is not defined.");
}
function he(e, t) {
  var n = typeof Symbol == "function" && e[Symbol.iterator];
  if (!n) return e;
  var r = n.call(e), i, o = [], s;
  try {
    for (; (t === void 0 || t-- > 0) && !(i = r.next()).done; ) o.push(i.value);
  } catch (a) {
    s = { error: a };
  } finally {
    try {
      i && !i.done && (n = r.return) && n.call(r);
    } finally {
      if (s) throw s.error;
    }
  }
  return o;
}
function Se(e, t, n) {
  if (n || arguments.length === 2) for (var r = 0, i = t.length, o; r < i; r++)
    (o || !(r in t)) && (o || (o = Array.prototype.slice.call(t, 0, r)), o[r] = t[r]);
  return e.concat(o || Array.prototype.slice.call(t));
}
function fe(e) {
  return this instanceof fe ? (this.v = e, this) : new fe(e);
}
function Er(e, t, n) {
  if (!Symbol.asyncIterator) throw new TypeError("Symbol.asyncIterator is not defined.");
  var r = n.apply(e, t || []), i, o = [];
  return i = Object.create((typeof AsyncIterator == "function" ? AsyncIterator : Object).prototype), a("next"), a("throw"), a("return", s), i[Symbol.asyncIterator] = function() {
    return this;
  }, i;
  function s(d) {
    return function(m) {
      return Promise.resolve(m).then(d, f);
    };
  }
  function a(d, m) {
    r[d] && (i[d] = function(p) {
      return new Promise(function(g, v) {
        o.push([d, p, g, v]) > 1 || c(d, p);
      });
    }, m && (i[d] = m(i[d])));
  }
  function c(d, m) {
    try {
      u(r[d](m));
    } catch (p) {
      h(o[0][3], p);
    }
  }
  function u(d) {
    d.value instanceof fe ? Promise.resolve(d.value.v).then(l, f) : h(o[0][2], d);
  }
  function l(d) {
    c("next", d);
  }
  function f(d) {
    c("throw", d);
  }
  function h(d, m) {
    d(m), o.shift(), o.length && c(o[0][0], o[0][1]);
  }
}
function Tr(e) {
  if (!Symbol.asyncIterator) throw new TypeError("Symbol.asyncIterator is not defined.");
  var t = e[Symbol.asyncIterator], n;
  return t ? t.call(e) : (e = typeof pe == "function" ? pe(e) : e[Symbol.iterator](), n = {}, r("next"), r("throw"), r("return"), n[Symbol.asyncIterator] = function() {
    return this;
  }, n);
  function r(o) {
    n[o] = e[o] && function(s) {
      return new Promise(function(a, c) {
        s = e[o](s), i(a, c, s.done, s.value);
      });
    };
  }
  function i(o, s, a, c) {
    Promise.resolve(c).then(function(u) {
      o({ value: u, done: a });
    }, s);
  }
}
function A(e) {
  return typeof e == "function";
}
function ot(e) {
  var t = function(r) {
    Error.call(r), r.stack = new Error().stack;
  }, n = e(t);
  return n.prototype = Object.create(Error.prototype), n.prototype.constructor = n, n;
}
var ze = ot(function(e) {
  return function(n) {
    e(this), this.message = n ? n.length + ` errors occurred during unsubscription:
` + n.map(function(r, i) {
      return i + 1 + ") " + r.toString();
    }).join(`
  `) : "", this.name = "UnsubscriptionError", this.errors = n;
  };
});
function _e(e, t) {
  if (e) {
    var n = e.indexOf(t);
    0 <= n && e.splice(n, 1);
  }
}
var Te = function() {
  function e(t) {
    this.initialTeardown = t, this.closed = !1, this._parentage = null, this._finalizers = null;
  }
  return e.prototype.unsubscribe = function() {
    var t, n, r, i, o;
    if (!this.closed) {
      this.closed = !0;
      var s = this._parentage;
      if (s)
        if (this._parentage = null, Array.isArray(s))
          try {
            for (var a = pe(s), c = a.next(); !c.done; c = a.next()) {
              var u = c.value;
              u.remove(this);
            }
          } catch (p) {
            t = { error: p };
          } finally {
            try {
              c && !c.done && (n = a.return) && n.call(a);
            } finally {
              if (t) throw t.error;
            }
          }
        else
          s.remove(this);
      var l = this.initialTeardown;
      if (A(l))
        try {
          l();
        } catch (p) {
          o = p instanceof ze ? p.errors : [p];
        }
      var f = this._finalizers;
      if (f) {
        this._finalizers = null;
        try {
          for (var h = pe(f), d = h.next(); !d.done; d = h.next()) {
            var m = d.value;
            try {
              wt(m);
            } catch (p) {
              o = o ?? [], p instanceof ze ? o = Se(Se([], he(o)), he(p.errors)) : o.push(p);
            }
          }
        } catch (p) {
          r = { error: p };
        } finally {
          try {
            d && !d.done && (i = h.return) && i.call(h);
          } finally {
            if (r) throw r.error;
          }
        }
      }
      if (o)
        throw new ze(o);
    }
  }, e.prototype.add = function(t) {
    var n;
    if (t && t !== this)
      if (this.closed)
        wt(t);
      else {
        if (t instanceof e) {
          if (t.closed || t._hasParent(this))
            return;
          t._addParent(this);
        }
        (this._finalizers = (n = this._finalizers) !== null && n !== void 0 ? n : []).push(t);
      }
  }, e.prototype._hasParent = function(t) {
    var n = this._parentage;
    return n === t || Array.isArray(n) && n.includes(t);
  }, e.prototype._addParent = function(t) {
    var n = this._parentage;
    this._parentage = Array.isArray(n) ? (n.push(t), n) : n ? [n, t] : t;
  }, e.prototype._removeParent = function(t) {
    var n = this._parentage;
    n === t ? this._parentage = null : Array.isArray(n) && _e(n, t);
  }, e.prototype.remove = function(t) {
    var n = this._finalizers;
    n && _e(n, t), t instanceof e && t._removeParent(this);
  }, e.EMPTY = function() {
    var t = new e();
    return t.closed = !0, t;
  }(), e;
}(), qt = Te.EMPTY;
function Xt(e) {
  return e instanceof Te || e && "closed" in e && A(e.remove) && A(e.add) && A(e.unsubscribe);
}
function wt(e) {
  A(e) ? e() : e.unsubscribe();
}
var Cr = {
  onUnhandledError: null,
  onStoppedNotification: null,
  Promise: void 0,
  useDeprecatedSynchronousErrorHandling: !1,
  useDeprecatedNextContext: !1
}, Zt = {
  setTimeout: function(e, t) {
    for (var n = [], r = 2; r < arguments.length; r++)
      n[r - 2] = arguments[r];
    return setTimeout.apply(void 0, Se([e, t], he(n)));
  },
  clearTimeout: function(e) {
    var t = Zt.delegate;
    return ((t == null ? void 0 : t.clearTimeout) || clearTimeout)(e);
  },
  delegate: void 0
};
function en(e) {
  Zt.setTimeout(function() {
    throw e;
  });
}
function Ie() {
}
function Ae(e) {
  e();
}
var st = function(e) {
  q(t, e);
  function t(n) {
    var r = e.call(this) || this;
    return r.isStopped = !1, n ? (r.destination = n, Xt(n) && n.add(r)) : r.destination = kr, r;
  }
  return t.create = function(n, r, i) {
    return new Xe(n, r, i);
  }, t.prototype.next = function(n) {
    this.isStopped || this._next(n);
  }, t.prototype.error = function(n) {
    this.isStopped || (this.isStopped = !0, this._error(n));
  }, t.prototype.complete = function() {
    this.isStopped || (this.isStopped = !0, this._complete());
  }, t.prototype.unsubscribe = function() {
    this.closed || (this.isStopped = !0, e.prototype.unsubscribe.call(this), this.destination = null);
  }, t.prototype._next = function(n) {
    this.destination.next(n);
  }, t.prototype._error = function(n) {
    try {
      this.destination.error(n);
    } finally {
      this.unsubscribe();
    }
  }, t.prototype._complete = function() {
    try {
      this.destination.complete();
    } finally {
      this.unsubscribe();
    }
  }, t;
}(Te), Pr = function() {
  function e(t) {
    this.partialObserver = t;
  }
  return e.prototype.next = function(t) {
    var n = this.partialObserver;
    if (n.next)
      try {
        n.next(t);
      } catch (r) {
        Ce(r);
      }
  }, e.prototype.error = function(t) {
    var n = this.partialObserver;
    if (n.error)
      try {
        n.error(t);
      } catch (r) {
        Ce(r);
      }
    else
      Ce(t);
  }, e.prototype.complete = function() {
    var t = this.partialObserver;
    if (t.complete)
      try {
        t.complete();
      } catch (n) {
        Ce(n);
      }
  }, e;
}(), Xe = function(e) {
  q(t, e);
  function t(n, r, i) {
    var o = e.call(this) || this, s;
    return A(n) || !n ? s = {
      next: n ?? void 0,
      error: r ?? void 0,
      complete: i ?? void 0
    } : s = n, o.destination = new Pr(s), o;
  }
  return t;
}(st);
function Ce(e) {
  en(e);
}
function Ar(e) {
  throw e;
}
var kr = {
  closed: !0,
  next: Ie,
  error: Ar,
  complete: Ie
}, at = function() {
  return typeof Symbol == "function" && Symbol.observable || "@@observable";
}();
function ct(e) {
  return e;
}
function Or(e) {
  return e.length === 0 ? ct : e.length === 1 ? e[0] : function(n) {
    return e.reduce(function(r, i) {
      return i(r);
    }, n);
  };
}
var O = function() {
  function e(t) {
    t && (this._subscribe = t);
  }
  return e.prototype.lift = function(t) {
    var n = new e();
    return n.source = this, n.operator = t, n;
  }, e.prototype.subscribe = function(t, n, r) {
    var i = this, o = _r(t) ? t : new Xe(t, n, r);
    return Ae(function() {
      var s = i, a = s.operator, c = s.source;
      o.add(a ? a.call(o, c) : c ? i._subscribe(o) : i._trySubscribe(o));
    }), o;
  }, e.prototype._trySubscribe = function(t) {
    try {
      return this._subscribe(t);
    } catch (n) {
      t.error(n);
    }
  }, e.prototype.forEach = function(t, n) {
    var r = this;
    return n = St(n), new n(function(i, o) {
      var s = new Xe({
        next: function(a) {
          try {
            t(a);
          } catch (c) {
            o(c), s.unsubscribe();
          }
        },
        error: o,
        complete: i
      });
      r.subscribe(s);
    });
  }, e.prototype._subscribe = function(t) {
    var n;
    return (n = this.source) === null || n === void 0 ? void 0 : n.subscribe(t);
  }, e.prototype[at] = function() {
    return this;
  }, e.prototype.pipe = function() {
    for (var t = [], n = 0; n < arguments.length; n++)
      t[n] = arguments[n];
    return Or(t)(this);
  }, e.prototype.toPromise = function(t) {
    var n = this;
    return t = St(t), new t(function(r, i) {
      var o;
      n.subscribe(function(s) {
        return o = s;
      }, function(s) {
        return i(s);
      }, function() {
        return r(o);
      });
    });
  }, e.create = function(t) {
    return new e(t);
  }, e;
}();
function St(e) {
  var t;
  return (t = e ?? Cr.Promise) !== null && t !== void 0 ? t : Promise;
}
function Lr(e) {
  return e && A(e.next) && A(e.error) && A(e.complete);
}
function _r(e) {
  return e && e instanceof st || Lr(e) && Xt(e);
}
function Ir(e) {
  return A(e == null ? void 0 : e.lift);
}
function B(e) {
  return function(t) {
    if (Ir(t))
      return t.lift(function(n) {
        try {
          return e(n, this);
        } catch (r) {
          this.error(r);
        }
      });
    throw new TypeError("Unable to lift unknown Observable type");
  };
}
function j(e, t, n, r, i) {
  return new Mr(e, t, n, r, i);
}
var Mr = function(e) {
  q(t, e);
  function t(n, r, i, o, s, a) {
    var c = e.call(this, n) || this;
    return c.onFinalize = s, c.shouldUnsubscribe = a, c._next = r ? function(u) {
      try {
        r(u);
      } catch (l) {
        n.error(l);
      }
    } : e.prototype._next, c._error = o ? function(u) {
      try {
        o(u);
      } catch (l) {
        n.error(l);
      } finally {
        this.unsubscribe();
      }
    } : e.prototype._error, c._complete = i ? function() {
      try {
        i();
      } catch (u) {
        n.error(u);
      } finally {
        this.unsubscribe();
      }
    } : e.prototype._complete, c;
  }
  return t.prototype.unsubscribe = function() {
    var n;
    if (!this.shouldUnsubscribe || this.shouldUnsubscribe()) {
      var r = this.closed;
      e.prototype.unsubscribe.call(this), !r && ((n = this.onFinalize) === null || n === void 0 || n.call(this));
    }
  }, t;
}(st), Dr = ot(function(e) {
  return function() {
    e(this), this.name = "ObjectUnsubscribedError", this.message = "object unsubscribed";
  };
}), re = function(e) {
  q(t, e);
  function t() {
    var n = e.call(this) || this;
    return n.closed = !1, n.currentObservers = null, n.observers = [], n.isStopped = !1, n.hasError = !1, n.thrownError = null, n;
  }
  return t.prototype.lift = function(n) {
    var r = new xt(this, this);
    return r.operator = n, r;
  }, t.prototype._throwIfClosed = function() {
    if (this.closed)
      throw new Dr();
  }, t.prototype.next = function(n) {
    var r = this;
    Ae(function() {
      var i, o;
      if (r._throwIfClosed(), !r.isStopped) {
        r.currentObservers || (r.currentObservers = Array.from(r.observers));
        try {
          for (var s = pe(r.currentObservers), a = s.next(); !a.done; a = s.next()) {
            var c = a.value;
            c.next(n);
          }
        } catch (u) {
          i = { error: u };
        } finally {
          try {
            a && !a.done && (o = s.return) && o.call(s);
          } finally {
            if (i) throw i.error;
          }
        }
      }
    });
  }, t.prototype.error = function(n) {
    var r = this;
    Ae(function() {
      if (r._throwIfClosed(), !r.isStopped) {
        r.hasError = r.isStopped = !0, r.thrownError = n;
        for (var i = r.observers; i.length; )
          i.shift().error(n);
      }
    });
  }, t.prototype.complete = function() {
    var n = this;
    Ae(function() {
      if (n._throwIfClosed(), !n.isStopped) {
        n.isStopped = !0;
        for (var r = n.observers; r.length; )
          r.shift().complete();
      }
    });
  }, t.prototype.unsubscribe = function() {
    this.isStopped = this.closed = !0, this.observers = this.currentObservers = null;
  }, Object.defineProperty(t.prototype, "observed", {
    get: function() {
      var n;
      return ((n = this.observers) === null || n === void 0 ? void 0 : n.length) > 0;
    },
    enumerable: !1,
    configurable: !0
  }), t.prototype._trySubscribe = function(n) {
    return this._throwIfClosed(), e.prototype._trySubscribe.call(this, n);
  }, t.prototype._subscribe = function(n) {
    return this._throwIfClosed(), this._checkFinalizedStatuses(n), this._innerSubscribe(n);
  }, t.prototype._innerSubscribe = function(n) {
    var r = this, i = this, o = i.hasError, s = i.isStopped, a = i.observers;
    return o || s ? qt : (this.currentObservers = null, a.push(n), new Te(function() {
      r.currentObservers = null, _e(a, n);
    }));
  }, t.prototype._checkFinalizedStatuses = function(n) {
    var r = this, i = r.hasError, o = r.thrownError, s = r.isStopped;
    i ? n.error(o) : s && n.complete();
  }, t.prototype.asObservable = function() {
    var n = new O();
    return n.source = this, n;
  }, t.create = function(n, r) {
    return new xt(n, r);
  }, t;
}(O), xt = function(e) {
  q(t, e);
  function t(n, r) {
    var i = e.call(this) || this;
    return i.destination = n, i.source = r, i;
  }
  return t.prototype.next = function(n) {
    var r, i;
    (i = (r = this.destination) === null || r === void 0 ? void 0 : r.next) === null || i === void 0 || i.call(r, n);
  }, t.prototype.error = function(n) {
    var r, i;
    (i = (r = this.destination) === null || r === void 0 ? void 0 : r.error) === null || i === void 0 || i.call(r, n);
  }, t.prototype.complete = function() {
    var n, r;
    (r = (n = this.destination) === null || n === void 0 ? void 0 : n.complete) === null || r === void 0 || r.call(n);
  }, t.prototype._subscribe = function(n) {
    var r, i;
    return (i = (r = this.source) === null || r === void 0 ? void 0 : r.subscribe(n)) !== null && i !== void 0 ? i : qt;
  }, t;
}(re), tn = function(e) {
  q(t, e);
  function t(n) {
    var r = e.call(this) || this;
    return r._value = n, r;
  }
  return Object.defineProperty(t.prototype, "value", {
    get: function() {
      return this.getValue();
    },
    enumerable: !1,
    configurable: !0
  }), t.prototype._subscribe = function(n) {
    var r = e.prototype._subscribe.call(this, n);
    return !r.closed && n.next(this._value), r;
  }, t.prototype.getValue = function() {
    var n = this, r = n.hasError, i = n.thrownError, o = n._value;
    if (r)
      throw i;
    return this._throwIfClosed(), o;
  }, t.prototype.next = function(n) {
    e.prototype.next.call(this, this._value = n);
  }, t;
}(re), Rr = {
  now: function() {
    return Date.now();
  },
  delegate: void 0
}, Nr = function(e) {
  q(t, e);
  function t(n, r) {
    return e.call(this) || this;
  }
  return t.prototype.schedule = function(n, r) {
    return this;
  }, t;
}(Te), Et = {
  setInterval: function(e, t) {
    for (var n = [], r = 2; r < arguments.length; r++)
      n[r - 2] = arguments[r];
    return setInterval.apply(void 0, Se([e, t], he(n)));
  },
  clearInterval: function(e) {
    return clearInterval(e);
  },
  delegate: void 0
}, $r = function(e) {
  q(t, e);
  function t(n, r) {
    var i = e.call(this, n, r) || this;
    return i.scheduler = n, i.work = r, i.pending = !1, i;
  }
  return t.prototype.schedule = function(n, r) {
    var i;
    if (r === void 0 && (r = 0), this.closed)
      return this;
    this.state = n;
    var o = this.id, s = this.scheduler;
    return o != null && (this.id = this.recycleAsyncId(s, o, r)), this.pending = !0, this.delay = r, this.id = (i = this.id) !== null && i !== void 0 ? i : this.requestAsyncId(s, this.id, r), this;
  }, t.prototype.requestAsyncId = function(n, r, i) {
    return i === void 0 && (i = 0), Et.setInterval(n.flush.bind(n, this), i);
  }, t.prototype.recycleAsyncId = function(n, r, i) {
    if (i === void 0 && (i = 0), i != null && this.delay === i && this.pending === !1)
      return r;
    r != null && Et.clearInterval(r);
  }, t.prototype.execute = function(n, r) {
    if (this.closed)
      return new Error("executing a cancelled action");
    this.pending = !1;
    var i = this._execute(n, r);
    if (i)
      return i;
    this.pending === !1 && this.id != null && (this.id = this.recycleAsyncId(this.scheduler, this.id, null));
  }, t.prototype._execute = function(n, r) {
    var i = !1, o;
    try {
      this.work(n);
    } catch (s) {
      i = !0, o = s || new Error("Scheduled action threw falsy error");
    }
    if (i)
      return this.unsubscribe(), o;
  }, t.prototype.unsubscribe = function() {
    if (!this.closed) {
      var n = this, r = n.id, i = n.scheduler, o = i.actions;
      this.work = this.state = this.scheduler = null, this.pending = !1, _e(o, this), r != null && (this.id = this.recycleAsyncId(i, r, null)), this.delay = null, e.prototype.unsubscribe.call(this);
    }
  }, t;
}(Nr), Tt = function() {
  function e(t, n) {
    n === void 0 && (n = e.now), this.schedulerActionCtor = t, this.now = n;
  }
  return e.prototype.schedule = function(t, n, r) {
    return n === void 0 && (n = 0), new this.schedulerActionCtor(this, t).schedule(r, n);
  }, e.now = Rr.now, e;
}(), Ur = function(e) {
  q(t, e);
  function t(n, r) {
    r === void 0 && (r = Tt.now);
    var i = e.call(this, n, r) || this;
    return i.actions = [], i._active = !1, i;
  }
  return t.prototype.flush = function(n) {
    var r = this.actions;
    if (this._active) {
      r.push(n);
      return;
    }
    var i;
    this._active = !0;
    do
      if (i = n.execute(n.state, n.delay))
        break;
    while (n = r.shift());
    if (this._active = !1, i) {
      for (; n = r.shift(); )
        n.unsubscribe();
      throw i;
    }
  }, t;
}(Tt), Fr = new Ur($r), jr = new O(function(e) {
  return e.complete();
});
function Wr(e) {
  return e && A(e.schedule);
}
function nn(e) {
  return e[e.length - 1];
}
function Be(e) {
  return Wr(nn(e)) ? e.pop() : void 0;
}
function Br(e, t) {
  return typeof nn(e) == "number" ? e.pop() : t;
}
var ut = function(e) {
  return e && typeof e.length == "number" && typeof e != "function";
};
function rn(e) {
  return A(e == null ? void 0 : e.then);
}
function on(e) {
  return A(e[at]);
}
function sn(e) {
  return Symbol.asyncIterator && A(e == null ? void 0 : e[Symbol.asyncIterator]);
}
function an(e) {
  return new TypeError("You provided " + (e !== null && typeof e == "object" ? "an invalid object" : "'" + e + "'") + " where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.");
}
function Vr() {
  return typeof Symbol != "function" || !Symbol.iterator ? "@@iterator" : Symbol.iterator;
}
var cn = Vr();
function un(e) {
  return A(e == null ? void 0 : e[cn]);
}
function ln(e) {
  return Er(this, arguments, function() {
    var n, r, i, o;
    return Jt(this, function(s) {
      switch (s.label) {
        case 0:
          n = e.getReader(), s.label = 1;
        case 1:
          s.trys.push([1, , 9, 10]), s.label = 2;
        case 2:
          return [4, fe(n.read())];
        case 3:
          return r = s.sent(), i = r.value, o = r.done, o ? [4, fe(void 0)] : [3, 5];
        case 4:
          return [2, s.sent()];
        case 5:
          return [4, fe(i)];
        case 6:
          return [4, s.sent()];
        case 7:
          return s.sent(), [3, 2];
        case 8:
          return [3, 10];
        case 9:
          return n.releaseLock(), [7];
        case 10:
          return [2];
      }
    });
  });
}
function fn(e) {
  return A(e == null ? void 0 : e.getReader);
}
function Y(e) {
  if (e instanceof O)
    return e;
  if (e != null) {
    if (on(e))
      return Hr(e);
    if (ut(e))
      return zr(e);
    if (rn(e))
      return Yr(e);
    if (sn(e))
      return dn(e);
    if (un(e))
      return Gr(e);
    if (fn(e))
      return Kr(e);
  }
  throw an(e);
}
function Hr(e) {
  return new O(function(t) {
    var n = e[at]();
    if (A(n.subscribe))
      return n.subscribe(t);
    throw new TypeError("Provided object does not correctly implement Symbol.observable");
  });
}
function zr(e) {
  return new O(function(t) {
    for (var n = 0; n < e.length && !t.closed; n++)
      t.next(e[n]);
    t.complete();
  });
}
function Yr(e) {
  return new O(function(t) {
    e.then(function(n) {
      t.closed || (t.next(n), t.complete());
    }, function(n) {
      return t.error(n);
    }).then(null, en);
  });
}
function Gr(e) {
  return new O(function(t) {
    var n, r;
    try {
      for (var i = pe(e), o = i.next(); !o.done; o = i.next()) {
        var s = o.value;
        if (t.next(s), t.closed)
          return;
      }
    } catch (a) {
      n = { error: a };
    } finally {
      try {
        o && !o.done && (r = i.return) && r.call(i);
      } finally {
        if (n) throw n.error;
      }
    }
    t.complete();
  });
}
function dn(e) {
  return new O(function(t) {
    Qr(e, t).catch(function(n) {
      return t.error(n);
    });
  });
}
function Kr(e) {
  return dn(ln(e));
}
function Qr(e, t) {
  var n, r, i, o;
  return xr(this, void 0, void 0, function() {
    var s, a;
    return Jt(this, function(c) {
      switch (c.label) {
        case 0:
          c.trys.push([0, 5, 6, 11]), n = Tr(e), c.label = 1;
        case 1:
          return [4, n.next()];
        case 2:
          if (r = c.sent(), !!r.done) return [3, 4];
          if (s = r.value, t.next(s), t.closed)
            return [2];
          c.label = 3;
        case 3:
          return [3, 1];
        case 4:
          return [3, 11];
        case 5:
          return a = c.sent(), i = { error: a }, [3, 11];
        case 6:
          return c.trys.push([6, , 9, 10]), r && !r.done && (o = n.return) ? [4, o.call(n)] : [3, 8];
        case 7:
          c.sent(), c.label = 8;
        case 8:
          return [3, 10];
        case 9:
          if (i) throw i.error;
          return [7];
        case 10:
          return [7];
        case 11:
          return t.complete(), [2];
      }
    });
  });
}
function ee(e, t, n, r, i) {
  r === void 0 && (r = 0), i === void 0 && (i = !1);
  var o = t.schedule(function() {
    n(), i ? e.add(this.schedule(null, r)) : this.unsubscribe();
  }, r);
  if (e.add(o), !i)
    return o;
}
function pn(e, t) {
  return t === void 0 && (t = 0), B(function(n, r) {
    n.subscribe(j(r, function(i) {
      return ee(r, e, function() {
        return r.next(i);
      }, t);
    }, function() {
      return ee(r, e, function() {
        return r.complete();
      }, t);
    }, function(i) {
      return ee(r, e, function() {
        return r.error(i);
      }, t);
    }));
  });
}
function hn(e, t) {
  return t === void 0 && (t = 0), B(function(n, r) {
    r.add(e.schedule(function() {
      return n.subscribe(r);
    }, t));
  });
}
function Jr(e, t) {
  return Y(e).pipe(hn(t), pn(t));
}
function qr(e, t) {
  return Y(e).pipe(hn(t), pn(t));
}
function Xr(e, t) {
  return new O(function(n) {
    var r = 0;
    return t.schedule(function() {
      r === e.length ? n.complete() : (n.next(e[r++]), n.closed || this.schedule());
    });
  });
}
function Zr(e, t) {
  return new O(function(n) {
    var r;
    return ee(n, t, function() {
      r = e[cn](), ee(n, t, function() {
        var i, o, s;
        try {
          i = r.next(), o = i.value, s = i.done;
        } catch (a) {
          n.error(a);
          return;
        }
        s ? n.complete() : n.next(o);
      }, 0, !0);
    }), function() {
      return A(r == null ? void 0 : r.return) && r.return();
    };
  });
}
function vn(e, t) {
  if (!e)
    throw new Error("Iterable cannot be null");
  return new O(function(n) {
    ee(n, t, function() {
      var r = e[Symbol.asyncIterator]();
      ee(n, t, function() {
        r.next().then(function(i) {
          i.done ? n.complete() : n.next(i.value);
        });
      }, 0, !0);
    });
  });
}
function ei(e, t) {
  return vn(ln(e), t);
}
function ti(e, t) {
  if (e != null) {
    if (on(e))
      return Jr(e, t);
    if (ut(e))
      return Xr(e, t);
    if (rn(e))
      return qr(e, t);
    if (sn(e))
      return vn(e, t);
    if (un(e))
      return Zr(e, t);
    if (fn(e))
      return ei(e, t);
  }
  throw an(e);
}
function lt(e, t) {
  return t ? ti(e, t) : Y(e);
}
function Ct() {
  for (var e = [], t = 0; t < arguments.length; t++)
    e[t] = arguments[t];
  var n = Be(e);
  return lt(e, n);
}
function ni(e) {
  return e instanceof Date && !isNaN(e);
}
var ri = ot(function(e) {
  return function(n) {
    n === void 0 && (n = null), e(this), this.message = "Timeout has occurred", this.name = "TimeoutError", this.info = n;
  };
});
function ii(e, t) {
  var n = ni(e) ? { first: e } : typeof e == "number" ? { each: e } : e, r = n.first, i = n.each, o = n.with, s = o === void 0 ? oi : o, a = n.scheduler, c = a === void 0 ? Fr : a, u = n.meta, l = u === void 0 ? null : u;
  if (r == null && i == null)
    throw new TypeError("No timeout provided.");
  return B(function(f, h) {
    var d, m, p = null, g = 0, v = function(E) {
      m = ee(h, c, function() {
        try {
          d.unsubscribe(), Y(s({
            meta: l,
            lastValue: p,
            seen: g
          })).subscribe(h);
        } catch (P) {
          h.error(P);
        }
      }, E);
    };
    d = f.subscribe(j(h, function(E) {
      m == null || m.unsubscribe(), g++, h.next(p = E), i > 0 && v(i);
    }, void 0, void 0, function() {
      m != null && m.closed || m == null || m.unsubscribe(), p = null;
    })), !g && v(r != null ? typeof r == "number" ? r : +r - c.now() : i);
  });
}
function oi(e) {
  throw new ri(e);
}
function k(e, t) {
  return B(function(n, r) {
    var i = 0;
    n.subscribe(j(r, function(o) {
      r.next(e.call(t, o, i++));
    }));
  });
}
var si = Array.isArray;
function ai(e, t) {
  return si(t) ? e.apply(void 0, Se([], he(t))) : e(t);
}
function ci(e) {
  return k(function(t) {
    return ai(e, t);
  });
}
function ui(e, t, n, r, i, o, s, a) {
  var c = [], u = 0, l = 0, f = !1, h = function() {
    f && !c.length && !u && t.complete();
  }, d = function(p) {
    return u < r ? m(p) : c.push(p);
  }, m = function(p) {
    u++;
    var g = !1;
    Y(n(p, l++)).subscribe(j(t, function(v) {
      t.next(v);
    }, function() {
      g = !0;
    }, void 0, function() {
      if (g)
        try {
          u--;
          for (var v = function() {
            var E = c.shift();
            s || m(E);
          }; c.length && u < r; )
            v();
          h();
        } catch (E) {
          t.error(E);
        }
    }));
  };
  return e.subscribe(j(t, d, function() {
    f = !0, h();
  })), function() {
  };
}
function ft(e, t, n) {
  return n === void 0 && (n = 1 / 0), A(t) ? ft(function(r, i) {
    return k(function(o, s) {
      return t(r, o, i, s);
    })(Y(e(r, i)));
  }, n) : (typeof t == "number" && (n = t), B(function(r, i) {
    return ui(r, i, e, n);
  }));
}
function mn(e) {
  return e === void 0 && (e = 1 / 0), ft(ct, e);
}
function li() {
  return mn(1);
}
function Me() {
  for (var e = [], t = 0; t < arguments.length; t++)
    e[t] = arguments[t];
  return li()(lt(e, Be(e)));
}
var fi = ["addListener", "removeListener"], di = ["addEventListener", "removeEventListener"], pi = ["on", "off"];
function Ze(e, t, n, r) {
  if (A(n) && (r = n, n = void 0), r)
    return Ze(e, t, n).pipe(ci(r));
  var i = he(mi(e) ? di.map(function(a) {
    return function(c) {
      return e[a](t, c, n);
    };
  }) : hi(e) ? fi.map(Pt(e, t)) : vi(e) ? pi.map(Pt(e, t)) : [], 2), o = i[0], s = i[1];
  if (!o && ut(e))
    return ft(function(a) {
      return Ze(a, t, n);
    })(Y(e));
  if (!o)
    throw new TypeError("Invalid event target");
  return new O(function(a) {
    var c = function() {
      for (var u = [], l = 0; l < arguments.length; l++)
        u[l] = arguments[l];
      return a.next(1 < u.length ? u : u[0]);
    };
    return o(c), function() {
      return s(c);
    };
  });
}
function Pt(e, t) {
  return function(n) {
    return function(r) {
      return e[n](t, r);
    };
  };
}
function hi(e) {
  return A(e.addListener) && A(e.removeListener);
}
function vi(e) {
  return A(e.on) && A(e.off);
}
function mi(e) {
  return A(e.addEventListener) && A(e.removeEventListener);
}
function gi() {
  for (var e = [], t = 0; t < arguments.length; t++)
    e[t] = arguments[t];
  var n = Be(e), r = Br(e, 1 / 0), i = e;
  return i.length ? i.length === 1 ? Y(i[0]) : mn(r)(lt(i, n)) : jr;
}
function dt(e, t) {
  return B(function(n, r) {
    var i = 0;
    n.subscribe(j(r, function(o) {
      return e.call(t, o, i++) && r.next(o);
    }));
  });
}
function bi(e, t, n, r, i) {
  return function(o, s) {
    var a = n, c = t, u = 0;
    o.subscribe(j(s, function(l) {
      var f = u++;
      c = a ? e(c, l, f) : (a = !0, l), s.next(c);
    }, i));
  };
}
function yi(e, t) {
  return t === void 0 && (t = ct), e = e ?? wi, B(function(n, r) {
    var i, o = !0;
    n.subscribe(j(r, function(s) {
      var a = t(s);
      (o || !e(i, a)) && (o = !1, i = a, r.next(s));
    }));
  });
}
function wi(e, t) {
  return e === t;
}
function Si(e) {
  return B(function(t, n) {
    try {
      t.subscribe(n);
    } finally {
      n.add(e);
    }
  });
}
function xi(e, t) {
  return B(bi(e, t, arguments.length >= 2, !0));
}
function Ei(e) {
  return B(function(t, n) {
    var r = !1, i = j(n, function() {
      i == null || i.unsubscribe(), r = !0;
    }, Ie);
    Y(e).subscribe(i), t.subscribe(j(n, function(o) {
      return r && n.next(o);
    }));
  });
}
function R() {
  for (var e = [], t = 0; t < arguments.length; t++)
    e[t] = arguments[t];
  var n = Be(e);
  return B(function(r, i) {
    (n ? Me(e, r, n) : Me(e, r)).subscribe(i);
  });
}
function gn(e, t) {
  return B(function(n, r) {
    var i = null, o = 0, s = !1, a = function() {
      return s && !i && r.complete();
    };
    n.subscribe(j(r, function(c) {
      i == null || i.unsubscribe();
      var u = 0, l = o++;
      Y(e(c, l)).subscribe(i = j(r, function(f) {
        return r.next(t ? t(c, f, l, u++) : f);
      }, function() {
        i = null, a();
      }));
    }, function() {
      s = !0, a();
    }));
  });
}
function At(e) {
  return B(function(t, n) {
    Y(e).subscribe(j(n, function() {
      return n.complete();
    }, Ie)), !n.closed && t.subscribe(n);
  });
}
var Ti = Object.defineProperty, Ci = Object.defineProperties, Pi = Object.getOwnPropertyDescriptors, kt = Object.getOwnPropertySymbols, Ai = Object.prototype.hasOwnProperty, ki = Object.prototype.propertyIsEnumerable, Ot = (e, t, n) => t in e ? Ti(e, t, { enumerable: !0, configurable: !0, writable: !0, value: n }) : e[t] = n, Q = (e, t) => {
  for (var n in t || (t = {}))
    Ai.call(t, n) && Ot(e, n, t[n]);
  if (kt)
    for (var n of kt(t))
      ki.call(t, n) && Ot(e, n, t[n]);
  return e;
}, ye = (e, t) => Ci(e, Pi(t)), W = (e, t, n) => new Promise((r, i) => {
  var o = (c) => {
    try {
      a(n.next(c));
    } catch (u) {
      i(u);
    }
  }, s = (c) => {
    try {
      a(n.throw(c));
    } catch (u) {
      i(u);
    }
  }, a = (c) => c.done ? r(c.value) : Promise.resolve(c.value).then(o, s);
  a((n = n.apply(e, t)).next());
}), bn = "lk";
function F(e) {
  return typeof e > "u" ? !1 : Oi(e) || Li(e);
}
function Oi(e) {
  var t;
  return e ? e.hasOwnProperty("participant") && e.hasOwnProperty("source") && e.hasOwnProperty("track") && typeof ((t = e.publication) == null ? void 0 : t.track) < "u" : !1;
}
function Li(e) {
  return e ? e.hasOwnProperty("participant") && e.hasOwnProperty("source") && e.hasOwnProperty("publication") && typeof e.publication < "u" : !1;
}
function xe(e) {
  return e ? e.hasOwnProperty("participant") && e.hasOwnProperty("source") && typeof e.publication > "u" : !1;
}
function N(e) {
  if (typeof e == "string" || typeof e == "number")
    return `${e}`;
  if (xe(e))
    return `${e.participant.identity}_${e.source}_placeholder`;
  if (F(e))
    return `${e.participant.identity}_${e.publication.source}_${e.publication.trackSid}`;
  throw new Error(`Can't generate a id for the given track reference: ${e}`);
}
function To(e, t) {
  return e === void 0 || t === void 0 ? !1 : F(e) && F(t) ? e.publication.trackSid === t.publication.trackSid : N(e) === N(t);
}
function Co(e, t) {
  return typeof t > "u" ? !1 : F(e) ? t.some(
    (n) => n.participant.identity === e.participant.identity && F(n) && n.publication.trackSid === e.publication.trackSid
  ) : xe(e) ? t.some(
    (n) => n.participant.identity === e.participant.identity && xe(n) && n.source === e.source
  ) : !1;
}
function _i(e, t) {
  return xe(e) && F(t) && t.participant.identity === e.participant.identity && t.source === e.source;
}
function Po() {
  const e = document.createElement("p");
  e.style.width = "100%", e.style.height = "200px";
  const t = document.createElement("div");
  t.style.position = "absolute", t.style.top = "0px", t.style.left = "0px", t.style.visibility = "hidden", t.style.width = "200px", t.style.height = "150px", t.style.overflow = "hidden", t.appendChild(e), document.body.appendChild(t);
  const n = e.offsetWidth;
  t.style.overflow = "scroll";
  let r = e.offsetWidth;
  return n === r && (r = t.clientWidth), document.body.removeChild(t), n - r;
}
function Ao() {
  return typeof document < "u";
}
function Ii(e) {
  e = Q({}, e);
  const t = "(?:(?:[a-z]+:)?//)?", n = "(?:\\S+(?::\\S*)?@)?", r = new RegExp(
    "(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}",
    "g"
  ).source, u = `(?:${t}|www\\.)${n}(?:localhost|${r}|(?:(?:[a-z\\u00a1-\\uffff0-9][-_]*)*[a-z\\u00a1-\\uffff0-9]+)(?:\\.(?:[a-z\\u00a1-\\uffff0-9]-*)*[a-z\\u00a1-\\uffff0-9]+)*(?:\\.(?:[a-z\\u00a1-\\uffff]{2,}))\\.?)(?::\\d{2,5})?(?:[/?#][^\\s"]*)?`;
  return e.exact ? new RegExp(`(?:^${u}$)`, "i") : new RegExp(u, "ig");
}
var Lt = "[^\\.\\s@:](?:[^\\s@:]*[^\\s@:\\.])?@[^\\.\\s@]+(?:\\.[^\\.\\s@]+)*";
function Mi({ exact: e } = {}) {
  return e ? new RegExp(`^${Lt}$`) : new RegExp(Lt, "g");
}
function ko(e, t) {
  return W(this, null, function* () {
    const { x: n, y: r } = yield gr(e, t, {
      placement: "top",
      middleware: [hr(6), mr(), vr({ padding: 5 })]
    });
    return { x: n, y: r };
  });
}
function Oo(e, t) {
  return !e.contains(t.target);
}
var Lo = () => ({
  email: Mi(),
  url: Ii({})
});
function _o(e, t) {
  const n = Object.entries(t).map(
    ([o, s], a) => Array.from(e.matchAll(s)).map(({ index: c, 0: u }) => ({
      type: o,
      weight: a,
      content: u,
      index: c ?? 0
    }))
  ).flat().sort((o, s) => {
    const a = o.index - s.index;
    return a !== 0 ? a : o.weight - s.weight;
  }).filter(({ index: o }, s, a) => {
    if (s === 0) return !0;
    const c = a[s - 1];
    return c.index + c.content.length <= o;
  }), r = [];
  let i = 0;
  for (const { type: o, content: s, index: a } of n)
    a > i && r.push(e.substring(i, a)), r.push({ type: o, content: s }), i = a + s.length;
  return e.length > i && r.push(e.substring(i)), r;
}
var Di = [
  b.ConnectionStateChanged,
  b.RoomMetadataChanged,
  b.ActiveSpeakersChanged,
  b.ConnectionQualityChanged,
  b.ParticipantConnected,
  b.ParticipantDisconnected,
  b.ParticipantPermissionsChanged,
  b.ParticipantMetadataChanged,
  b.ParticipantNameChanged,
  b.ParticipantAttributesChanged,
  b.TrackMuted,
  b.TrackUnmuted,
  b.TrackPublished,
  b.TrackUnpublished,
  b.TrackStreamStateChanged,
  b.TrackSubscriptionFailed,
  b.TrackSubscriptionPermissionChanged,
  b.TrackSubscriptionStatusChanged
], yn = [
  ...Di,
  b.LocalTrackPublished,
  b.LocalTrackUnpublished
], Ri = [
  y.TrackPublished,
  y.TrackUnpublished,
  y.TrackMuted,
  y.TrackUnmuted,
  y.TrackStreamStateChanged,
  y.TrackSubscribed,
  y.TrackUnsubscribed,
  y.TrackSubscriptionPermissionChanged,
  y.TrackSubscriptionFailed,
  y.LocalTrackPublished,
  y.LocalTrackUnpublished
], Ni = [
  y.ConnectionQualityChanged,
  y.IsSpeakingChanged,
  y.ParticipantMetadataChanged,
  y.ParticipantPermissionsChanged,
  y.TrackMuted,
  y.TrackUnmuted,
  y.TrackPublished,
  y.TrackUnpublished,
  y.TrackStreamStateChanged,
  y.TrackSubscriptionFailed,
  y.TrackSubscriptionPermissionChanged,
  y.TrackSubscriptionStatusChanged
], wn = [
  ...Ni,
  y.LocalTrackPublished,
  y.LocalTrackUnpublished
], _ = Sr.getLogger("lk-components-js");
_.setDefaultLevel("WARN");
function Io(e, t = {}) {
  var n;
  _.setLevel(e), Nn((n = t.liveKitClientLogLevel) != null ? n : e);
}
function Mo(e, t = {}) {
  var n;
  const r = _.methodFactory;
  _.methodFactory = (i, o, s) => {
    const a = r(i, o, s), c = pt[i], u = c >= o && c < pt.silent;
    return (l, f) => {
      f ? a(l, f) : a(l), u && e(c, l, f);
    };
  }, _.setLevel(_.getLevel()), $n((n = t.liveKitClientLogExtension) != null ? n : e);
}
var Do = [
  {
    columns: 1,
    rows: 1
  },
  {
    columns: 1,
    rows: 2,
    orientation: "portrait"
  },
  {
    columns: 2,
    rows: 1,
    orientation: "landscape"
  },
  {
    columns: 2,
    rows: 2,
    minWidth: 560
  },
  {
    columns: 3,
    rows: 3,
    minWidth: 700
  },
  {
    columns: 4,
    rows: 4,
    minWidth: 960
  },
  {
    columns: 5,
    rows: 5,
    minWidth: 1100
  }
];
function $i(e, t, n, r) {
  if (e.length < 1)
    throw new Error("At least one grid layout definition must be provided.");
  const i = Ui(e);
  if (n <= 0 || r <= 0)
    return i[0];
  let o = 0;
  const s = n / r > 1 ? "landscape" : "portrait";
  let a = i.find((c, u, l) => {
    o = u;
    const f = l.findIndex((h, d) => {
      const m = !h.orientation || h.orientation === s, p = d > u, g = h.maxTiles === c.maxTiles;
      return p && g && m;
    }) !== -1;
    return c.maxTiles >= t && !f;
  });
  if (a === void 0)
    if (a = i[i.length - 1], a)
      _.warn(
        `No layout found for: participantCount: ${t}, width/height: ${n}/${r} fallback to biggest available layout (${a}).`
      );
    else
      throw new Error("No layout or fallback layout found.");
  if ((n < a.minWidth || r < a.minHeight) && o > 0) {
    const c = i[o - 1];
    a = $i(
      i.slice(0, o),
      c.maxTiles,
      n,
      r
    );
  }
  return a;
}
function Ui(e) {
  return [...e].map((t) => {
    var n, r;
    return {
      name: `${t.columns}x${t.rows}`,
      columns: t.columns,
      rows: t.rows,
      maxTiles: t.columns * t.rows,
      minWidth: (n = t.minWidth) != null ? n : 0,
      minHeight: (r = t.minHeight) != null ? r : 0,
      orientation: t.orientation
    };
  }).sort((t, n) => t.maxTiles !== n.maxTiles ? t.maxTiles - n.maxTiles : t.minWidth !== 0 || n.minWidth !== 0 ? t.minWidth - n.minWidth : t.minHeight !== 0 || n.minHeight !== 0 ? t.minHeight - n.minHeight : 0);
}
function Ro() {
  return typeof navigator < "u" && navigator.mediaDevices && !!navigator.mediaDevices.getDisplayMedia;
}
function No(e, t) {
  var n;
  return ye(Q({}, e), {
    receivedAtMediaTimestamp: (n = t.rtpTimestamp) != null ? n : 0,
    receivedAt: t.timestamp
  });
}
function $o(e, t, n) {
  return [...e, ...t].reduceRight((r, i) => (r.find((o) => o.id === i.id) || r.unshift(i), r), []).slice(0 - n);
}
var Sn = [], xn = {
  showChat: !1,
  unreadMessages: 0,
  showSettings: !1
};
function Fi(e) {
  return typeof e == "object";
}
function Uo(e) {
  return Array.isArray(e) && e.filter(Fi).length > 0;
}
function En(e, t) {
  return t.audioLevel - e.audioLevel;
}
function Tn(e, t) {
  return e.isSpeaking === t.isSpeaking ? 0 : e.isSpeaking ? -1 : 1;
}
function Cn(e, t) {
  var n, r, i, o;
  return e.lastSpokeAt !== void 0 || t.lastSpokeAt !== void 0 ? ((r = (n = t.lastSpokeAt) == null ? void 0 : n.getTime()) != null ? r : 0) - ((o = (i = e.lastSpokeAt) == null ? void 0 : i.getTime()) != null ? o : 0) : 0;
}
function De(e, t) {
  var n, r, i, o;
  return ((r = (n = e.joinedAt) == null ? void 0 : n.getTime()) != null ? r : 0) - ((o = (i = t.joinedAt) == null ? void 0 : i.getTime()) != null ? o : 0);
}
function ji(e, t) {
  return F(e) ? F(t) ? 0 : -1 : F(t) ? 1 : 0;
}
function Wi(e, t) {
  const n = e.participant.isCameraEnabled, r = t.participant.isCameraEnabled;
  return n !== r ? n ? -1 : 1 : 0;
}
function Fo(e) {
  const t = [], n = [], r = [], i = [];
  e.forEach((a) => {
    a.participant.isLocal && a.source === D.Source.Camera ? t.push(a) : a.source === D.Source.ScreenShare ? n.push(a) : a.source === D.Source.Camera ? r.push(a) : i.push(a);
  });
  const o = Bi(n), s = Vi(r);
  return [...t, ...o, ...s, ...i];
}
function Bi(e) {
  const t = [], n = [];
  return e.forEach((i) => {
    i.participant.isLocal ? t.push(i) : n.push(i);
  }), t.sort((i, o) => De(i.participant, o.participant)), n.sort((i, o) => De(i.participant, o.participant)), [...n, ...t];
}
function Vi(e) {
  const t = [], n = [];
  return e.forEach((r) => {
    r.participant.isLocal ? t.push(r) : n.push(r);
  }), n.sort((r, i) => r.participant.isSpeaking && i.participant.isSpeaking ? En(r.participant, i.participant) : r.participant.isSpeaking !== i.participant.isSpeaking ? Tn(r.participant, i.participant) : r.participant.lastSpokeAt !== i.participant.lastSpokeAt ? Cn(r.participant, i.participant) : F(r) !== F(i) ? ji(r, i) : r.participant.isCameraEnabled !== i.participant.isCameraEnabled ? Wi(r, i) : De(r.participant, i.participant)), [...t, ...n];
}
function jo(e) {
  const t = [...e];
  t.sort((r, i) => {
    if (r.isSpeaking && i.isSpeaking)
      return En(r, i);
    if (r.isSpeaking !== i.isSpeaking)
      return Tn(r, i);
    if (r.lastSpokeAt !== i.lastSpokeAt)
      return Cn(r, i);
    const o = r.videoTrackPublications.size > 0, s = i.videoTrackPublications.size > 0;
    return o !== s ? o ? -1 : 1 : De(r, i);
  });
  const n = t.find((r) => r.isLocal);
  if (n) {
    const r = t.indexOf(n);
    r >= 0 && (t.splice(r, 1), t.length > 0 ? t.splice(0, 0, n) : t.push(n));
  }
  return t;
}
function Hi(e, t) {
  return e.reduce(
    (n, r, i) => i % t === 0 ? [...n, [r]] : [...n.slice(0, -1), [...n.slice(-1)[0], r]],
    []
  );
}
function _t(e, t) {
  const n = Math.max(e.length, t.length);
  return new Array(n).fill([]).map((r, i) => [e[i], t[i]]);
}
function Re(e, t, n) {
  return e.filter((r) => !t.map((i) => n(i)).includes(n(r)));
}
function et(e) {
  return e.map((t) => typeof t == "string" || typeof t == "number" ? `${t}` : N(t));
}
function zi(e, t) {
  return {
    dropped: Re(e, t, N),
    added: Re(t, e, N)
  };
}
function Yi(e) {
  return e.added.length !== 0 || e.dropped.length !== 0;
}
function tt(e, t) {
  const n = t.findIndex(
    (r) => N(r) === N(e)
  );
  if (n === -1)
    throw new Error(
      `Element not part of the array: ${N(
        e
      )} not in ${et(t)}`
    );
  return n;
}
function Gi(e, t, n) {
  const r = tt(e, n), i = tt(t, n);
  return n.splice(r, 1, t), n.splice(i, 1, e), n;
}
function Ki(e, t) {
  const n = tt(e, t);
  return t.splice(n, 1), t;
}
function Qi(e, t) {
  return [...t, e];
}
function Ye(e, t) {
  return Hi(e, t);
}
function Wo(e, t, n) {
  let r = Ji(e, t);
  if (r.length < t.length) {
    const s = Re(t, r, N);
    r = [...r, ...s];
  }
  const i = Ye(r, n), o = Ye(t, n);
  if (_t(i, o).forEach(([s, a], c) => {
    if (s && a) {
      const u = Ye(r, n)[c], l = zi(u, a);
      Yi(l) && (_.debug(
        `Detected visual changes on page: ${c}, current: ${et(
          s
        )}, next: ${et(a)}`,
        { changes: l }
      ), l.added.length === l.dropped.length && _t(l.added, l.dropped).forEach(([f, h]) => {
        if (f && h)
          r = Gi(f, h, r);
        else
          throw new Error(
            `For a swap action we need a addition and a removal one is missing: ${f}, ${h}`
          );
      }), l.added.length === 0 && l.dropped.length > 0 && l.dropped.forEach((f) => {
        r = Ki(f, r);
      }), l.added.length > 0 && l.dropped.length === 0 && l.added.forEach((f) => {
        r = Qi(f, r);
      }));
    }
  }), r.length > t.length) {
    const s = Re(r, t, N);
    r = r.filter(
      (a) => !s.map(N).includes(N(a))
    );
  }
  return r;
}
function Ji(e, t) {
  return e.map((n) => {
    const r = t.find(
      (i) => (
        // If the IDs match or ..
        N(n) === N(i) || // ... if the current item is a placeholder and the new item is the track reference can replace it.
        typeof n != "number" && xe(n) && F(i) && _i(n, i)
      )
    );
    return r ?? n;
  });
}
function $(e) {
  return `${bn}-${e}`;
}
function Bo(e) {
  const t = It(e), n = Pn(e.participant).pipe(
    k(() => It(e)),
    R(t)
  );
  return { className: $(
    e.source === D.Source.Camera || e.source === D.Source.ScreenShare ? "participant-media-video" : "participant-media-audio"
  ), trackObserver: n };
}
function It(e) {
  if (F(e))
    return e.publication;
  {
    const { source: t, name: n, participant: r } = e;
    if (t && n)
      return r.getTrackPublications().find((i) => i.source === t && i.trackName === n);
    if (n)
      return r.getTrackPublicationByName(n);
    if (t)
      return r.getTrackPublication(t);
    throw new Error("At least one of source and name needs to be defined");
  }
}
function se(e, ...t) {
  return new O((r) => {
    const i = () => {
      r.next(e);
    };
    return t.forEach((s) => {
      e.on(s, i);
    }), () => {
      t.forEach((s) => {
        e.off(s, i);
      });
    };
  }).pipe(R(e));
}
function ae(e, t) {
  return new O((r) => {
    const i = (...s) => {
      r.next(s);
    };
    return e.on(t, i), () => {
      e.off(t, i);
    };
  });
}
function Vo(e) {
  return ae(e, b.ConnectionStateChanged).pipe(
    k(([t]) => t),
    R(e.state)
  );
}
function Ho(e) {
  return se(
    e,
    b.RoomMetadataChanged,
    b.ConnectionStateChanged
  ).pipe(
    k((n) => ({ name: n.name, metadata: n.metadata }))
  );
}
function zo(e) {
  return ae(e, b.ActiveSpeakersChanged).pipe(
    k(([t]) => t)
  );
}
function Yo(e, t, n = !0) {
  var r;
  const i = () => W(this, null, function* () {
    try {
      const a = yield ht.getLocalDevices(e, n);
      o.next(a);
    } catch (a) {
      t == null || t(a);
    }
  }), o = new re(), s = o.pipe(
    Si(() => {
      var a;
      (a = navigator == null ? void 0 : navigator.mediaDevices) == null || a.removeEventListener("devicechange", i);
    })
  );
  if (typeof window < "u") {
    if (!window.isSecureContext)
      throw new Error(
        "Accessing media devices is available only in secure contexts (HTTPS and localhost), in some or all supporting browsers. See: https://developer.mozilla.org/en-US/docs/Web/API/Navigator/mediaDevices"
      );
    (r = navigator == null ? void 0 : navigator.mediaDevices) == null || r.addEventListener("devicechange", i);
  }
  return Me(
    ht.getLocalDevices(e, n).catch((a) => (t == null || t(a), [])),
    s
  );
}
function qi(e) {
  return ae(e, b.DataReceived);
}
function Xi(e) {
  return ae(e, b.ChatMessage);
}
function Zi(e) {
  return se(e, b.AudioPlaybackStatusChanged).pipe(
    k((n) => ({ canPlayAudio: n.canPlaybackAudio }))
  );
}
function eo(e) {
  return se(e, b.VideoPlaybackStatusChanged).pipe(
    k((n) => ({ canPlayVideo: n.canPlaybackVideo }))
  );
}
function to(e, t) {
  return ae(e, b.ActiveDeviceChanged).pipe(
    dt(([n]) => n === t),
    k(([n, r]) => (_.debug("activeDeviceObservable | RoomEvent.ActiveDeviceChanged", { kind: n, deviceId: r }), r))
  );
}
function Go(e, t) {
  return ae(e, b.ParticipantEncryptionStatusChanged).pipe(
    dt(
      ([, n]) => (t == null ? void 0 : t.identity) === (n == null ? void 0 : n.identity) || !n && (t == null ? void 0 : t.identity) === e.localParticipant.identity
    ),
    k(([n]) => n),
    R(
      t != null && t.isLocal ? t.isE2EEEnabled : !!(t != null && t.isEncrypted)
    )
  );
}
function Ko(e) {
  return ae(e, b.RecordingStatusChanged).pipe(
    k(([t]) => t),
    R(e.isRecording)
  );
}
function me(e, ...t) {
  return new O((r) => {
    const i = () => {
      r.next(e);
    };
    return t.forEach((s) => {
      e.on(s, i);
    }), () => {
      t.forEach((s) => {
        e.off(s, i);
      });
    };
  }).pipe(R(e));
}
function Pn(e) {
  return me(
    e,
    y.TrackMuted,
    y.TrackUnmuted,
    y.ParticipantPermissionsChanged,
    // ParticipantEvent.IsSpeakingChanged,
    y.TrackPublished,
    y.TrackUnpublished,
    y.LocalTrackPublished,
    y.LocalTrackUnpublished,
    y.MediaDevicesError,
    y.TrackSubscriptionStatusChanged
    // ParticipantEvent.ConnectionQualityChanged,
  ).pipe(
    k((n) => {
      const { isMicrophoneEnabled: r, isCameraEnabled: i, isScreenShareEnabled: o } = n, s = n.getTrackPublication(D.Source.Microphone), a = n.getTrackPublication(D.Source.Camera);
      return {
        isCameraEnabled: i,
        isMicrophoneEnabled: r,
        isScreenShareEnabled: o,
        cameraTrack: a,
        microphoneTrack: s,
        participant: n
      };
    })
  );
}
function no(e) {
  return e ? me(
    e,
    y.ParticipantMetadataChanged,
    y.ParticipantNameChanged
  ).pipe(
    k(({ name: n, identity: r, metadata: i }) => ({
      name: n,
      identity: r,
      metadata: i
    })),
    R({
      name: e.name,
      identity: e.identity,
      metadata: e.metadata
    })
  ) : void 0;
}
function ro(e) {
  return Ve(
    e,
    y.ConnectionQualityChanged
  ).pipe(
    k(([n]) => n),
    R(e.connectionQuality)
  );
}
function Ve(e, t) {
  return new O((r) => {
    const i = (...s) => {
      r.next(s);
    };
    return e.on(t, i), () => {
      e.off(t, i);
    };
  });
}
function io(e) {
  var t, n, r, i;
  return me(
    e.participant,
    y.TrackMuted,
    y.TrackUnmuted,
    y.TrackSubscribed,
    y.TrackUnsubscribed,
    y.LocalTrackPublished,
    y.LocalTrackUnpublished
  ).pipe(
    k((o) => {
      var s, a;
      const c = (s = e.publication) != null ? s : o.getTrackPublication(e.source);
      return (a = c == null ? void 0 : c.isMuted) != null ? a : !0;
    }),
    R(
      (i = (r = (t = e.publication) == null ? void 0 : t.isMuted) != null ? r : (n = e.participant.getTrackPublication(e.source)) == null ? void 0 : n.isMuted) != null ? i : !0
    )
  );
}
function Qo(e) {
  return Ve(e, y.IsSpeakingChanged).pipe(
    k(([t]) => t)
  );
}
function Jo(e, t = {}) {
  var n;
  let r;
  const i = new O((c) => (r = c, () => a.unsubscribe())).pipe(R(Array.from(e.remoteParticipants.values()))), o = (n = t.additionalRoomEvents) != null ? n : yn, s = Array.from(
    /* @__PURE__ */ new Set([
      b.ParticipantConnected,
      b.ParticipantDisconnected,
      b.ConnectionStateChanged,
      ...o
    ])
  ), a = se(e, ...s).subscribe(
    (c) => r == null ? void 0 : r.next(Array.from(c.remoteParticipants.values()))
  );
  return e.remoteParticipants.size > 0 && (r == null || r.next(Array.from(e.remoteParticipants.values()))), i;
}
function qo(e, t, n = {}) {
  var r;
  const i = (r = n.additionalEvents) != null ? r : wn;
  return se(
    e,
    b.ParticipantConnected,
    b.ParticipantDisconnected,
    b.ConnectionStateChanged
  ).pipe(
    gn((s) => {
      const a = s.getParticipantByIdentity(t);
      return a ? me(a, ...i) : new O((c) => c.next(void 0));
    }),
    R(e.getParticipantByIdentity(t))
  );
}
function Xo(e) {
  return Ve(
    e,
    y.ParticipantPermissionsChanged
  ).pipe(
    k(() => e.permissions),
    R(e.permissions)
  );
}
function Zo(e, { kind: t, identity: n }, r = {}) {
  var i;
  const o = (i = r.additionalEvents) != null ? i : wn, s = (c) => {
    let u = !0;
    return t && (u = u && c.kind === t), n && (u = u && c.identity === n), u;
  };
  return se(
    e,
    b.ParticipantConnected,
    b.ParticipantDisconnected,
    b.ConnectionStateChanged
  ).pipe(
    gn((c) => {
      const u = Array.from(c.remoteParticipants.values()).find(
        (l) => s(l)
      );
      return u ? me(u, ...o) : new O((l) => l.next(void 0));
    }),
    R(Array.from(e.remoteParticipants.values()).find((c) => s(c)))
  );
}
function es(e) {
  return typeof e > "u" ? new O() : Ve(e, y.AttributesChanged).pipe(
    k(([t]) => ({
      changed: t,
      attributes: e.attributes
    })),
    R({ changed: e.attributes, attributes: e.attributes })
  );
}
function ts(e, t, n, r, i) {
  const { localParticipant: o } = t, s = (f, h) => {
    let d = !1;
    switch (f) {
      case D.Source.Camera:
        d = h.isCameraEnabled;
        break;
      case D.Source.Microphone:
        d = h.isMicrophoneEnabled;
        break;
      case D.Source.ScreenShare:
        d = h.isScreenShareEnabled;
        break;
    }
    return d;
  }, a = Pn(o).pipe(
    k((f) => s(e, f.participant)),
    R(s(e, o))
  ), c = new re(), u = (f, h) => W(this, null, function* () {
    try {
      switch (h ?? (h = n), c.next(!0), e) {
        case D.Source.Camera:
          return yield o.setCameraEnabled(
            f ?? !o.isCameraEnabled,
            h,
            r
          ), o.isCameraEnabled;
        case D.Source.Microphone:
          return yield o.setMicrophoneEnabled(
            f ?? !o.isMicrophoneEnabled,
            h,
            r
          ), o.isMicrophoneEnabled;
        case D.Source.ScreenShare:
          return yield o.setScreenShareEnabled(
            f ?? !o.isScreenShareEnabled,
            h,
            r
          ), o.isScreenShareEnabled;
        default:
          throw new TypeError("Tried to toggle unsupported source");
      }
    } catch (d) {
      if (i && d instanceof Error) {
        i == null || i(d);
        return;
      } else
        throw d;
    } finally {
      c.next(!1);
    }
  });
  return {
    className: $("button"),
    toggle: u,
    enabledObserver: a,
    pendingObserver: c.asObservable()
  };
}
function ns() {
  let e = !1;
  const t = new re(), n = new re(), r = (o) => W(this, null, function* () {
    n.next(!0), e = o ?? !e, t.next(e), n.next(!1);
  });
  return {
    className: $("button"),
    toggle: r,
    enabledObserver: t.asObservable(),
    pendingObserver: n.asObservable()
  };
}
function rs(e, t, n) {
  const r = new tn(void 0), i = to(t, e), o = (a, ...c) => W(this, [a, ...c], function* (u, l = {}) {
    var f, h, d;
    if (t) {
      _.debug(`Switching active device of kind "${e}" with id ${u}.`), yield t.switchActiveDevice(e, u, l.exact);
      const m = (f = t.getActiveDevice(e)) != null ? f : u;
      m !== u && u !== "default" && _.info(
        `We tried to select the device with id (${u}), but the browser decided to select the device with id (${m}) instead.`
      );
      let p;
      e === "audioinput" ? p = (h = t.localParticipant.getTrackPublication(D.Source.Microphone)) == null ? void 0 : h.track : e === "videoinput" && (p = (d = t.localParticipant.getTrackPublication(D.Source.Camera)) == null ? void 0 : d.track);
      const g = u === "default" && !p || u === "default" && (p == null ? void 0 : p.mediaStreamTrack.label.startsWith("Default"));
      r.next(g ? u : m);
    }
  });
  return {
    className: $("media-device-select"),
    activeDeviceObservable: i,
    setActiveMediaDevice: o
  };
}
function is(e) {
  const t = (r) => {
    e.disconnect(r);
  };
  return { className: $("disconnect-button"), disconnect: t };
}
function os(e) {
  const t = $("connection-quality"), n = ro(e);
  return { className: t, connectionQualityObserver: n };
}
function ss(e) {
  let t = "track-muted-indicator-camera";
  switch (e.source) {
    case D.Source.Camera:
      t = "track-muted-indicator-camera";
      break;
    case D.Source.Microphone:
      t = "track-muted-indicator-microphone";
      break;
  }
  const n = $(t), r = io(e);
  return { className: n, mediaMutedObserver: r };
}
function as(e) {
  return { className: "lk-participant-name", infoObserver: no(e) };
}
function cs() {
  return {
    className: $("participant-tile")
  };
}
var Mt = {
  CHAT: "lk-chat-topic",
  CHAT_UPDATE: "lk-chat-update-topic"
};
function nt(e, t) {
  return W(this, arguments, function* (n, r, i = {}) {
    const { reliable: o, destinationIdentities: s, topic: a } = i;
    yield n.publishData(r, {
      destinationIdentities: s,
      topic: a,
      reliable: o
    });
  });
}
function oo(e, t, n) {
  const r = Array.isArray(t) ? t : [t], i = qi(e).pipe(
    dt(
      ([, , , c]) => t === void 0 || c !== void 0 && r.includes(c)
    ),
    k(([c, u, , l]) => {
      const f = {
        payload: c,
        topic: l,
        from: u
      };
      return n == null || n(f), f;
    })
  );
  let o;
  const s = new O((c) => {
    o = c;
  });
  return { messageObservable: i, isSendingObservable: s, send: (c, ...u) => W(this, [c, ...u], function* (l, f = {}) {
    o.next(!0);
    try {
      yield nt(e.localParticipant, l, Q({ topic: r[0] }, f));
    } finally {
      o.next(!1);
    }
  }) };
}
function so(e) {
  return { chatObservable: Xi(e), send: (i) => W(this, null, function* () {
    return yield e.localParticipant.sendChatMessage(i);
  }), edit: (i, o) => W(this, null, function* () {
    return yield e.localParticipant.editChatMessage(i, o);
  }) };
}
var ao = new TextEncoder(), co = new TextDecoder(), Pe = /* @__PURE__ */ new Map(), uo = (e) => ao.encode(JSON.stringify(e)), lo = (e) => JSON.parse(co.decode(e));
function us(e, t) {
  var n, r;
  const i = new re(), o = () => {
    var T, x, I;
    return ((T = e.serverInfo) == null ? void 0 : T.edition) === 1 || !!((x = e.serverInfo) != null && x.version) && Un((I = e.serverInfo) == null ? void 0 : I.version, "1.17.2") > 0;
  }, { messageDecoder: s, messageEncoder: a, channelTopic: c, updateChannelTopic: u } = t ?? {}, l = c ?? Mt.CHAT, f = u ?? Mt.CHAT_UPDATE;
  let h = !1;
  Pe.has(e) || (h = !0);
  const d = (n = Pe.get(e)) != null ? n : /* @__PURE__ */ new Map(), m = (r = d.get(l)) != null ? r : new re();
  if (d.set(l, m), Pe.set(e, d), h) {
    const { messageObservable: T } = oo(e, [l, f]);
    T.pipe(At(i)).subscribe(m);
  }
  const { chatObservable: p, send: g } = so(e), v = s ?? lo, E = gi(
    m.pipe(
      k((T) => {
        const x = v(T.payload), I = ye(Q({}, x), { from: T.from });
        if (!fo(I))
          return I;
      })
    ),
    p.pipe(
      k(([T, x]) => ye(Q({}, T), { from: x }))
    )
  ).pipe(
    xi((T, x) => {
      var I;
      if (!x)
        return T;
      if ("id" in x && T.find((w) => {
        var S, V;
        return ((S = w.from) == null ? void 0 : S.identity) === ((V = x.from) == null ? void 0 : V.identity) && w.id === x.id;
      })) {
        const w = T.findIndex((S) => S.id === x.id);
        if (w > -1) {
          const S = T[w];
          T[w] = ye(Q({}, x), {
            timestamp: S.timestamp,
            editTimestamp: (I = x.editTimestamp) != null ? I : x.timestamp
          });
        }
        return [...T];
      }
      return [...T, x];
    }, []),
    At(i)
  ), P = new tn(!1), L = a ?? uo, C = (T) => W(this, null, function* () {
    P.next(!0);
    try {
      const x = yield g(T), I = L(ye(Q({}, x), {
        ignore: o()
      }));
      return yield nt(e.localParticipant, I, {
        reliable: !0,
        topic: l
      }), x;
    } finally {
      P.next(!1);
    }
  }), ie = (T, x) => W(this, null, function* () {
    const I = Date.now(), w = typeof x == "string" ? { id: x, message: "", timestamp: I } : x;
    P.next(!0);
    try {
      const S = yield e.localParticipant.editChatMessage(T, w), V = L(S);
      return yield nt(e.localParticipant, V, {
        topic: f,
        reliable: !0
      }), S;
    } finally {
      P.next(!1);
    }
  });
  function K() {
    i.next(), i.complete(), Pe.delete(e);
  }
  return e.once(b.Disconnected, K), {
    messageObservable: E,
    isSendingObservable: P,
    send: C,
    update: ie
  };
}
function fo(e) {
  return e.ignore == !0;
}
function ls() {
  const e = (n) => W(this, null, function* () {
    _.info("Start Audio for room: ", n), yield n.startAudio();
  });
  return { className: $("start-audio-button"), roomAudioPlaybackAllowedObservable: Zi, handleStartAudioPlayback: e };
}
function fs() {
  const e = (n) => W(this, null, function* () {
    _.info("Start Video for room: ", n), yield n.startVideo();
  });
  return { className: $("start-audio-button"), roomVideoPlaybackAllowedObservable: eo, handleStartVideoPlayback: e };
}
function ds() {
  return { className: [$("button"), $("chat-toggle")].join(" ") };
}
function ps() {
  return { className: [$("button"), $("focus-toggle-button")].join(" ") };
}
function hs() {
  return { className: "lk-clear-pin-button lk-button" };
}
function vs() {
  return { className: "lk-room-container" };
}
function Dt(e, t, n = !0) {
  const i = [e.localParticipant, ...Array.from(e.remoteParticipants.values())], o = [];
  return i.forEach((s) => {
    t.forEach((a) => {
      const c = Array.from(
        s.trackPublications.values()
      ).filter(
        (u) => u.source === a && // either return all or only the ones that are subscribed
        (!n || u.track)
      ).map((u) => ({
        participant: s,
        publication: u,
        source: u.source
      }));
      o.push(...c);
    });
  }), { trackReferences: o, participants: i };
}
function Rt(e, t, n = !1) {
  const { sources: r, kind: i, name: o } = t;
  return Array.from(e.trackPublications.values()).filter(
    (a) => (!r || r.includes(a.source)) && (!i || a.kind === i) && (!o || a.trackName === o) && // either return all or only the ones that are subscribed
    (!n || a.track)
  ).map((a) => ({
    participant: e,
    publication: a,
    source: a.source
  }));
}
function ms(e, t, n) {
  var r, i;
  const o = (r = n.additionalRoomEvents) != null ? r : yn, s = (i = n.onlySubscribed) != null ? i : !0, a = Array.from(
    (/* @__PURE__ */ new Set([
      b.ParticipantConnected,
      b.ParticipantDisconnected,
      b.ConnectionStateChanged,
      b.LocalTrackPublished,
      b.LocalTrackUnpublished,
      b.TrackPublished,
      b.TrackUnpublished,
      b.TrackSubscriptionStatusChanged,
      ...o
    ])).values()
  );
  return se(e, ...a).pipe(
    k((u) => {
      const l = Dt(u, t, s);
      return _.debug(`TrackReference[] was updated. (length ${l.trackReferences.length})`, l), l;
    }),
    R(Dt(e, t, s))
  );
}
function gs(e, t) {
  return me(e, ...Ri).pipe(
    k((r) => {
      const i = Rt(r, t);
      return _.debug(`TrackReference[] was updated. (length ${i.length})`, i), i;
    }),
    R(Rt(e, t))
  );
}
function An(e, t) {
  return new O((r) => {
    const i = (...s) => {
      r.next(s);
    };
    return e.on(t, i), () => {
      e.off(t, i);
    };
  });
}
function bs(e) {
  return An(e, Nt.TranscriptionReceived);
}
function ys(e) {
  return An(e, Nt.TimeSyncUpdate).pipe(
    k(([t]) => t)
  );
}
function ws(e, t = 1e3) {
  if (e === null) return Ct(!1);
  const n = Ze(e, "mousemove", { passive: !0 }).pipe(k(() => !0)), r = n.pipe(
    ii({
      each: t,
      with: () => Me(Ct(!1), r.pipe(Ei(n)))
    }),
    yi()
  );
  return r;
}
function po(e, t) {
  if (typeof localStorage > "u") {
    _.error("Local storage is not available.");
    return;
  }
  try {
    if (t) {
      const n = Object.fromEntries(
        Object.entries(t).filter(([, r]) => r !== "")
      );
      localStorage.setItem(e, JSON.stringify(n));
    }
  } catch (n) {
    _.error(`Error setting item to local storage: ${n}`);
  }
}
function ho(e) {
  if (typeof localStorage > "u") {
    _.error("Local storage is not available.");
    return;
  }
  try {
    const t = localStorage.getItem(e);
    if (!t) {
      _.warn(`Item with key ${e} does not exist in local storage.`);
      return;
    }
    return JSON.parse(t);
  } catch (t) {
    _.error(`Error getting item from local storage: ${t}`);
    return;
  }
}
function vo(e) {
  return {
    load: () => ho(e),
    save: (t) => po(e, t)
  };
}
var mo = `${bn}-user-choices`, be = {
  videoEnabled: !0,
  audioEnabled: !0,
  videoDeviceId: "default",
  audioDeviceId: "default",
  username: ""
}, { load: go, save: bo } = vo(mo);
function Ss(e, t = !1) {
  t !== !0 && bo(e);
}
function xs(e, t = !1) {
  var n, r, i, o, s;
  const a = {
    videoEnabled: (n = e == null ? void 0 : e.videoEnabled) != null ? n : be.videoEnabled,
    audioEnabled: (r = e == null ? void 0 : e.audioEnabled) != null ? r : be.audioEnabled,
    videoDeviceId: (i = e == null ? void 0 : e.videoDeviceId) != null ? i : be.videoDeviceId,
    audioDeviceId: (o = e == null ? void 0 : e.audioDeviceId) != null ? o : be.audioDeviceId,
    username: (s = e == null ? void 0 : e.username) != null ? s : be.username
  };
  if (t)
    return a;
  {
    const c = go();
    return Q(Q({}, a), c ?? {});
  }
}
function kn(e, t) {
  if (t.msg === "show_chat")
    return { ...e, showChat: !0, unreadMessages: 0 };
  if (t.msg === "hide_chat")
    return { ...e, showChat: !1 };
  if (t.msg === "toggle_chat") {
    const n = { ...e, showChat: !e.showChat };
    return n.showChat === !0 && (n.unreadMessages = 0), n;
  } else return t.msg === "unread_msg" ? { ...e, unreadMessages: t.count } : t.msg === "toggle_settings" ? { ...e, showSettings: !e.showSettings } : { ...e };
}
function On(e, t) {
  return t.msg === "set_pin" ? [t.trackReference] : t.msg === "clear_pin" ? [] : { ...e };
}
const Ln = M.createContext(void 0);
function Es() {
  const e = M.useContext(Ln);
  if (!e)
    throw Error("Tried to access LayoutContext context outside a LayoutContextProvider provider.");
  return e;
}
function Ts(e) {
  const t = yo();
  if (e ?? (e = t), !e)
    throw Error("Tried to access LayoutContext context outside a LayoutContextProvider provider.");
  return e;
}
function Cs() {
  const [e, t] = M.useReducer(On, Sn), [n, r] = M.useReducer(kn, xn);
  return {
    pin: { dispatch: t, state: e },
    widget: { dispatch: r, state: n }
  };
}
function Ps(e) {
  const [t, n] = M.useReducer(On, Sn), [r, i] = M.useReducer(kn, xn);
  return e ?? {
    pin: { dispatch: n, state: t },
    widget: { dispatch: i, state: r }
  };
}
function yo() {
  return M.useContext(Ln);
}
const _n = M.createContext(
  void 0
);
function As() {
  const e = M.useContext(_n);
  if (!e)
    throw Error("tried to access track context outside of track context provider");
  return e;
}
function In() {
  return M.useContext(_n);
}
function ks(e) {
  const t = In(), n = e ?? t;
  if (!n)
    throw new Error(
      "No TrackRef, make sure you are inside a TrackRefContext or pass the TrackRef explicitly"
    );
  return n;
}
const Mn = M.createContext(void 0);
function Os() {
  const e = M.useContext(Mn);
  if (!e)
    throw Error("tried to access participant context outside of participant context provider");
  return e;
}
function wo() {
  return M.useContext(Mn);
}
function Ls(e) {
  const t = wo(), n = In(), r = e ?? t ?? (n == null ? void 0 : n.participant);
  if (!r)
    throw new Error(
      "No participant provided, make sure you are inside a participant context or pass the participant explicitly"
    );
  return r;
}
const Dn = M.createContext(void 0);
function _s() {
  const e = M.useContext(Dn);
  if (!e)
    throw Error("tried to access room context outside of livekit room component");
  return e;
}
function So() {
  return M.useContext(Dn);
}
function Is(e) {
  const t = So(), n = e ?? t;
  if (!n)
    throw new Error(
      "No room provided, make sure you are inside a Room context or pass the room explicitly"
    );
  return n;
}
const xo = M.createContext(void 0);
function Ms(e) {
  const t = M.useContext(xo);
  if (e === !0) {
    if (t)
      return t;
    throw Error("tried to access feature context, but none is present");
  }
  return t;
}
export {
  Ss as $,
  Wo as A,
  wo as B,
  no as C,
  cs as D,
  Jo as E,
  Ts as F,
  Do as G,
  qo as H,
  Zo as I,
  Ho as J,
  zo as K,
  xo as L,
  jo as M,
  ls as N,
  fs as O,
  ds as P,
  ss as Q,
  Dn as R,
  ts as S,
  ns as T,
  Fi as U,
  ms as V,
  Uo as W,
  It as X,
  Bo as Y,
  us as Z,
  xs as _,
  Es as a,
  Go as a0,
  F as a1,
  gs as a2,
  ys as a3,
  bs as a4,
  $o as a5,
  No as a6,
  es as a7,
  Ko as a8,
  In as a9,
  Ln as aa,
  as as ab,
  br as ac,
  yr as ad,
  Mn as ae,
  _n as af,
  Ms as ag,
  ws as ah,
  Po as ai,
  Ps as aj,
  _o as ak,
  Lo as al,
  ko as am,
  Oo as an,
  Ro as ao,
  Cs as ap,
  To as aq,
  Ao as ar,
  Io as as,
  Mo as at,
  Os as au,
  As as av,
  hs as b,
  Ls as c,
  os as d,
  Vo as e,
  _s as f,
  oo as g,
  is as h,
  ks as i,
  yo as j,
  ps as k,
  _ as l,
  Co as m,
  $i as n,
  io as o,
  N as p,
  Qo as q,
  Zi as r,
  vs as s,
  Pn as t,
  Is as u,
  Xo as v,
  So as w,
  Yo as x,
  rs as y,
  Fo as z
};
//# sourceMappingURL=contexts-B7YgC7ji.mjs.map
