(()=>{var e={};e.id=856,e.ids=[856],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13073:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>u,tree:()=>o});var a=r(65239),s=r(48088),d=r(88170),n=r.n(d),i=r(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let o={children:["",{children:["dashboard",{children:["numbers",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,32110)),"E:\\quickcall\\frontend\\quickcall-frontend\\src\\app\\dashboard\\numbers\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,63144)),"E:\\quickcall\\frontend\\quickcall-frontend\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"E:\\quickcall\\frontend\\quickcall-frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["E:\\quickcall\\frontend\\quickcall-frontend\\src\\app\\dashboard\\numbers\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/dashboard/numbers/page",pathname:"/dashboard/numbers",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32110:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\numbers\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\quickcall\\frontend\\quickcall-frontend\\src\\app\\dashboard\\numbers\\page.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},47264:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var a=r(60687),s=r(43210);function d(){let[e,t]=(0,s.useState)([{id:"1",number:"+****************",label:"Main Outbound",status:"active",dateAdded:"May 15, 2025"},{id:"2",number:"+1 (555) 987-6543",label:"Support Line",status:"active",dateAdded:"May 10, 2025"},{id:"3",number:"+1 (555) 456-7890",label:"Sales Line",status:"inactive",dateAdded:"April 28, 2025"}]),[r,d]=(0,s.useState)(!1),[n,i]=(0,s.useState)(""),[l,o]=(0,s.useState)(""),[c,x]=(0,s.useState)({type:"",text:""}),[u,m]=(0,s.useState)(""),p=e.filter(e=>e.number.includes(u)||e.label.toLowerCase().includes(u.toLowerCase())),g=e=>{t(t=>t.map(t=>t.id===e?{...t,status:"active"===t.status?"inactive":"active"}:t))};return(0,a.jsxs)("div",{className:"p-6 max-w-7xl mx-auto",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-semibold",children:"Phone Numbers"}),(0,a.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"Manage your outbound caller IDs"})]}),(0,a.jsx)("div",{children:(0,a.jsxs)("button",{onClick:()=>document.getElementById("add-number-modal")?.classList.remove("hidden"),className:"bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md font-medium transition-colors flex items-center gap-2",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z",clipRule:"evenodd"})}),"Add Number"]})})]}),c.text&&(0,a.jsx)("div",{className:`p-4 mb-6 rounded-md ${"success"===c.type?"bg-green-50 dark:bg-green-900/20 text-green-800 dark:text-green-200":"error"===c.type?"bg-red-50 dark:bg-red-900/20 text-red-800 dark:text-red-200":""}`,children:c.text}),(0,a.jsx)("div",{className:"bg-white dark:bg-[#1A1A1A] rounded-lg border border-gray-200 dark:border-gray-800 p-4 mb-6",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none",children:(0,a.jsx)("svg",{className:"w-4 h-4 text-gray-500 dark:text-gray-400","aria-hidden":"true",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"})})}),(0,a.jsx)("input",{type:"search",className:"block w-full p-2 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-white dark:bg-[#1A1A1A] dark:border-gray-700 dark:text-white focus:ring-indigo-500 focus:border-indigo-500",placeholder:"Search phone numbers...",value:u,onChange:e=>m(e.target.value)})]})}),(0,a.jsxs)("div",{className:"bg-white dark:bg-[#1A1A1A] rounded-lg border border-gray-200 dark:border-gray-800 overflow-hidden",children:[(0,a.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-800 px-6 py-4",children:(0,a.jsx)("h2",{className:"font-medium",children:"Your Phone Numbers"})}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-800",children:[(0,a.jsx)("thead",{className:"bg-gray-50 dark:bg-[#222222]",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Phone Number"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Label"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Date Added"}),(0,a.jsx)("th",{scope:"col",className:"relative px-6 py-3",children:(0,a.jsx)("span",{className:"sr-only",children:"Actions"})})]})}),(0,a.jsx)("tbody",{className:"bg-white dark:bg-[#1A1A1A] divide-y divide-gray-200 dark:divide-gray-800",children:p.length>0?p.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50 dark:hover:bg-[#222222]",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:e.number}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400",children:e.label}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${"active"===e.status?"bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400":"bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400"}`,children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400",children:e.dateAdded}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:[(0,a.jsx)("button",{onClick:()=>g(e.id),className:"text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300 mr-3",children:"active"===e.status?"Deactivate":"Activate"}),(0,a.jsx)("button",{className:"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{d:"M6 10a2 2 0 11-4 0 2 2 0 014 0zM12 10a2 2 0 11-4 0 2 2 0 014 0zM16 12a2 2 0 100-4 2 2 0 000 4z"})})})]})]},e.id)):(0,a.jsx)("tr",{children:(0,a.jsx)("td",{colSpan:5,className:"px-6 py-10 text-center text-gray-500 dark:text-gray-400",children:"No phone numbers found matching your criteria"})})})]})})]}),(0,a.jsx)("div",{id:"add-number-modal",className:"hidden fixed inset-0 bg-black bg-opacity-50 dark:bg-opacity-70 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white dark:bg-[#1A1A1A] rounded-lg max-w-md w-full mx-4",children:[(0,a.jsxs)("div",{className:"border-b border-gray-200 dark:border-gray-800 px-6 py-4 flex justify-between items-center",children:[(0,a.jsx)("h3",{className:"font-medium",children:"Add New Phone Number"}),(0,a.jsx)("button",{onClick:()=>document.getElementById("add-number-modal")?.classList.add("hidden"),className:"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})]}),(0,a.jsxs)("div",{className:"p-6 space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"phone-number",className:"block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300",children:"Phone Number"}),(0,a.jsx)("input",{type:"text",id:"phone-number",value:n,onChange:e=>i(e.target.value),placeholder:"+****************",className:"block w-full p-2 text-sm text-gray-900 border border-gray-300 rounded-md bg-white dark:bg-[#1A1A1A] dark:border-gray-700 dark:text-white focus:ring-indigo-500 focus:border-indigo-500"}),(0,a.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:"Format: +[country code][number], e.g., +33759215427"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"label",className:"block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300",children:"Label"}),(0,a.jsx)("input",{type:"text",id:"label",value:l,onChange:e=>o(e.target.value),placeholder:"Main Outbound",className:"block w-full p-2 text-sm text-gray-900 border border-gray-300 rounded-md bg-white dark:bg-[#1A1A1A] dark:border-gray-700 dark:text-white focus:ring-indigo-500 focus:border-indigo-500"})]}),(0,a.jsxs)("div",{className:"flex justify-end gap-3 mt-6",children:[(0,a.jsx)("button",{onClick:()=>document.getElementById("add-number-modal")?.classList.add("hidden"),className:"px-4 py-2 bg-white dark:bg-[#222222] border border-gray-300 dark:border-gray-700 rounded-md text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800",children:"Cancel"}),(0,a.jsx)("button",{onClick:()=>{if(!n||!l){x({type:"error",text:"Please provide both a phone number and label"}),setTimeout(()=>x({type:"",text:""}),3e3);return}d(!0),setTimeout(()=>{let r=(e.length+1).toString();t([...e,{id:r,number:n,label:l,status:"active",dateAdded:new Date().toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})}]),i(""),o(""),d(!1),x({type:"success",text:"Phone number added successfully"}),setTimeout(()=>{x({type:"",text:""})},3e3)},1e3)},disabled:r,className:"bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors flex items-center gap-2 disabled:opacity-50",children:r?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("svg",{className:"animate-spin h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Adding..."]}):"Add Number"})]})]})]})})]})}},52102:(e,t,r)=>{Promise.resolve().then(r.bind(r,47264))},60350:(e,t,r)=>{Promise.resolve().then(r.bind(r,32110))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[447,771,658,418,310],()=>r(13073));module.exports=a})();