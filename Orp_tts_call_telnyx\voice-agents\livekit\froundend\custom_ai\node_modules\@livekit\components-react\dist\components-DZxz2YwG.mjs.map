{"version": 3, "file": "components-DZxz2YwG.mjs", "sources": ["../src/components/controls/ClearPinButton.tsx", "../src/components/ConnectionState.tsx", "../src/components/controls/ChatToggle.tsx", "../src/components/controls/DisconnectButton.tsx", "../src/assets/icons/CameraDisabledIcon.tsx", "../src/assets/icons/CameraIcon.tsx", "../src/assets/icons/ChatCloseIcon.tsx", "../src/assets/icons/ChatIcon.tsx", "../src/assets/icons/Chevron.tsx", "../src/assets/icons/FocusToggleIcon.tsx", "../src/assets/icons/GearIcon.tsx", "../src/assets/icons/LeaveIcon.tsx", "../src/assets/icons/LockLockedIcon.tsx", "../src/assets/icons/MicDisabledIcon.tsx", "../src/assets/icons/MicIcon.tsx", "../src/assets/icons/QualityExcellentIcon.tsx", "../src/assets/icons/QualityGoodIcon.tsx", "../src/assets/icons/QualityPoorIcon.tsx", "../src/assets/icons/QualityUnknownIcon.tsx", "../src/assets/icons/ScreenShareIcon.tsx", "../src/assets/icons/ScreenShareStopIcon.tsx", "../src/assets/icons/SpinnerIcon.tsx", "../src/assets/icons/UnfocusToggleIcon.tsx", "../src/components/controls/FocusToggle.tsx", "../src/components/controls/MediaDeviceSelect.tsx", "../src/components/controls/StartAudio.tsx", "../src/components/controls/StartMediaButton.tsx", "../src/assets/icons/util.tsx", "../src/components/controls/TrackToggle.tsx", "../src/components/participant/ConnectionQualityIndicator.tsx", "../src/components/participant/ParticipantName.tsx", "../src/components/participant/TrackMutedIndicator.tsx", "../src/assets/images/ParticipantPlaceholder.tsx", "../src/hooks/useMediaTrackBySourceOrName.ts", "../../../node_modules/.pnpm/lodash.debounce@4.0.8/node_modules/lodash.debounce/index.js", "../../../node_modules/.pnpm/usehooks-ts@3.1.0_react@18.3.1/node_modules/usehooks-ts/dist/index.js", "../src/components/participant/VideoTrack.tsx", "../src/components/participant/AudioTrack.tsx", "../src/components/participant/ParticipantTile.tsx", "../src/components/layout/FocusLayout.tsx", "../src/components/TrackLoop.tsx", "../src/components/controls/PaginationControl.tsx", "../src/components/controls/PaginationIndicator.tsx", "../src/components/layout/GridLayout.tsx", "../src/components/layout/CarouselLayout.tsx", "../src/components/layout/LayoutContextProvider.tsx", "../src/components/participant/AudioVisualizer.tsx", "../src/components/ParticipantLoop.tsx", "../src/components/RoomAudioRenderer.tsx", "../src/components/RoomName.tsx", "../src/components/Toast.tsx", "../src/components/participant/animationSequences/connectingSequence.ts", "../src/components/participant/animationSequences/listeningSequence.ts", "../src/components/participant/animators/useBarAnimator.ts", "../src/components/participant/BarVisualizer.tsx", "../src/components/participant/ParticipantAudioTile.tsx", "../src/components/ConnectionStateToast.tsx", "../src/components/ChatEntry.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { useClearPinButton } from '../../hooks';\n\n/** @public */\nexport interface ClearPinButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {}\n\n/**\n * The `ClearPinButton` is a basic html button with the added ability to signal\n * the `LayoutContext` that it should display the grid view again.\n * @remarks\n * This component works only inside a `LayoutContext`.\n *\n * @example\n * ```tsx\n * <LiveKitRoom>\n *   <ClearPinButton>Back to grid view</ClearPinButton>\n * </LiveKitRoom>\n * ```\n * @public\n */\nexport const ClearPinButton: (\n  props: ClearPinButtonProps & React.RefAttributes<HTMLButtonElement>,\n) => React.ReactNode = /* @__PURE__ */ React.forwardRef<HTMLButtonElement, ClearPinButtonProps>(\n  function ClearPinButton(props: ClearPinButtonProps, ref) {\n    const { buttonProps } = useClearPinButton(props);\n    return (\n      <button ref={ref} {...buttonProps}>\n        {props.children}\n      </button>\n    );\n  },\n);\n", "import type { Room } from 'livekit-client';\nimport * as React from 'react';\nimport { useConnectionState } from '../hooks';\n\n/** @public */\nexport interface ConnectionStatusProps extends React.HTMLAttributes<HTMLDivElement> {\n  /**\n   * The room from which the connection status should be displayed.\n   */\n  room?: Room;\n}\n\n/**\n * The `ConnectionState` component displays the connection status of the room as strings\n * (`\"connected\" | \"connecting\" | \"disconnected\" | \"reconnecting\"`).\n *\n * @example\n * ```tsx\n * <LiveKitRoom>\n *   <ConnectionState />\n * </LiveKitRoom>\n * ```\n * @public\n */\nexport const ConnectionState: (\n  props: ConnectionStatusProps & React.RefAttributes<HTMLDivElement>,\n) => React.ReactNode = /* @__PURE__ */ React.forwardRef<HTMLDivElement, ConnectionStatusProps>(\n  function ConnectionState({ room, ...props }: ConnectionStatusProps, ref) {\n    const connectionState = useConnectionState(room);\n    return (\n      <div ref={ref} {...props}>\n        {connectionState}\n      </div>\n    );\n  },\n);\n", "import * as React from 'react';\nimport { useChatToggle } from '../../hooks';\n\n/** @public */\nexport interface ChatToggleProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {}\n\n/**\n * The `ChatToggle` component is a button that toggles the visibility of the `Chat` component.\n * @remarks\n * For the component to have any effect it has to live inside a `LayoutContext` context.\n *\n * @example\n * ```tsx\n * <LiveKitRoom>\n *   <ToggleChat />\n * </LiveKitRoom>\n * ```\n * @public\n */\nexport const ChatToggle: (\n  props: ChatToggleProps & React.RefAttributes<HTMLButtonElement>,\n) => React.ReactNode = /* @__PURE__ */ React.forwardRef<HTMLButtonElement, ChatToggleProps>(\n  function ChatToggle(props: ChatToggleProps, ref) {\n    const { mergedProps } = useChatToggle({ props });\n\n    return (\n      <button ref={ref} {...mergedProps}>\n        {props.children}\n      </button>\n    );\n  },\n);\n", "import * as React from 'react';\nimport { useDisconnectButton } from '../../hooks';\n\n/** @public */\nexport interface DisconnectButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  stopTracks?: boolean;\n}\n\n/**\n * The `DisconnectButton` is a basic html button with the added ability to disconnect from a LiveKit room.\n * Normally this is the big red button that allows end users to leave the video or audio call.\n *\n * @example\n * ```tsx\n * <LiveKitRoom>\n *   <DisconnectButton>Leave room</DisconnectButton>\n * </LiveKitRoom>\n * ```\n * @public\n */\nexport const DisconnectButton: (\n  props: DisconnectButtonProps & React.RefAttributes<HTMLButtonElement>,\n) => React.ReactNode = /* @__PURE__ */ React.forwardRef<HTMLButtonElement, DisconnectButtonProps>(\n  function DisconnectButton(props: DisconnectButtonProps, ref) {\n    const { buttonProps } = useDisconnectButton(props);\n    return (\n      <button ref={ref} {...buttonProps}>\n        {props.children}\n      </button>\n    );\n  },\n);\n", "/**\n * WARNING: This file was auto-generated by svgr. Do not edit.\n */\nimport * as React from 'react';\nimport type { SVGProps } from 'react';\n/**\n * @internal\n */\nconst SvgCameraDisabledIcon = (props: SVGProps<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" width={16} height={16} fill=\"currentColor\" {...props}>\n    <path d=\"M1.354.646a.5.5 0 1 0-.708.708l14 14a.5.5 0 0 0 .708-.708L11 10.293V4.5A1.5 1.5 0 0 0 9.5 3H3.707zM0 4.5a1.5 1.5 0 0 1 .943-1.393l9.532 9.533c-.262.224-.603.36-.975.36h-8A1.5 1.5 0 0 1 0 11.5z\" />\n    <path d=\"m15.2 3.6-2.8 2.1a1 1 0 0 0-.4.8v3a1 1 0 0 0 .4.8l2.8 2.1a.5.5 0 0 0 .8-.4V4a.5.5 0 0 0-.8-.4z\" />\n  </svg>\n);\nexport default SvgCameraDisabledIcon;\n", "/**\n * WARNING: This file was auto-generated by svgr. Do not edit.\n */\nimport * as React from 'react';\nimport type { SVGProps } from 'react';\n/**\n * @internal\n */\nconst SvgCameraIcon = (props: SVGProps<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" width={16} height={16} fill=\"currentColor\" {...props}>\n    <path d=\"M0 4.5A1.5 1.5 0 0 1 1.5 3h8A1.5 1.5 0 0 1 11 4.5v7A1.5 1.5 0 0 1 9.5 13h-8A1.5 1.5 0 0 1 0 11.5zM15.2 3.6l-2.8 2.1a1 1 0 0 0-.4.8v3a1 1 0 0 0 .4.8l2.8 2.1a.5.5 0 0 0 .8-.4V4a.5.5 0 0 0-.8-.4z\" />\n  </svg>\n);\nexport default SvgCameraIcon;\n", "/**\n * WARNING: This file was auto-generated by svgr. Do not edit.\n */\nimport * as React from 'react';\nimport type { SVGProps } from 'react';\n/**\n * @internal\n */\nconst SvgChatCloseIcon = (props: SVGProps<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" width={16} height={16} viewBox=\"0 0 24 24\" {...props}>\n    <path\n      fill=\"#FFF\"\n      d=\"M4.99 3.99a1 1 0 0 0-.697 1.717L10.586 12l-6.293 6.293a1 1 0 1 0 1.414 1.414L12 13.414l6.293 6.293a1 1 0 1 0 1.414-1.414L13.414 12l6.293-6.293a1 1 0 0 0-.727-1.717 1 1 0 0 0-.687.303L12 10.586 5.707 4.293a1 1 0 0 0-.717-.303z\"\n    />\n  </svg>\n);\nexport default SvgChatCloseIcon;\n", "/**\n * WARNING: This file was auto-generated by svgr. Do not edit.\n */\nimport * as React from 'react';\nimport type { SVGProps } from 'react';\n/**\n * @internal\n */\nconst SvgChatIcon = (props: SVGProps<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" width={16} height={18} fill=\"none\" {...props}>\n    <path\n      fill=\"currentColor\"\n      fillRule=\"evenodd\"\n      d=\"M0 2.75A2.75 2.75 0 0 1 2.75 0h10.5A2.75 2.75 0 0 1 16 2.75v13.594a.75.75 0 0 1-1.234.572l-3.691-3.12a1.25 1.25 0 0 0-.807-.296H2.75A2.75 2.75 0 0 1 0 10.75v-8ZM2.75 1.5c-.69 0-1.25.56-1.25 1.25v8c0 .69.56 1.25 1.25 1.25h7.518c.65 0 1.279.23 1.775.65l2.457 2.077V2.75c0-.69-.56-1.25-1.25-1.25H2.75Z\"\n      clipRule=\"evenodd\"\n    />\n    <path\n      fill=\"currentColor\"\n      fillRule=\"evenodd\"\n      d=\"M3 4.5a.5.5 0 0 1 .5-.5h9a.5.5 0 0 1 0 1h-9a.5.5 0 0 1-.5-.5Zm0 2a.5.5 0 0 1 .5-.5h9a.5.5 0 0 1 0 1h-9a.5.5 0 0 1-.5-.5Zm0 2a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5Z\"\n      clipRule=\"evenodd\"\n    />\n  </svg>\n);\nexport default SvgChatIcon;\n", "/**\n * WARNING: This file was auto-generated by svgr. Do not edit.\n */\nimport * as React from 'react';\nimport type { SVGProps } from 'react';\n/**\n * @internal\n */\nconst SvgChevron = (props: SVGProps<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" width={16} height={16} fill=\"none\" {...props}>\n    <path\n      fill=\"currentcolor\"\n      fillRule=\"evenodd\"\n      d=\"M5.293 2.293a1 1 0 0 1 1.414 0l4.823 4.823a1.25 1.25 0 0 1 0 1.768l-4.823 4.823a1 1 0 0 1-1.414-1.414L9.586 8 5.293 3.707a1 1 0 0 1 0-1.414z\"\n      clipRule=\"evenodd\"\n    />\n  </svg>\n);\nexport default SvgChevron;\n", "/**\n * WARNING: This file was auto-generated by svgr. Do not edit.\n */\nimport * as React from 'react';\nimport type { SVGProps } from 'react';\n/**\n * @internal\n */\nconst SvgFocusToggleIcon = (props: SVGProps<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" width={16} height={16} fill=\"none\" {...props}>\n    <g stroke=\"currentColor\" strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5}>\n      <path d=\"M10 1.75h4.25m0 0V6m0-4.25L9 7M6 14.25H1.75m0 0V10m0 4.25L7 9\" />\n    </g>\n  </svg>\n);\nexport default SvgFocusToggleIcon;\n", "/**\n * WARNING: This file was auto-generated by svgr. Do not edit.\n */\nimport * as React from 'react';\nimport type { SVGProps } from 'react';\n/**\n * @internal\n */\nconst SvgGearIcon = (props: SVGProps<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" width={16} height={16} fill=\"none\" {...props}>\n    <path\n      fill=\"currentcolor\"\n      fillRule=\"evenodd\"\n      d=\"M8.961.894C8.875-.298 7.125-.298 7.04.894c-.066.912-1.246 1.228-1.76.472-.67-.99-2.186-.115-1.664.96.399.824-.465 1.688-1.288 1.289-1.076-.522-1.95.994-.961 1.665.756.513.44 1.693-.472 1.759-1.192.086-1.192 1.836 0 1.922.912.066 1.228 1.246.472 1.76-.99.67-.115 2.186.96 1.664.824-.399 1.688.465 1.289 1.288-.522 1.076.994 1.95 1.665.961.513-.756 1.693-.44 1.759.472.086 1.192 1.836 1.192 1.922 0 .066-.912 1.246-1.228 1.76-.472.67.99 2.186.115 1.664-.96-.399-.824.465-1.688 1.288-1.289 1.076.522 1.95-.994.961-1.665-.756-.513-.44-1.693.472-1.759 1.192-.086 1.192-1.836 0-1.922-.912-.066-1.228-1.246-.472-1.76.99-.67.115-2.186-.96-1.664-.824.399-1.688-.465-1.289-1.288.522-1.076-.994-1.95-1.665-.961-.513.756-1.693.44-1.759-.472ZM8 13A5 5 0 1 0 8 3a5 5 0 0 0 0 10Z\"\n      clipRule=\"evenodd\"\n    />\n  </svg>\n);\nexport default SvgGearIcon;\n", "/**\n * WARNING: This file was auto-generated by svgr. Do not edit.\n */\nimport * as React from 'react';\nimport type { SVGProps } from 'react';\n/**\n * @internal\n */\nconst SvgLeaveIcon = (props: SVGProps<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" width={16} height={16} fill=\"none\" {...props}>\n    <path\n      fill=\"currentColor\"\n      fillRule=\"evenodd\"\n      d=\"M2 2.75A2.75 2.75 0 0 1 4.75 0h6.5A2.75 2.75 0 0 1 14 2.75v10.5A2.75 2.75 0 0 1 11.25 16h-6.5A2.75 2.75 0 0 1 2 13.25v-.5a.75.75 0 0 1 1.5 0v.5c0 .69.56 1.25 1.25 1.25h6.5c.69 0 1.25-.56 1.25-1.25V2.75c0-.69-.56-1.25-1.25-1.25h-6.5c-.69 0-1.25.56-1.25 1.25v.5a.75.75 0 0 1-1.5 0v-.5Z\"\n      clipRule=\"evenodd\"\n    />\n    <path\n      fill=\"currentColor\"\n      fillRule=\"evenodd\"\n      d=\"M8.78 7.47a.75.75 0 0 1 0 1.06l-2.25 2.25a.75.75 0 1 1-1.06-1.06l.97-.97H1.75a.75.75 0 0 1 0-1.5h4.69l-.97-.97a.75.75 0 0 1 1.06-1.06l2.25 2.25Z\"\n      clipRule=\"evenodd\"\n    />\n  </svg>\n);\nexport default SvgLeaveIcon;\n", "/**\n * WARNING: This file was auto-generated by svgr. Do not edit.\n */\nimport * as React from 'react';\nimport type { SVGProps } from 'react';\n/**\n * @internal\n */\nconst SvgLockLockedIcon = (props: SVGProps<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" width={16} height={16} fill=\"none\" {...props}>\n    <path\n      fill=\"currentcolor\"\n      fillRule=\"evenodd\"\n      d=\"M4 6.104V4a4 4 0 1 1 8 0v2.104c1.154.326 2 1.387 2 2.646v4.5A2.75 2.75 0 0 1 11.25 16h-6.5A2.75 2.75 0 0 1 2 13.25v-4.5c0-1.259.846-2.32 2-2.646ZM5.5 4a2.5 2.5 0 0 1 5 0v2h-5V4Z\"\n      clipRule=\"evenodd\"\n    />\n  </svg>\n);\nexport default SvgLockLockedIcon;\n", "/**\n * WARNING: This file was auto-generated by svgr. Do not edit.\n */\nimport * as React from 'react';\nimport type { SVGProps } from 'react';\n/**\n * @internal\n */\nconst SvgMicDisabledIcon = (props: SVGProps<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" width={16} height={16} fill=\"currentColor\" {...props}>\n    <path d=\"M12.227 11.52a5.477 5.477 0 0 0 1.246-******** 0 0 0-.995-.1 4.478 4.478 0 0 1-.962 2.359l-1.07-1.07C10.794 9.247 11 8.647 11 8V3a3 3 0 0 0-6 0v1.293L1.354.646a.5.5 0 1 0-.708.708l14 14a.5.5 0 0 0 .708-.708zM8 12.5c.683 0 1.33-.152 1.911-.425l.743.743c-.649.359-1.378.59-2.154.66V15h2a.5.5 0 0 1 0 1h-5a.5.5 0 0 1 0-1h2v-1.522a5.502 5.502 0 0 1-4.973-4.929.5.5 0 0 1 .995-.098A4.5 4.5 0 0 0 8 12.5z\" />\n    <path d=\"M8.743 10.907 5 7.164V8a3 3 0 0 0 3.743 2.907z\" />\n  </svg>\n);\nexport default SvgMicDisabledIcon;\n", "/**\n * WARNING: This file was auto-generated by svgr. Do not edit.\n */\nimport * as React from 'react';\nimport type { SVGProps } from 'react';\n/**\n * @internal\n */\nconst SvgMicIcon = (props: SVGProps<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" width={16} height={16} fill=\"currentColor\" {...props}>\n    <path\n      fillRule=\"evenodd\"\n      d=\"M2.975 8.002a.5.5 0 0 1 .547.449 4.5 4.5 0 0 0 8.956 0 .5.5 0 1 1 .995.098A5.502 5.502 0 0 1 8.5 13.478V15h2a.5.5 0 0 1 0 1h-5a.5.5 0 0 1 0-1h2v-1.522a5.502 5.502 0 0 1-4.973-4.929.5.5 0 0 1 .448-.547z\"\n      clipRule=\"evenodd\"\n    />\n    <path d=\"M5 3a3 3 0 1 1 6 0v5a3 3 0 0 1-6 0z\" />\n  </svg>\n);\nexport default SvgMicIcon;\n", "/**\n * WARNING: This file was auto-generated by svgr. Do not edit.\n */\nimport * as React from 'react';\nimport type { SVGProps } from 'react';\n/**\n * @internal\n */\nconst SvgQualityExcellentIcon = (props: SVGProps<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" width={16} height={16} fill=\"currentcolor\" {...props}>\n    <path d=\"M0 11.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v4a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5zm6-5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v9a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5zm6-6a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v15a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5z\" />\n    <path d=\"M0 11.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v4a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5zm6-5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v9a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5zm6-6a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v15a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5z\" />\n  </svg>\n);\nexport default SvgQualityExcellentIcon;\n", "/**\n * WARNING: This file was auto-generated by svgr. Do not edit.\n */\nimport * as React from 'react';\nimport type { SVGProps } from 'react';\n/**\n * @internal\n */\nconst SvgQualityGoodIcon = (props: SVGProps<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" width={16} height={16} fill=\"currentcolor\" {...props}>\n    <path d=\"M0 11.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v4a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5zm6-5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v9a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5z\" />\n    <path d=\"M0 11.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v4a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5zm6-5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v9a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5z\" />\n    <g opacity={0.25}>\n      <path d=\"M12 .5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v15a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5z\" />\n      <path d=\"M12 .5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v15a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5z\" />\n    </g>\n  </svg>\n);\nexport default SvgQualityGoodIcon;\n", "/**\n * WARNING: This file was auto-generated by svgr. Do not edit.\n */\nimport * as React from 'react';\nimport type { SVGProps } from 'react';\n/**\n * @internal\n */\nconst SvgQualityPoorIcon = (props: SVGProps<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" width={16} height={16} fill=\"currentcolor\" {...props}>\n    <path d=\"M0 11.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v4a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5z\" />\n    <path d=\"M0 11.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v4a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5z\" />\n    <g opacity={0.25}>\n      <path d=\"M6 6.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v9a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5z\" />\n      <path d=\"M6 6.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v9a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5zm6-6a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v15a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5z\" />\n      <path d=\"M12 .5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v15a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5z\" />\n    </g>\n  </svg>\n);\nexport default SvgQualityPoorIcon;\n", "/**\n * WARNING: This file was auto-generated by svgr. Do not edit.\n */\nimport * as React from 'react';\nimport type { SVGProps } from 'react';\n/**\n * @internal\n */\nconst SvgQualityUnknownIcon = (props: SVGProps<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" width={16} height={16} fill=\"currentColor\" {...props}>\n    <g opacity={0.25}>\n      <path d=\"M0 11.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v4a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-4Zm6-5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v9a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-9Zm6-6a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v15a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5V.5Z\" />\n      <path d=\"M0 11.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v4a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-4Zm6-5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v9a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-9Zm6-6a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v15a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5V.5Z\" />\n    </g>\n  </svg>\n);\nexport default SvgQualityUnknownIcon;\n", "/**\n * WARNING: This file was auto-generated by svgr. Do not edit.\n */\nimport * as React from 'react';\nimport type { SVGProps } from 'react';\n/**\n * @internal\n */\nconst SvgScreenShareIcon = (props: SVGProps<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" width={20} height={16} fill=\"none\" {...props}>\n    <path\n      fill=\"currentColor\"\n      fillRule=\"evenodd\"\n      d=\"M0 2.75A2.75 2.75 0 0 1 2.75 0h14.5A2.75 2.75 0 0 1 20 2.75v10.5A2.75 2.75 0 0 1 17.25 16H2.75A2.75 2.75 0 0 1 0 13.25V2.75ZM2.75 1.5c-.69 0-1.25.56-1.25 1.25v10.5c0 .69.56 1.25 1.25 1.25h14.5c.69 0 1.25-.56 1.25-1.25V2.75c0-.69-.56-1.25-1.25-1.25H2.75Z\"\n      clipRule=\"evenodd\"\n    />\n    <path\n      fill=\"currentColor\"\n      fillRule=\"evenodd\"\n      d=\"M9.47 4.22a.75.75 0 0 1 1.06 0l2.25 2.25a.75.75 0 0 1-1.06 1.06l-.97-.97v4.69a.75.75 0 0 1-1.5 0V6.56l-.97.97a.75.75 0 0 1-1.06-1.06l2.25-2.25Z\"\n      clipRule=\"evenodd\"\n    />\n  </svg>\n);\nexport default SvgScreenShareIcon;\n", "/**\n * WARNING: This file was auto-generated by svgr. Do not edit.\n */\nimport * as React from 'react';\nimport type { SVGProps } from 'react';\n/**\n * @internal\n */\nconst SvgScreenShareStopIcon = (props: SVGProps<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" width={20} height={16} fill=\"none\" {...props}>\n    <g fill=\"currentColor\">\n      <path d=\"M7.28 4.22a.75.75 0 0 0-1.06 1.06L8.94 8l-2.72 2.72a.75.75 0 1 0 1.06 1.06L10 9.06l2.72 2.72a.75.75 0 1 0 1.06-1.06L11.06 8l2.72-2.72a.75.75 0 0 0-1.06-1.06L10 6.94z\" />\n      <path\n        fillRule=\"evenodd\"\n        d=\"M2.75 0A2.75 2.75 0 0 0 0 2.75v10.5A2.75 2.75 0 0 0 2.75 16h14.5A2.75 2.75 0 0 0 20 13.25V2.75A2.75 2.75 0 0 0 17.25 0zM1.5 2.75c0-.69.56-1.25 1.25-1.25h14.5c.69 0 1.25.56 1.25 1.25v10.5c0 .69-.56 1.25-1.25 1.25H2.75c-.69 0-1.25-.56-1.25-1.25z\"\n        clipRule=\"evenodd\"\n      />\n    </g>\n  </svg>\n);\nexport default SvgScreenShareStopIcon;\n", "/**\n * WARNING: This file was auto-generated by svgr. Do not edit.\n */\nimport * as React from 'react';\nimport type { SVGProps } from 'react';\n/**\n * @internal\n */\nconst SvgSpinnerIcon = (props: SVGProps<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" width={16} height={16} fill=\"none\" {...props}>\n    <path\n      fill=\"currentColor\"\n      fillRule=\"evenodd\"\n      d=\"M8 0a.75.75 0 0 1 .75.75v2.5a.75.75 0 0 1-1.5 0V.75A.75.75 0 0 1 8 0Z\"\n      clipRule=\"evenodd\"\n    />\n    <path\n      fill=\"currentColor\"\n      fillRule=\"evenodd\"\n      d=\"M8 12a.75.75 0 0 1 .75.75v2.5a.75.75 0 0 1-1.5 0v-2.5A.75.75 0 0 1 8 12Z\"\n      clipRule=\"evenodd\"\n      opacity={0.7}\n    />\n    <path\n      fill=\"currentColor\"\n      fillRule=\"evenodd\"\n      d=\"M12 1.072a.75.75 0 0 1 .274 1.024l-1.25 2.165a.75.75 0 0 1-1.299-.75l1.25-2.165A.75.75 0 0 1 12 1.072Z\"\n      clipRule=\"evenodd\"\n    />\n    <path\n      fill=\"currentColor\"\n      fillRule=\"evenodd\"\n      d=\"M6 11.464a.75.75 0 0 1 .274 1.025l-1.25 2.165a.75.75 0 0 1-1.299-.75l1.25-2.165A.75.75 0 0 1 6 11.464Z\"\n      clipRule=\"evenodd\"\n      opacity={0.6}\n    />\n    <path\n      fill=\"currentColor\"\n      fillRule=\"evenodd\"\n      d=\"M14.928 4a.75.75 0 0 1-.274 1.025l-2.165 1.25a.75.75 0 1 1-.75-1.3l2.165-1.25A.75.75 0 0 1 14.928 4Z\"\n      clipRule=\"evenodd\"\n    />\n    <path\n      fill=\"currentColor\"\n      fillRule=\"evenodd\"\n      d=\"M4.536 10a.75.75 0 0 1-.275 1.024l-2.165 1.25a.75.75 0 0 1-.75-1.298l2.165-1.25A.75.75 0 0 1 4.536 10Z\"\n      clipRule=\"evenodd\"\n      opacity={0.5}\n    />\n    <path\n      fill=\"currentColor\"\n      fillRule=\"evenodd\"\n      d=\"M16 8a.75.75 0 0 1-.75.75h-2.5a.75.75 0 0 1 0-1.5h2.5A.75.75 0 0 1 16 8Z\"\n      clipRule=\"evenodd\"\n    />\n    <path\n      fill=\"currentColor\"\n      fillRule=\"evenodd\"\n      d=\"M4 8a.75.75 0 0 1-.75.75H.75a.75.75 0 0 1 0-1.5h2.5A.75.75 0 0 1 4 8Z\"\n      clipRule=\"evenodd\"\n      opacity={0.4}\n    />\n    <path\n      fill=\"currentColor\"\n      fillRule=\"evenodd\"\n      d=\"M14.928 12a.75.75 0 0 1-1.024.274l-2.165-1.25a.75.75 0 0 1 .75-1.299l2.165 1.25A.75.75 0 0 1 14.928 12Z\"\n      clipRule=\"evenodd\"\n      opacity={0.9}\n    />\n    <path\n      fill=\"currentColor\"\n      fillRule=\"evenodd\"\n      d=\"M4.536 6a.75.75 0 0 1-1.025.275l-2.165-1.25a.75.75 0 1 1 .75-1.3l2.165 1.25A.75.75 0 0 1 4.536 6Z\"\n      clipRule=\"evenodd\"\n      opacity={0.3}\n    />\n    <path\n      fill=\"currentColor\"\n      fillRule=\"evenodd\"\n      d=\"M12 14.928a.75.75 0 0 1-1.024-.274l-1.25-2.165a.75.75 0 0 1 1.298-.75l1.25 2.165A.75.75 0 0 1 12 14.928Z\"\n      clipRule=\"evenodd\"\n      opacity={0.8}\n    />\n    <path\n      fill=\"currentColor\"\n      fillRule=\"evenodd\"\n      d=\"M6 4.536a.75.75 0 0 1-1.024-.275l-1.25-2.165a.75.75 0 1 1 1.299-.75l1.25 2.165A.75.75 0 0 1 6 4.536Z\"\n      clipRule=\"evenodd\"\n      opacity={0.2}\n    />\n  </svg>\n);\nexport default SvgSpinnerIcon;\n", "/**\n * WARNING: This file was auto-generated by svgr. Do not edit.\n */\nimport * as React from 'react';\nimport type { SVGProps } from 'react';\n/**\n * @internal\n */\nconst SvgUnfocusToggleIcon = (props: SVGProps<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" width={16} height={16} fill=\"none\" {...props}>\n    <g stroke=\"currentColor\" strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5}>\n      <path d=\"M13.25 7H9m0 0V2.75M9 7l5.25-5.25M2.75 9H7m0 0v4.25M7 9l-5.25 5.25\" />\n    </g>\n  </svg>\n);\nexport default SvgUnfocusToggleIcon;\n", "import * as React from 'react';\nimport { LayoutContext, useMaybeTrackRefContext } from '../../context';\nimport { FocusToggleIcon, UnfocusToggleIcon } from '../../assets/icons';\nimport { useFocusToggle } from '../../hooks';\nimport type { TrackReferenceOrPlaceholder } from '@livekit/components-core';\n\n/** @public */\nexport interface FocusToggleProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  trackRef?: TrackReferenceOrPlaceholder;\n}\n\n/**\n * The `FocusToggle` puts the `ParticipantTile` in focus or removes it from focus.\n * @remarks\n * This component needs to live inside `LayoutContext` to work properly.\n *\n * @example\n * ```tsx\n * <ParticipantTile>\n *   <FocusToggle />\n * </ParticipantTile>\n * ```\n * @public\n */\nexport const FocusToggle: (\n  props: FocusToggleProps & React.RefAttributes<HTMLButtonElement>,\n) => React.ReactNode = /* @__PURE__ */ React.forwardRef<HTMLButtonElement, FocusToggleProps>(\n  function FocusToggle({ trackRef, ...props }: FocusToggleProps, ref) {\n    const trackRefFromContext = useMaybeTrackRefContext();\n\n    const { mergedProps, inFocus } = useFocusToggle({\n      trackRef: trackRef ?? trackRefFromContext,\n      props,\n    });\n\n    return (\n      <LayoutContext.Consumer>\n        {(layoutContext) =>\n          layoutContext !== undefined && (\n            <button ref={ref} {...mergedProps}>\n              {props.children ? (\n                props.children\n              ) : inFocus ? (\n                <UnfocusToggleIcon />\n              ) : (\n                <FocusToggleIcon />\n              )}\n            </button>\n          )\n        }\n      </LayoutContext.Consumer>\n    );\n  },\n);\n", "import * as React from 'react';\nimport { useMaybeRoomContext } from '../../context';\nimport { mergeProps } from '../../utils';\nimport { RoomEvent, type LocalAudioTrack, type LocalVideoTrack } from 'livekit-client';\nimport { useMediaDeviceSelect } from '../../hooks';\n\n/** @public */\nexport interface MediaDeviceSelectProps\n  extends Omit<React.HTMLAttributes<HTMLUListElement>, 'onError'> {\n  kind: MediaDeviceKind;\n  onActiveDeviceChange?: (deviceId: string) => void;\n  onDeviceListChange?: (devices: MediaDeviceInfo[]) => void;\n  onDeviceSelectError?: (e: Error) => void;\n  initialSelection?: string;\n  /** will force the browser to only return the specified device\n   * will call `onDeviceSelectError` with the error in case this fails\n   */\n  exactMatch?: boolean;\n  track?: LocalAudioTrack | LocalVideoTrack;\n  /**\n   * this will call getUserMedia if the permissions are not yet given to enumerate the devices with device labels.\n   * in some browsers multiple calls to getUserMedia result in multiple permission prompts.\n   * It's generally advised only flip this to true, once a (preview) track has been acquired successfully with the\n   * appropriate permissions.\n   *\n   * @see {@link MediaDeviceMenu}\n   * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/MediaDevices/enumerateDevices | MDN enumerateDevices}\n   */\n  requestPermissions?: boolean;\n  onError?: (e: Error) => void;\n}\n\n/**\n * The `MediaDeviceSelect` list all media devices of one kind.\n * Clicking on one of the listed devices make it the active media device.\n *\n * @example\n * ```tsx\n * <LiveKitRoom>\n *   <MediaDeviceSelect kind='audioinput' />\n * </LiveKitRoom>\n * ```\n * @public\n */\nexport const MediaDeviceSelect: (\n  props: MediaDeviceSelectProps & React.RefAttributes<HTMLUListElement>,\n) => React.ReactNode = /* @__PURE__ */ React.forwardRef<HTMLUListElement, MediaDeviceSelectProps>(\n  function MediaDeviceSelect(\n    {\n      kind,\n      initialSelection,\n      onActiveDeviceChange,\n      onDeviceListChange,\n      onDeviceSelectError,\n      exactMatch,\n      track,\n      requestPermissions,\n      onError,\n      ...props\n    }: MediaDeviceSelectProps,\n    ref,\n  ) {\n    const room = useMaybeRoomContext();\n    const handleError = React.useCallback(\n      (e: Error) => {\n        if (room) {\n          // awkwardly emit the event from outside of the room, as we don't have other means to raise a MediaDeviceError\n          room.emit(RoomEvent.MediaDevicesError, e);\n        }\n        onError?.(e);\n      },\n      [room, onError],\n    );\n    const { devices, activeDeviceId, setActiveMediaDevice, className } = useMediaDeviceSelect({\n      kind,\n      room,\n      track,\n      requestPermissions,\n      onError: handleError,\n    });\n    React.useEffect(() => {\n      if (initialSelection !== undefined) {\n        setActiveMediaDevice(initialSelection);\n      }\n    }, [setActiveMediaDevice]);\n\n    React.useEffect(() => {\n      if (typeof onDeviceListChange === 'function') {\n        onDeviceListChange(devices);\n      }\n    }, [onDeviceListChange, devices]);\n\n    React.useEffect(() => {\n      if (activeDeviceId && activeDeviceId !== '') {\n        onActiveDeviceChange?.(activeDeviceId);\n      }\n    }, [activeDeviceId]);\n\n    const handleActiveDeviceChange = async (deviceId: string) => {\n      try {\n        await setActiveMediaDevice(deviceId, { exact: exactMatch });\n      } catch (e) {\n        if (e instanceof Error) {\n          onDeviceSelectError?.(e);\n        } else {\n          throw e;\n        }\n      }\n    };\n    // Merge Props\n    const mergedProps = React.useMemo(\n      () => mergeProps(props, { className }, { className: 'lk-list' }),\n      [className, props],\n    );\n\n    const hasDefault = !!devices.find((info) => info.label.toLowerCase().startsWith('default'));\n\n    function isActive(deviceId: string, activeDeviceId: string, index: number) {\n      return (\n        deviceId === activeDeviceId || (!hasDefault && index === 0 && activeDeviceId === 'default')\n      );\n    }\n\n    return (\n      <ul ref={ref} {...mergedProps}>\n        {devices.map((device, index) => (\n          <li\n            key={device.deviceId}\n            id={device.deviceId}\n            data-lk-active={isActive(device.deviceId, activeDeviceId, index)}\n            aria-selected={isActive(device.deviceId, activeDeviceId, index)}\n            role=\"option\"\n          >\n            <button className=\"lk-button\" onClick={() => handleActiveDeviceChange(device.deviceId)}>\n              {device.label}\n            </button>\n          </li>\n        ))}\n      </ul>\n    );\n  },\n);\n", "import * as React from 'react';\nimport { useRoomContext } from '../../context';\nimport { useStartAudio } from '../../hooks';\n\n/** @public */\nexport interface AllowAudioPlaybackProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  label: string;\n}\n\n/**\n * The `StartAudio` component is only visible when the browser blocks audio playback. This is due to some browser implemented autoplay policies.\n * To start audio playback, the user must perform a user-initiated event such as clicking this button.\n * As soon as audio playback starts, the button hides itself again.\n *\n * @example\n * ```tsx\n * <LiveKitRoom>\n *   <StartAudio label=\"Click to allow audio playback\" />\n * </LiveKitRoom>\n * ```\n *\n * @see Autoplay policy on MDN web docs: {@link https://developer.mozilla.org/en-US/docs/Web/API/Web_Audio_API/Best_practices#autoplay_policy}\n * @public\n */\nexport const StartAudio: (\n  props: AllowAudioPlaybackProps & React.RefAttributes<HTMLButtonElement>,\n) => React.ReactNode = /* @__PURE__ */ React.forwardRef<HTMLButtonElement, AllowAudioPlaybackProps>(\n  function StartAudio({ label = 'Allow Audio', ...props }: AllowAudioPlaybackProps, ref) {\n    const room = useRoomContext();\n    const { mergedProps } = useStartAudio({ room, props });\n\n    return (\n      <button ref={ref} {...mergedProps}>\n        {label}\n      </button>\n    );\n  },\n);\n", "import * as React from 'react';\nimport { useRoomContext } from '../../context';\nimport { useStartAudio, useStartVideo } from '../../hooks';\n\n/** @public */\nexport interface AllowMediaPlaybackProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  label?: string;\n}\n\n/**\n * The `StartMediaButton` component is only visible when the browser blocks media playback. This is due to some browser implemented autoplay policies.\n * To start media playback, the user must perform a user-initiated event such as clicking this button.\n * As soon as media playback starts, the button hides itself again.\n *\n * @example\n * ```tsx\n * <LiveKitRoom>\n *   <StartMediaButton label=\"Click to allow media playback\" />\n * </LiveKitRoom>\n * ```\n *\n * @see Autoplay policy on MDN web docs: {@link https://developer.mozilla.org/en-US/docs/Web/API/Web_Audio_API/Best_practices#autoplay_policy}\n * @public\n */\nexport const StartMediaButton: (\n  props: AllowMediaPlaybackProps & React.RefAttributes<HTMLButtonElement>,\n) => React.ReactNode = /* @__PURE__ */ React.forwardRef<HTMLButtonElement, AllowMediaPlaybackProps>(\n  function StartMediaButton({ label, ...props }: AllowMediaPlaybackProps, ref) {\n    const room = useRoomContext();\n    const { mergedProps: audioProps, canPlayAudio } = useStartAudio({ room, props });\n    const { mergedProps, canPlayVideo } = useStartVideo({ room, props: audioProps });\n    const { style, ...restProps } = mergedProps;\n    style.display = canPlayAudio && canPlayVideo ? 'none' : 'block';\n\n    return (\n      <button ref={ref} style={style} {...restProps}>\n        {label ?? `Start ${!canPlayAudio ? 'Audio' : 'Video'}`}\n      </button>\n    );\n  },\n);\n", "import * as React from 'react';\nimport { ConnectionQuality, Track } from 'livekit-client';\n\nimport {\n  MicIcon,\n  MicDisabledIcon,\n  CameraIcon,\n  CameraDisabledIcon,\n  QualityUnknownIcon,\n  QualityExcellentIcon,\n  QualityGoodIcon,\n  QualityPoorIcon,\n  ScreenShareIcon,\n  ScreenShareStopIcon,\n} from './index';\n\n/**\n * @internal\n */\nexport function getSourceIcon(source: Track.Source, enabled: boolean) {\n  switch (source) {\n    case Track.Source.Microphone:\n      return enabled ? <MicIcon /> : <MicDisabledIcon />;\n    case Track.Source.Camera:\n      return enabled ? <CameraIcon /> : <CameraDisabledIcon />;\n    case Track.Source.ScreenShare:\n      return enabled ? <ScreenShareStopIcon /> : <ScreenShareIcon />;\n    default:\n      return undefined;\n  }\n}\n\n/**\n * @internal\n */\nexport function getConnectionQualityIcon(quality: ConnectionQuality) {\n  switch (quality) {\n    case ConnectionQuality.Excellent:\n      return <QualityExcellentIcon />;\n    case ConnectionQuality.Good:\n      return <QualityGoodIcon />;\n    case ConnectionQuality.Poor:\n      return <QualityPoorIcon />;\n    default:\n      return <QualityUnknownIcon />;\n  }\n}\n", "import type { CaptureOptionsBySource, ToggleSource } from '@livekit/components-core';\nimport * as React from 'react';\nimport { getSourceIcon } from '../../assets/icons/util';\nimport { useTrackToggle } from '../../hooks';\nimport { TrackPublishOptions } from 'livekit-client';\n\n/** @public */\nexport interface TrackToggleProps<T extends ToggleSource>\n  extends Omit<React.ButtonHTMLAttributes<HTMLButtonElement>, 'onChange'> {\n  source: T;\n  showIcon?: boolean;\n  initialState?: boolean;\n  /**\n   * Function that is called when the enabled state of the toggle changes.\n   * The second function argument `isUserInitiated` is `true` if the change was initiated by a user interaction, such as a click.\n   */\n  onChange?: (enabled: boolean, isUserInitiated: boolean) => void;\n  captureOptions?: CaptureOptionsBySource<T>;\n  publishOptions?: TrackPublishOptions;\n  onDeviceError?: (error: Error) => void;\n}\n\n/**\n * With the `TrackToggle` component it is possible to mute and unmute your camera and microphone.\n * The component uses an html button element under the hood so you can treat it like a button.\n *\n * @example\n * ```tsx\n * <LiveKitRoom>\n *   <TrackToggle source={Track.Source.Microphone} />\n *   <TrackToggle source={Track.Source.Camera} />\n * </LiveKitRoom>\n * ```\n * @public\n */\nexport const TrackToggle: <T extends ToggleSource>(\n  props: TrackToggleProps<T> & React.RefAttributes<HTMLButtonElement>,\n) => React.ReactNode = /* @__PURE__ */ React.forwardRef(function TrackToggle<\n  T extends ToggleSource,\n>({ showIcon, ...props }: TrackToggleProps<T>, ref: React.ForwardedRef<HTMLButtonElement>) {\n  const { buttonProps, enabled } = useTrackToggle(props);\n  const [isClient, setIsClient] = React.useState(false);\n  React.useEffect(() => {\n    setIsClient(true);\n  }, []);\n  return (\n    isClient && (\n      <button ref={ref} {...buttonProps}>\n        {(showIcon ?? true) && getSourceIcon(props.source, enabled)}\n        {props.children}\n      </button>\n    )\n  );\n});\n", "import * as React from 'react';\nimport { mergeProps } from '../../utils';\nimport { getConnectionQualityIcon } from '../../assets/icons/util';\nimport type { ConnectionQualityIndicatorOptions } from '../../hooks';\nimport { useConnectionQualityIndicator } from '../../hooks';\n\n/** @public */\nexport interface ConnectionQualityIndicatorProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    ConnectionQualityIndicatorOptions {}\n\n/**\n * The `ConnectionQualityIndicator` shows the individual connection quality of a participant.\n *\n * @example\n * ```tsx\n * <ConnectionQualityIndicator />\n * ```\n * @public\n */\nexport const ConnectionQualityIndicator: (\n  props: ConnectionQualityIndicatorProps & React.RefAttributes<HTMLDivElement>,\n) => React.ReactNode = /* @__PURE__ */ React.forwardRef<\n  HTMLDivElement,\n  ConnectionQualityIndicatorProps\n>(function ConnectionQualityIndicator(props: ConnectionQualityIndicatorProps, ref) {\n  const { className, quality } = useConnectionQualityIndicator(props);\n  const elementProps = React.useMemo(() => {\n    return { ...mergeProps(props, { className: className as string }), 'data-lk-quality': quality };\n  }, [quality, props, className]);\n  return (\n    <div ref={ref} {...elementProps}>\n      {props.children ?? getConnectionQualityIcon(quality)}\n    </div>\n  );\n});\n", "import { setupParticipantName } from '@livekit/components-core';\nimport * as React from 'react';\nimport { useEnsureParticipant } from '../../context';\nimport { useObservableState } from '../../hooks/internal/useObservableState';\nimport { mergeProps } from '../../utils';\nimport type { UseParticipantInfoOptions } from '../../hooks';\n\n/** @public */\nexport interface ParticipantNameProps\n  extends React.HTMLAttributes<HTMLSpanElement>,\n    UseParticipantInfoOptions {}\n\n/**\n * The `ParticipantName` component displays the name of the participant as a string within an HTML span element.\n * If no participant name is undefined the participant identity string is displayed.\n *\n * @example\n * ```tsx\n * <ParticipantName />\n * ```\n * @public\n */\nexport const ParticipantName: (\n  props: ParticipantNameProps & React.RefAttributes<HTMLSpanElement>,\n) => React.ReactNode = /* @__PURE__ */ React.forwardRef<HTMLSpanElement, ParticipantNameProps>(\n  function ParticipantName({ participant, ...props }: ParticipantNameProps, ref) {\n    const p = useEnsureParticipant(participant);\n\n    const { className, infoObserver } = React.useMemo(() => {\n      return setupParticipantName(p);\n    }, [p]);\n\n    const { identity, name } = useObservableState(infoObserver, {\n      name: p.name,\n      identity: p.identity,\n      metadata: p.metadata,\n    });\n\n    const mergedProps = React.useMemo(() => {\n      return mergeProps(props, { className, 'data-lk-participant-name': name });\n    }, [props, className, name]);\n\n    return (\n      <span ref={ref} {...mergedProps}>\n        {name !== '' ? name : identity}\n        {props.children}\n      </span>\n    );\n  },\n);\n", "import * as React from 'react';\nimport { mergeProps } from '../../utils';\nimport { getSourceIcon } from '../../assets/icons/util';\nimport { useTrackMutedIndicator } from '../../hooks';\nimport type { TrackReferenceOrPlaceholder } from '@livekit/components-core';\n\n/** @public */\nexport interface TrackMutedIndicatorProps extends React.HTMLAttributes<HTMLDivElement> {\n  trackRef: TrackReferenceOrPlaceholder;\n  show?: 'always' | 'muted' | 'unmuted';\n}\n\n/**\n * The `TrackMutedIndicator` shows whether the participant's camera or microphone is muted or not.\n * By default, a muted/unmuted icon is displayed for a camera, microphone, and screen sharing track.\n *\n * @example\n * ```tsx\n * <TrackMutedIndicator trackRef={trackRef} />\n * ```\n * @public\n */\nexport const TrackMutedIndicator: (\n  props: TrackMutedIndicatorProps & React.RefAttributes<HTMLDivElement>,\n) => React.ReactNode = /* @__PURE__ */ React.forwardRef<HTMLDivElement, TrackMutedIndicatorProps>(\n  function TrackMutedIndicator(\n    { trackRef, show = 'always', ...props }: TrackMutedIndicatorProps,\n    ref,\n  ) {\n    const { className, isMuted } = useTrackMutedIndicator(trackRef);\n\n    const showIndicator =\n      show === 'always' || (show === 'muted' && isMuted) || (show === 'unmuted' && !isMuted);\n\n    const htmlProps = React.useMemo(\n      () =>\n        mergeProps(props, {\n          className,\n        }),\n      [className, props],\n    );\n\n    if (!showIndicator) {\n      return null;\n    }\n\n    return (\n      <div ref={ref} {...htmlProps} data-lk-muted={isMuted}>\n        {props.children ?? getSourceIcon(trackRef.source, !isMuted)}\n      </div>\n    );\n  },\n);\n", "/**\n * WARNING: This file was auto-generated by svgr. Do not edit.\n */\nimport * as React from 'react';\nimport type { SVGProps } from 'react';\n/**\n * @internal\n */\nconst SvgParticipantPlaceholder = (props: SVGProps<SVGSVGElement>) => (\n  <svg\n    width={320}\n    height={320}\n    viewBox=\"0 0 320 320\"\n    preserveAspectRatio=\"xMidYMid meet\"\n    fill=\"none\"\n    xmlns=\"http://www.w3.org/2000/svg\"\n    {...props}\n  >\n    <path\n      d=\"M160 180C204.182 180 240 144.183 240 100C240 55.8172 204.182 20 160 20C115.817 20 79.9997 55.8172 79.9997 100C79.9997 144.183 115.817 180 160 180Z\"\n      fill=\"white\"\n      fillOpacity={0.25}\n    />\n    <path\n      d=\"M97.6542 194.614C103.267 191.818 109.841 192.481 115.519 195.141C129.025 201.466 144.1 205 159.999 205C175.899 205 190.973 201.466 204.48 195.141C210.158 192.481 216.732 191.818 222.345 194.614C262.703 214.719 291.985 253.736 298.591 300.062C300.15 310.997 291.045 320 280 320H39.9997C28.954 320 19.8495 310.997 21.4087 300.062C28.014 253.736 57.2966 214.72 97.6542 194.614Z\"\n      fill=\"white\"\n      fillOpacity={0.25}\n    />\n  </svg>\n);\nexport default SvgParticipantPlaceholder;\n", "import type { TrackIdentifier } from '@livekit/components-core';\nimport {\n  getTrackByIdentifier,\n  isTrackReference,\n  log,\n  setupMediaTrack,\n} from '@livekit/components-core';\nimport * as React from 'react';\nimport { mergeProps } from '../utils';\n\n/** @public */\nexport interface UseMediaTrackOptions {\n  element?: React.RefObject<HTMLMediaElement> | null;\n  props?: React.HTMLAttributes<HTMLVideoElement | HTMLAudioElement>;\n}\n\n/**\n * @internal\n */\nexport function useMediaTrackBySourceOrName(\n  observerOptions: TrackIdentifier,\n  options: UseMediaTrackOptions = {},\n) {\n  const [publication, setPublication] = React.useState(getTrackByIdentifier(observerOptions));\n\n  const [isMuted, setMuted] = React.useState(publication?.isMuted);\n  const [isSubscribed, setSubscribed] = React.useState(publication?.isSubscribed);\n\n  const [track, setTrack] = React.useState(publication?.track);\n  const [orientation, setOrientation] = React.useState<'landscape' | 'portrait'>('landscape');\n  const previousElement = React.useRef<HTMLMediaElement | undefined | null>();\n\n  const { className, trackObserver } = React.useMemo(() => {\n    return setupMediaTrack(observerOptions);\n  }, [\n    observerOptions.participant.sid ?? observerOptions.participant.identity,\n    observerOptions.source,\n    isTrackReference(observerOptions) && observerOptions.publication.trackSid,\n  ]);\n\n  React.useEffect(() => {\n    const subscription = trackObserver.subscribe((publication) => {\n      log.debug('update track', publication);\n      setPublication(publication);\n      setMuted(publication?.isMuted);\n      setSubscribed(publication?.isSubscribed);\n      setTrack(publication?.track);\n    });\n    return () => subscription?.unsubscribe();\n  }, [trackObserver]);\n\n  React.useEffect(() => {\n    if (track) {\n      if (previousElement.current) {\n        track.detach(previousElement.current);\n      }\n      if (\n        options.element?.current &&\n        !(observerOptions.participant.isLocal && track?.kind === 'audio')\n      ) {\n        track.attach(options.element.current);\n      }\n    }\n    previousElement.current = options.element?.current;\n    return () => {\n      if (previousElement.current) {\n        track?.detach(previousElement.current);\n      }\n    };\n  }, [track, options.element]);\n\n  React.useEffect(() => {\n    // Set the orientation of the video track.\n    // TODO: This does not handle changes in orientation after a track got published (e.g when rotating a phone camera from portrait to landscape).\n    if (\n      typeof publication?.dimensions?.width === 'number' &&\n      typeof publication?.dimensions?.height === 'number'\n    ) {\n      const orientation_ =\n        publication.dimensions.width > publication.dimensions.height ? 'landscape' : 'portrait';\n      setOrientation(orientation_);\n    }\n  }, [publication]);\n\n  return {\n    publication,\n    isMuted,\n    isSubscribed,\n    track,\n    elementProps: mergeProps(options.props, {\n      className,\n      'data-lk-local-participant': observerOptions.participant.isLocal,\n      'data-lk-source': publication?.source,\n      ...(publication?.kind === 'video' && { 'data-lk-orientation': orientation }),\n    }),\n  };\n}\n", "/**\n * lodash (Custom Build) <https://lodash.com/>\n * Build: `lodash modularize exports=\"npm\" -o ./`\n * Copyright jQuery Foundation and other contributors <https://jquery.org/>\n * Released under MIT license <https://lodash.com/license>\n * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>\n * Copyright <PERSON>, DocumentCloud and Investigative Reporters & Editors\n */\n\n/** Used as the `TypeError` message for \"Functions\" methods. */\nvar FUNC_ERROR_TEXT = 'Expected a function';\n\n/** Used as references for various `Number` constants. */\nvar NAN = 0 / 0;\n\n/** `Object#toString` result references. */\nvar symbolTag = '[object Symbol]';\n\n/** Used to match leading and trailing whitespace. */\nvar reTrim = /^\\s+|\\s+$/g;\n\n/** Used to detect bad signed hexadecimal string values. */\nvar reIsBadHex = /^[-+]0x[0-9a-f]+$/i;\n\n/** Used to detect binary string values. */\nvar reIsBinary = /^0b[01]+$/i;\n\n/** Used to detect octal string values. */\nvar reIsOctal = /^0o[0-7]+$/i;\n\n/** Built-in method references without a dependency on `root`. */\nvar freeParseInt = parseInt;\n\n/** Detect free variable `global` from Node.js. */\nvar freeGlobal = typeof global == 'object' && global && global.Object === Object && global;\n\n/** Detect free variable `self`. */\nvar freeSelf = typeof self == 'object' && self && self.Object === Object && self;\n\n/** Used as a reference to the global object. */\nvar root = freeGlobal || freeSelf || Function('return this')();\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar objectToString = objectProto.toString;\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max,\n    nativeMin = Math.min;\n\n/**\n * Gets the timestamp of the number of milliseconds that have elapsed since\n * the Unix epoch (1 January 1970 00:00:00 UTC).\n *\n * @static\n * @memberOf _\n * @since 2.4.0\n * @category Date\n * @returns {number} Returns the timestamp.\n * @example\n *\n * _.defer(function(stamp) {\n *   console.log(_.now() - stamp);\n * }, _.now());\n * // => Logs the number of milliseconds it took for the deferred invocation.\n */\nvar now = function() {\n  return root.Date.now();\n};\n\n/**\n * Creates a debounced function that delays invoking `func` until after `wait`\n * milliseconds have elapsed since the last time the debounced function was\n * invoked. The debounced function comes with a `cancel` method to cancel\n * delayed `func` invocations and a `flush` method to immediately invoke them.\n * Provide `options` to indicate whether `func` should be invoked on the\n * leading and/or trailing edge of the `wait` timeout. The `func` is invoked\n * with the last arguments provided to the debounced function. Subsequent\n * calls to the debounced function return the result of the last `func`\n * invocation.\n *\n * **Note:** If `leading` and `trailing` options are `true`, `func` is\n * invoked on the trailing edge of the timeout only if the debounced function\n * is invoked more than once during the `wait` timeout.\n *\n * If `wait` is `0` and `leading` is `false`, `func` invocation is deferred\n * until to the next tick, similar to `setTimeout` with a timeout of `0`.\n *\n * See [David Corbacho's article](https://css-tricks.com/debouncing-throttling-explained-examples/)\n * for details over the differences between `_.debounce` and `_.throttle`.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Function\n * @param {Function} func The function to debounce.\n * @param {number} [wait=0] The number of milliseconds to delay.\n * @param {Object} [options={}] The options object.\n * @param {boolean} [options.leading=false]\n *  Specify invoking on the leading edge of the timeout.\n * @param {number} [options.maxWait]\n *  The maximum time `func` is allowed to be delayed before it's invoked.\n * @param {boolean} [options.trailing=true]\n *  Specify invoking on the trailing edge of the timeout.\n * @returns {Function} Returns the new debounced function.\n * @example\n *\n * // Avoid costly calculations while the window size is in flux.\n * jQuery(window).on('resize', _.debounce(calculateLayout, 150));\n *\n * // Invoke `sendMail` when clicked, debouncing subsequent calls.\n * jQuery(element).on('click', _.debounce(sendMail, 300, {\n *   'leading': true,\n *   'trailing': false\n * }));\n *\n * // Ensure `batchLog` is invoked once after 1 second of debounced calls.\n * var debounced = _.debounce(batchLog, 250, { 'maxWait': 1000 });\n * var source = new EventSource('/stream');\n * jQuery(source).on('message', debounced);\n *\n * // Cancel the trailing debounced invocation.\n * jQuery(window).on('popstate', debounced.cancel);\n */\nfunction debounce(func, wait, options) {\n  var lastArgs,\n      lastThis,\n      maxWait,\n      result,\n      timerId,\n      lastCallTime,\n      lastInvokeTime = 0,\n      leading = false,\n      maxing = false,\n      trailing = true;\n\n  if (typeof func != 'function') {\n    throw new TypeError(FUNC_ERROR_TEXT);\n  }\n  wait = toNumber(wait) || 0;\n  if (isObject(options)) {\n    leading = !!options.leading;\n    maxing = 'maxWait' in options;\n    maxWait = maxing ? nativeMax(toNumber(options.maxWait) || 0, wait) : maxWait;\n    trailing = 'trailing' in options ? !!options.trailing : trailing;\n  }\n\n  function invokeFunc(time) {\n    var args = lastArgs,\n        thisArg = lastThis;\n\n    lastArgs = lastThis = undefined;\n    lastInvokeTime = time;\n    result = func.apply(thisArg, args);\n    return result;\n  }\n\n  function leadingEdge(time) {\n    // Reset any `maxWait` timer.\n    lastInvokeTime = time;\n    // Start the timer for the trailing edge.\n    timerId = setTimeout(timerExpired, wait);\n    // Invoke the leading edge.\n    return leading ? invokeFunc(time) : result;\n  }\n\n  function remainingWait(time) {\n    var timeSinceLastCall = time - lastCallTime,\n        timeSinceLastInvoke = time - lastInvokeTime,\n        result = wait - timeSinceLastCall;\n\n    return maxing ? nativeMin(result, maxWait - timeSinceLastInvoke) : result;\n  }\n\n  function shouldInvoke(time) {\n    var timeSinceLastCall = time - lastCallTime,\n        timeSinceLastInvoke = time - lastInvokeTime;\n\n    // Either this is the first call, activity has stopped and we're at the\n    // trailing edge, the system time has gone backwards and we're treating\n    // it as the trailing edge, or we've hit the `maxWait` limit.\n    return (lastCallTime === undefined || (timeSinceLastCall >= wait) ||\n      (timeSinceLastCall < 0) || (maxing && timeSinceLastInvoke >= maxWait));\n  }\n\n  function timerExpired() {\n    var time = now();\n    if (shouldInvoke(time)) {\n      return trailingEdge(time);\n    }\n    // Restart the timer.\n    timerId = setTimeout(timerExpired, remainingWait(time));\n  }\n\n  function trailingEdge(time) {\n    timerId = undefined;\n\n    // Only invoke if we have `lastArgs` which means `func` has been\n    // debounced at least once.\n    if (trailing && lastArgs) {\n      return invokeFunc(time);\n    }\n    lastArgs = lastThis = undefined;\n    return result;\n  }\n\n  function cancel() {\n    if (timerId !== undefined) {\n      clearTimeout(timerId);\n    }\n    lastInvokeTime = 0;\n    lastArgs = lastCallTime = lastThis = timerId = undefined;\n  }\n\n  function flush() {\n    return timerId === undefined ? result : trailingEdge(now());\n  }\n\n  function debounced() {\n    var time = now(),\n        isInvoking = shouldInvoke(time);\n\n    lastArgs = arguments;\n    lastThis = this;\n    lastCallTime = time;\n\n    if (isInvoking) {\n      if (timerId === undefined) {\n        return leadingEdge(lastCallTime);\n      }\n      if (maxing) {\n        // Handle invocations in a tight loop.\n        timerId = setTimeout(timerExpired, wait);\n        return invokeFunc(lastCallTime);\n      }\n    }\n    if (timerId === undefined) {\n      timerId = setTimeout(timerExpired, wait);\n    }\n    return result;\n  }\n  debounced.cancel = cancel;\n  debounced.flush = flush;\n  return debounced;\n}\n\n/**\n * Checks if `value` is the\n * [language type](http://www.ecma-international.org/ecma-262/7.0/#sec-ecmascript-language-types)\n * of `Object`. (e.g. arrays, functions, objects, regexes, `new Number(0)`, and `new String('')`)\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an object, else `false`.\n * @example\n *\n * _.isObject({});\n * // => true\n *\n * _.isObject([1, 2, 3]);\n * // => true\n *\n * _.isObject(_.noop);\n * // => true\n *\n * _.isObject(null);\n * // => false\n */\nfunction isObject(value) {\n  var type = typeof value;\n  return !!value && (type == 'object' || type == 'function');\n}\n\n/**\n * Checks if `value` is object-like. A value is object-like if it's not `null`\n * and has a `typeof` result of \"object\".\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is object-like, else `false`.\n * @example\n *\n * _.isObjectLike({});\n * // => true\n *\n * _.isObjectLike([1, 2, 3]);\n * // => true\n *\n * _.isObjectLike(_.noop);\n * // => false\n *\n * _.isObjectLike(null);\n * // => false\n */\nfunction isObjectLike(value) {\n  return !!value && typeof value == 'object';\n}\n\n/**\n * Checks if `value` is classified as a `Symbol` primitive or object.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a symbol, else `false`.\n * @example\n *\n * _.isSymbol(Symbol.iterator);\n * // => true\n *\n * _.isSymbol('abc');\n * // => false\n */\nfunction isSymbol(value) {\n  return typeof value == 'symbol' ||\n    (isObjectLike(value) && objectToString.call(value) == symbolTag);\n}\n\n/**\n * Converts `value` to a number.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to process.\n * @returns {number} Returns the number.\n * @example\n *\n * _.toNumber(3.2);\n * // => 3.2\n *\n * _.toNumber(Number.MIN_VALUE);\n * // => 5e-324\n *\n * _.toNumber(Infinity);\n * // => Infinity\n *\n * _.toNumber('3.2');\n * // => 3.2\n */\nfunction toNumber(value) {\n  if (typeof value == 'number') {\n    return value;\n  }\n  if (isSymbol(value)) {\n    return NAN;\n  }\n  if (isObject(value)) {\n    var other = typeof value.valueOf == 'function' ? value.valueOf() : value;\n    value = isObject(other) ? (other + '') : other;\n  }\n  if (typeof value != 'string') {\n    return value === 0 ? value : +value;\n  }\n  value = value.replace(reTrim, '');\n  var isBinary = reIsBinary.test(value);\n  return (isBinary || reIsOctal.test(value))\n    ? freeParseInt(value.slice(2), isBinary ? 2 : 8)\n    : (reIsBadHex.test(value) ? NAN : +value);\n}\n\nmodule.exports = debounce;\n", "import { useState, useCallback, useLayoutEffect, useEffect, useRef, useMemo } from 'react';\nimport debounce from 'lodash.debounce';\n\n// src/useBoolean/useBoolean.ts\nfunction useBoolean(defaultValue = false) {\n  if (typeof defaultValue !== \"boolean\") {\n    throw new Error(\"defaultValue must be `true` or `false`\");\n  }\n  const [value, setValue] = useState(defaultValue);\n  const setTrue = useCallback(() => {\n    setValue(true);\n  }, []);\n  const setFalse = useCallback(() => {\n    setValue(false);\n  }, []);\n  const toggle = useCallback(() => {\n    setValue((x) => !x);\n  }, []);\n  return { value, setValue, setTrue, setFalse, toggle };\n}\nvar useIsomorphicLayoutEffect = typeof window !== \"undefined\" ? useLayoutEffect : useEffect;\n\n// src/useEventListener/useEventListener.ts\nfunction useEventListener(eventName, handler, element, options) {\n  const savedHandler = useRef(handler);\n  useIsomorphicLayoutEffect(() => {\n    savedHandler.current = handler;\n  }, [handler]);\n  useEffect(() => {\n    const targetElement = (element == null ? void 0 : element.current) ?? window;\n    if (!(targetElement && targetElement.addEventListener))\n      return;\n    const listener = (event) => {\n      savedHandler.current(event);\n    };\n    targetElement.addEventListener(eventName, listener, options);\n    return () => {\n      targetElement.removeEventListener(eventName, listener, options);\n    };\n  }, [eventName, element, options]);\n}\n\n// src/useClickAnyWhere/useClickAnyWhere.ts\nfunction useClickAnyWhere(handler) {\n  useEventListener(\"click\", (event) => {\n    handler(event);\n  });\n}\nfunction useCopyToClipboard() {\n  const [copiedText, setCopiedText] = useState(null);\n  const copy = useCallback(async (text) => {\n    if (!(navigator == null ? void 0 : navigator.clipboard)) {\n      console.warn(\"Clipboard not supported\");\n      return false;\n    }\n    try {\n      await navigator.clipboard.writeText(text);\n      setCopiedText(text);\n      return true;\n    } catch (error) {\n      console.warn(\"Copy failed\", error);\n      setCopiedText(null);\n      return false;\n    }\n  }, []);\n  return [copiedText, copy];\n}\nfunction useCounter(initialValue) {\n  const [count, setCount] = useState(initialValue ?? 0);\n  const increment = useCallback(() => {\n    setCount((x) => x + 1);\n  }, []);\n  const decrement = useCallback(() => {\n    setCount((x) => x - 1);\n  }, []);\n  const reset = useCallback(() => {\n    setCount(initialValue ?? 0);\n  }, [initialValue]);\n  return {\n    count,\n    increment,\n    decrement,\n    reset,\n    setCount\n  };\n}\nfunction useInterval(callback, delay) {\n  const savedCallback = useRef(callback);\n  useIsomorphicLayoutEffect(() => {\n    savedCallback.current = callback;\n  }, [callback]);\n  useEffect(() => {\n    if (delay === null) {\n      return;\n    }\n    const id = setInterval(() => {\n      savedCallback.current();\n    }, delay);\n    return () => {\n      clearInterval(id);\n    };\n  }, [delay]);\n}\n\n// src/useCountdown/useCountdown.ts\nfunction useCountdown({\n  countStart,\n  countStop = 0,\n  intervalMs = 1e3,\n  isIncrement = false\n}) {\n  const {\n    count,\n    increment,\n    decrement,\n    reset: resetCounter\n  } = useCounter(countStart);\n  const {\n    value: isCountdownRunning,\n    setTrue: startCountdown,\n    setFalse: stopCountdown\n  } = useBoolean(false);\n  const resetCountdown = useCallback(() => {\n    stopCountdown();\n    resetCounter();\n  }, [stopCountdown, resetCounter]);\n  const countdownCallback = useCallback(() => {\n    if (count === countStop) {\n      stopCountdown();\n      return;\n    }\n    if (isIncrement) {\n      increment();\n    } else {\n      decrement();\n    }\n  }, [count, countStop, decrement, increment, isIncrement, stopCountdown]);\n  useInterval(countdownCallback, isCountdownRunning ? intervalMs : null);\n  return [count, { startCountdown, stopCountdown, resetCountdown }];\n}\nfunction useEventCallback(fn) {\n  const ref = useRef(() => {\n    throw new Error(\"Cannot call an event handler while rendering.\");\n  });\n  useIsomorphicLayoutEffect(() => {\n    ref.current = fn;\n  }, [fn]);\n  return useCallback((...args) => {\n    var _a;\n    return (_a = ref.current) == null ? void 0 : _a.call(ref, ...args);\n  }, [ref]);\n}\n\n// src/useLocalStorage/useLocalStorage.ts\nvar IS_SERVER = typeof window === \"undefined\";\nfunction useLocalStorage(key, initialValue, options = {}) {\n  const { initializeWithValue = true } = options;\n  const serializer = useCallback(\n    (value) => {\n      if (options.serializer) {\n        return options.serializer(value);\n      }\n      return JSON.stringify(value);\n    },\n    [options]\n  );\n  const deserializer = useCallback(\n    (value) => {\n      if (options.deserializer) {\n        return options.deserializer(value);\n      }\n      if (value === \"undefined\") {\n        return void 0;\n      }\n      const defaultValue = initialValue instanceof Function ? initialValue() : initialValue;\n      let parsed;\n      try {\n        parsed = JSON.parse(value);\n      } catch (error) {\n        console.error(\"Error parsing JSON:\", error);\n        return defaultValue;\n      }\n      return parsed;\n    },\n    [options, initialValue]\n  );\n  const readValue = useCallback(() => {\n    const initialValueToUse = initialValue instanceof Function ? initialValue() : initialValue;\n    if (IS_SERVER) {\n      return initialValueToUse;\n    }\n    try {\n      const raw = window.localStorage.getItem(key);\n      return raw ? deserializer(raw) : initialValueToUse;\n    } catch (error) {\n      console.warn(`Error reading localStorage key \\u201C${key}\\u201D:`, error);\n      return initialValueToUse;\n    }\n  }, [initialValue, key, deserializer]);\n  const [storedValue, setStoredValue] = useState(() => {\n    if (initializeWithValue) {\n      return readValue();\n    }\n    return initialValue instanceof Function ? initialValue() : initialValue;\n  });\n  const setValue = useEventCallback((value) => {\n    if (IS_SERVER) {\n      console.warn(\n        `Tried setting localStorage key \\u201C${key}\\u201D even though environment is not a client`\n      );\n    }\n    try {\n      const newValue = value instanceof Function ? value(readValue()) : value;\n      window.localStorage.setItem(key, serializer(newValue));\n      setStoredValue(newValue);\n      window.dispatchEvent(new StorageEvent(\"local-storage\", { key }));\n    } catch (error) {\n      console.warn(`Error setting localStorage key \\u201C${key}\\u201D:`, error);\n    }\n  });\n  const removeValue = useEventCallback(() => {\n    if (IS_SERVER) {\n      console.warn(\n        `Tried removing localStorage key \\u201C${key}\\u201D even though environment is not a client`\n      );\n    }\n    const defaultValue = initialValue instanceof Function ? initialValue() : initialValue;\n    window.localStorage.removeItem(key);\n    setStoredValue(defaultValue);\n    window.dispatchEvent(new StorageEvent(\"local-storage\", { key }));\n  });\n  useEffect(() => {\n    setStoredValue(readValue());\n  }, [key]);\n  const handleStorageChange = useCallback(\n    (event) => {\n      if (event.key && event.key !== key) {\n        return;\n      }\n      setStoredValue(readValue());\n    },\n    [key, readValue]\n  );\n  useEventListener(\"storage\", handleStorageChange);\n  useEventListener(\"local-storage\", handleStorageChange);\n  return [storedValue, setValue, removeValue];\n}\nvar IS_SERVER2 = typeof window === \"undefined\";\nfunction useMediaQuery(query, {\n  defaultValue = false,\n  initializeWithValue = true\n} = {}) {\n  const getMatches = (query2) => {\n    if (IS_SERVER2) {\n      return defaultValue;\n    }\n    return window.matchMedia(query2).matches;\n  };\n  const [matches, setMatches] = useState(() => {\n    if (initializeWithValue) {\n      return getMatches(query);\n    }\n    return defaultValue;\n  });\n  function handleChange() {\n    setMatches(getMatches(query));\n  }\n  useIsomorphicLayoutEffect(() => {\n    const matchMedia = window.matchMedia(query);\n    handleChange();\n    if (matchMedia.addListener) {\n      matchMedia.addListener(handleChange);\n    } else {\n      matchMedia.addEventListener(\"change\", handleChange);\n    }\n    return () => {\n      if (matchMedia.removeListener) {\n        matchMedia.removeListener(handleChange);\n      } else {\n        matchMedia.removeEventListener(\"change\", handleChange);\n      }\n    };\n  }, [query]);\n  return matches;\n}\n\n// src/useDarkMode/useDarkMode.ts\nvar COLOR_SCHEME_QUERY = \"(prefers-color-scheme: dark)\";\nvar LOCAL_STORAGE_KEY = \"usehooks-ts-dark-mode\";\nfunction useDarkMode(options = {}) {\n  const {\n    defaultValue,\n    localStorageKey = LOCAL_STORAGE_KEY,\n    initializeWithValue = true\n  } = options;\n  const isDarkOS = useMediaQuery(COLOR_SCHEME_QUERY, {\n    initializeWithValue,\n    defaultValue\n  });\n  const [isDarkMode, setDarkMode] = useLocalStorage(\n    localStorageKey,\n    defaultValue ?? isDarkOS ?? false,\n    { initializeWithValue }\n  );\n  useIsomorphicLayoutEffect(() => {\n    if (isDarkOS !== isDarkMode) {\n      setDarkMode(isDarkOS);\n    }\n  }, [isDarkOS]);\n  return {\n    isDarkMode,\n    toggle: () => {\n      setDarkMode((prev) => !prev);\n    },\n    enable: () => {\n      setDarkMode(true);\n    },\n    disable: () => {\n      setDarkMode(false);\n    },\n    set: (value) => {\n      setDarkMode(value);\n    }\n  };\n}\nfunction useUnmount(func) {\n  const funcRef = useRef(func);\n  funcRef.current = func;\n  useEffect(\n    () => () => {\n      funcRef.current();\n    },\n    []\n  );\n}\n\n// src/useDebounceCallback/useDebounceCallback.ts\nfunction useDebounceCallback(func, delay = 500, options) {\n  const debouncedFunc = useRef();\n  useUnmount(() => {\n    if (debouncedFunc.current) {\n      debouncedFunc.current.cancel();\n    }\n  });\n  const debounced = useMemo(() => {\n    const debouncedFuncInstance = debounce(func, delay, options);\n    const wrappedFunc = (...args) => {\n      return debouncedFuncInstance(...args);\n    };\n    wrappedFunc.cancel = () => {\n      debouncedFuncInstance.cancel();\n    };\n    wrappedFunc.isPending = () => {\n      return !!debouncedFunc.current;\n    };\n    wrappedFunc.flush = () => {\n      return debouncedFuncInstance.flush();\n    };\n    return wrappedFunc;\n  }, [func, delay, options]);\n  useEffect(() => {\n    debouncedFunc.current = debounce(func, delay, options);\n  }, [func, delay, options]);\n  return debounced;\n}\nfunction useDebounceValue(initialValue, delay, options) {\n  const eq = (options == null ? void 0 : options.equalityFn) ?? ((left, right) => left === right);\n  const unwrappedInitialValue = initialValue instanceof Function ? initialValue() : initialValue;\n  const [debouncedValue, setDebouncedValue] = useState(unwrappedInitialValue);\n  const previousValueRef = useRef(unwrappedInitialValue);\n  const updateDebouncedValue = useDebounceCallback(\n    setDebouncedValue,\n    delay,\n    options\n  );\n  if (!eq(previousValueRef.current, unwrappedInitialValue)) {\n    updateDebouncedValue(unwrappedInitialValue);\n    previousValueRef.current = unwrappedInitialValue;\n  }\n  return [debouncedValue, updateDebouncedValue];\n}\nfunction useDocumentTitle(title, options = {}) {\n  const { preserveTitleOnUnmount = true } = options;\n  const defaultTitle = useRef(null);\n  useIsomorphicLayoutEffect(() => {\n    defaultTitle.current = window.document.title;\n  }, []);\n  useIsomorphicLayoutEffect(() => {\n    window.document.title = title;\n  }, [title]);\n  useUnmount(() => {\n    if (!preserveTitleOnUnmount && defaultTitle.current) {\n      window.document.title = defaultTitle.current;\n    }\n  });\n}\nfunction useHover(elementRef) {\n  const [value, setValue] = useState(false);\n  const handleMouseEnter = () => {\n    setValue(true);\n  };\n  const handleMouseLeave = () => {\n    setValue(false);\n  };\n  useEventListener(\"mouseenter\", handleMouseEnter, elementRef);\n  useEventListener(\"mouseleave\", handleMouseLeave, elementRef);\n  return value;\n}\nfunction useIntersectionObserver({\n  threshold = 0,\n  root = null,\n  rootMargin = \"0%\",\n  freezeOnceVisible = false,\n  initialIsIntersecting = false,\n  onChange\n} = {}) {\n  var _a;\n  const [ref, setRef] = useState(null);\n  const [state, setState] = useState(() => ({\n    isIntersecting: initialIsIntersecting,\n    entry: void 0\n  }));\n  const callbackRef = useRef();\n  callbackRef.current = onChange;\n  const frozen = ((_a = state.entry) == null ? void 0 : _a.isIntersecting) && freezeOnceVisible;\n  useEffect(() => {\n    if (!ref)\n      return;\n    if (!(\"IntersectionObserver\" in window))\n      return;\n    if (frozen)\n      return;\n    let unobserve;\n    const observer = new IntersectionObserver(\n      (entries) => {\n        const thresholds = Array.isArray(observer.thresholds) ? observer.thresholds : [observer.thresholds];\n        entries.forEach((entry) => {\n          const isIntersecting = entry.isIntersecting && thresholds.some((threshold2) => entry.intersectionRatio >= threshold2);\n          setState({ isIntersecting, entry });\n          if (callbackRef.current) {\n            callbackRef.current(isIntersecting, entry);\n          }\n          if (isIntersecting && freezeOnceVisible && unobserve) {\n            unobserve();\n            unobserve = void 0;\n          }\n        });\n      },\n      { threshold, root, rootMargin }\n    );\n    observer.observe(ref);\n    return () => {\n      observer.disconnect();\n    };\n  }, [\n    ref,\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    JSON.stringify(threshold),\n    root,\n    rootMargin,\n    frozen,\n    freezeOnceVisible\n  ]);\n  const prevRef = useRef(null);\n  useEffect(() => {\n    var _a2;\n    if (!ref && ((_a2 = state.entry) == null ? void 0 : _a2.target) && !freezeOnceVisible && !frozen && prevRef.current !== state.entry.target) {\n      prevRef.current = state.entry.target;\n      setState({ isIntersecting: initialIsIntersecting, entry: void 0 });\n    }\n  }, [ref, state.entry, freezeOnceVisible, frozen, initialIsIntersecting]);\n  const result = [\n    setRef,\n    !!state.isIntersecting,\n    state.entry\n  ];\n  result.ref = result[0];\n  result.isIntersecting = result[1];\n  result.entry = result[2];\n  return result;\n}\nfunction useIsClient() {\n  const [isClient, setClient] = useState(false);\n  useEffect(() => {\n    setClient(true);\n  }, []);\n  return isClient;\n}\nfunction useIsMounted() {\n  const isMounted = useRef(false);\n  useEffect(() => {\n    isMounted.current = true;\n    return () => {\n      isMounted.current = false;\n    };\n  }, []);\n  return useCallback(() => isMounted.current, []);\n}\nfunction useMap(initialState = /* @__PURE__ */ new Map()) {\n  const [map, setMap] = useState(new Map(initialState));\n  const actions = {\n    set: useCallback((key, value) => {\n      setMap((prev) => {\n        const copy = new Map(prev);\n        copy.set(key, value);\n        return copy;\n      });\n    }, []),\n    setAll: useCallback((entries) => {\n      setMap(() => new Map(entries));\n    }, []),\n    remove: useCallback((key) => {\n      setMap((prev) => {\n        const copy = new Map(prev);\n        copy.delete(key);\n        return copy;\n      });\n    }, []),\n    reset: useCallback(() => {\n      setMap(() => /* @__PURE__ */ new Map());\n    }, [])\n  };\n  return [map, actions];\n}\n\n// src/useOnClickOutside/useOnClickOutside.ts\nfunction useOnClickOutside(ref, handler, eventType = \"mousedown\", eventListenerOptions = {}) {\n  useEventListener(\n    eventType,\n    (event) => {\n      const target = event.target;\n      if (!target || !target.isConnected) {\n        return;\n      }\n      const isOutside = Array.isArray(ref) ? ref.filter((r) => Boolean(r.current)).every((r) => r.current && !r.current.contains(target)) : ref.current && !ref.current.contains(target);\n      if (isOutside) {\n        handler(event);\n      }\n    },\n    void 0,\n    eventListenerOptions\n  );\n}\nvar IS_SERVER3 = typeof window === \"undefined\";\nfunction useReadLocalStorage(key, options = {}) {\n  let { initializeWithValue = true } = options;\n  if (IS_SERVER3) {\n    initializeWithValue = false;\n  }\n  const deserializer = useCallback(\n    (value) => {\n      if (options.deserializer) {\n        return options.deserializer(value);\n      }\n      if (value === \"undefined\") {\n        return void 0;\n      }\n      let parsed;\n      try {\n        parsed = JSON.parse(value);\n      } catch (error) {\n        console.error(\"Error parsing JSON:\", error);\n        return null;\n      }\n      return parsed;\n    },\n    [options]\n  );\n  const readValue = useCallback(() => {\n    if (IS_SERVER3) {\n      return null;\n    }\n    try {\n      const raw = window.localStorage.getItem(key);\n      return raw ? deserializer(raw) : null;\n    } catch (error) {\n      console.warn(`Error reading localStorage key \\u201C${key}\\u201D:`, error);\n      return null;\n    }\n  }, [key, deserializer]);\n  const [storedValue, setStoredValue] = useState(() => {\n    if (initializeWithValue) {\n      return readValue();\n    }\n    return void 0;\n  });\n  useEffect(() => {\n    setStoredValue(readValue());\n  }, [key]);\n  const handleStorageChange = useCallback(\n    (event) => {\n      if (event.key && event.key !== key) {\n        return;\n      }\n      setStoredValue(readValue());\n    },\n    [key, readValue]\n  );\n  useEventListener(\"storage\", handleStorageChange);\n  useEventListener(\"local-storage\", handleStorageChange);\n  return storedValue;\n}\nvar initialSize = {\n  width: void 0,\n  height: void 0\n};\nfunction useResizeObserver(options) {\n  const { ref, box = \"content-box\" } = options;\n  const [{ width, height }, setSize] = useState(initialSize);\n  const isMounted = useIsMounted();\n  const previousSize = useRef({ ...initialSize });\n  const onResize = useRef(void 0);\n  onResize.current = options.onResize;\n  useEffect(() => {\n    if (!ref.current)\n      return;\n    if (typeof window === \"undefined\" || !(\"ResizeObserver\" in window))\n      return;\n    const observer = new ResizeObserver(([entry]) => {\n      const boxProp = box === \"border-box\" ? \"borderBoxSize\" : box === \"device-pixel-content-box\" ? \"devicePixelContentBoxSize\" : \"contentBoxSize\";\n      const newWidth = extractSize(entry, boxProp, \"inlineSize\");\n      const newHeight = extractSize(entry, boxProp, \"blockSize\");\n      const hasChanged = previousSize.current.width !== newWidth || previousSize.current.height !== newHeight;\n      if (hasChanged) {\n        const newSize = { width: newWidth, height: newHeight };\n        previousSize.current.width = newWidth;\n        previousSize.current.height = newHeight;\n        if (onResize.current) {\n          onResize.current(newSize);\n        } else {\n          if (isMounted()) {\n            setSize(newSize);\n          }\n        }\n      }\n    });\n    observer.observe(ref.current, { box });\n    return () => {\n      observer.disconnect();\n    };\n  }, [box, ref, isMounted]);\n  return { width, height };\n}\nfunction extractSize(entry, box, sizeType) {\n  if (!entry[box]) {\n    if (box === \"contentBoxSize\") {\n      return entry.contentRect[sizeType === \"inlineSize\" ? \"width\" : \"height\"];\n    }\n    return void 0;\n  }\n  return Array.isArray(entry[box]) ? entry[box][0][sizeType] : (\n    // @ts-ignore Support Firefox's non-standard behavior\n    entry[box][sizeType]\n  );\n}\nvar IS_SERVER4 = typeof window === \"undefined\";\nfunction useScreen(options = {}) {\n  let { initializeWithValue = true } = options;\n  if (IS_SERVER4) {\n    initializeWithValue = false;\n  }\n  const readScreen = () => {\n    if (IS_SERVER4) {\n      return void 0;\n    }\n    return window.screen;\n  };\n  const [screen, setScreen] = useState(() => {\n    if (initializeWithValue) {\n      return readScreen();\n    }\n    return void 0;\n  });\n  const debouncedSetScreen = useDebounceCallback(\n    setScreen,\n    options.debounceDelay\n  );\n  function handleSize() {\n    const newScreen = readScreen();\n    const setSize = options.debounceDelay ? debouncedSetScreen : setScreen;\n    if (newScreen) {\n      const {\n        width,\n        height,\n        availHeight,\n        availWidth,\n        colorDepth,\n        orientation,\n        pixelDepth\n      } = newScreen;\n      setSize({\n        width,\n        height,\n        availHeight,\n        availWidth,\n        colorDepth,\n        orientation,\n        pixelDepth\n      });\n    }\n  }\n  useEventListener(\"resize\", handleSize);\n  useIsomorphicLayoutEffect(() => {\n    handleSize();\n  }, []);\n  return screen;\n}\nvar cachedScriptStatuses = /* @__PURE__ */ new Map();\nfunction getScriptNode(src) {\n  const node = document.querySelector(\n    `script[src=\"${src}\"]`\n  );\n  const status = node == null ? void 0 : node.getAttribute(\"data-status\");\n  return {\n    node,\n    status\n  };\n}\nfunction useScript(src, options) {\n  const [status, setStatus] = useState(() => {\n    if (!src || (options == null ? void 0 : options.shouldPreventLoad)) {\n      return \"idle\";\n    }\n    if (typeof window === \"undefined\") {\n      return \"loading\";\n    }\n    return cachedScriptStatuses.get(src) ?? \"loading\";\n  });\n  useEffect(() => {\n    if (!src || (options == null ? void 0 : options.shouldPreventLoad)) {\n      return;\n    }\n    const cachedScriptStatus = cachedScriptStatuses.get(src);\n    if (cachedScriptStatus === \"ready\" || cachedScriptStatus === \"error\") {\n      setStatus(cachedScriptStatus);\n      return;\n    }\n    const script = getScriptNode(src);\n    let scriptNode = script.node;\n    if (!scriptNode) {\n      scriptNode = document.createElement(\"script\");\n      scriptNode.src = src;\n      scriptNode.async = true;\n      if (options == null ? void 0 : options.id) {\n        scriptNode.id = options.id;\n      }\n      scriptNode.setAttribute(\"data-status\", \"loading\");\n      document.body.appendChild(scriptNode);\n      const setAttributeFromEvent = (event) => {\n        const scriptStatus = event.type === \"load\" ? \"ready\" : \"error\";\n        scriptNode == null ? void 0 : scriptNode.setAttribute(\"data-status\", scriptStatus);\n      };\n      scriptNode.addEventListener(\"load\", setAttributeFromEvent);\n      scriptNode.addEventListener(\"error\", setAttributeFromEvent);\n    } else {\n      setStatus(script.status ?? cachedScriptStatus ?? \"loading\");\n    }\n    const setStateFromEvent = (event) => {\n      const newStatus = event.type === \"load\" ? \"ready\" : \"error\";\n      setStatus(newStatus);\n      cachedScriptStatuses.set(src, newStatus);\n    };\n    scriptNode.addEventListener(\"load\", setStateFromEvent);\n    scriptNode.addEventListener(\"error\", setStateFromEvent);\n    return () => {\n      if (scriptNode) {\n        scriptNode.removeEventListener(\"load\", setStateFromEvent);\n        scriptNode.removeEventListener(\"error\", setStateFromEvent);\n      }\n      if (scriptNode && (options == null ? void 0 : options.removeOnUnmount)) {\n        scriptNode.remove();\n        cachedScriptStatuses.delete(src);\n      }\n    };\n  }, [src, options == null ? void 0 : options.shouldPreventLoad, options == null ? void 0 : options.removeOnUnmount, options == null ? void 0 : options.id]);\n  return status;\n}\nvar IS_SERVER5 = typeof window === \"undefined\";\nfunction useScrollLock(options = {}) {\n  const { autoLock = true, lockTarget, widthReflow = true } = options;\n  const [isLocked, setIsLocked] = useState(false);\n  const target = useRef(null);\n  const originalStyle = useRef(null);\n  const lock = () => {\n    if (target.current) {\n      const { overflow, paddingRight } = target.current.style;\n      originalStyle.current = { overflow, paddingRight };\n      if (widthReflow) {\n        const offsetWidth = target.current === document.body ? window.innerWidth : target.current.offsetWidth;\n        const currentPaddingRight = parseInt(window.getComputedStyle(target.current).paddingRight, 10) || 0;\n        const scrollbarWidth = offsetWidth - target.current.scrollWidth;\n        target.current.style.paddingRight = `${scrollbarWidth + currentPaddingRight}px`;\n      }\n      target.current.style.overflow = \"hidden\";\n      setIsLocked(true);\n    }\n  };\n  const unlock = () => {\n    if (target.current && originalStyle.current) {\n      target.current.style.overflow = originalStyle.current.overflow;\n      if (widthReflow) {\n        target.current.style.paddingRight = originalStyle.current.paddingRight;\n      }\n    }\n    setIsLocked(false);\n  };\n  useIsomorphicLayoutEffect(() => {\n    if (IS_SERVER5)\n      return;\n    if (lockTarget) {\n      target.current = typeof lockTarget === \"string\" ? document.querySelector(lockTarget) : lockTarget;\n    }\n    if (!target.current) {\n      target.current = document.body;\n    }\n    if (autoLock) {\n      lock();\n    }\n    return () => {\n      unlock();\n    };\n  }, [autoLock, lockTarget, widthReflow]);\n  return { isLocked, lock, unlock };\n}\nvar IS_SERVER6 = typeof window === \"undefined\";\nfunction useSessionStorage(key, initialValue, options = {}) {\n  const { initializeWithValue = true } = options;\n  const serializer = useCallback(\n    (value) => {\n      if (options.serializer) {\n        return options.serializer(value);\n      }\n      return JSON.stringify(value);\n    },\n    [options]\n  );\n  const deserializer = useCallback(\n    (value) => {\n      if (options.deserializer) {\n        return options.deserializer(value);\n      }\n      if (value === \"undefined\") {\n        return void 0;\n      }\n      const defaultValue = initialValue instanceof Function ? initialValue() : initialValue;\n      let parsed;\n      try {\n        parsed = JSON.parse(value);\n      } catch (error) {\n        console.error(\"Error parsing JSON:\", error);\n        return defaultValue;\n      }\n      return parsed;\n    },\n    [options, initialValue]\n  );\n  const readValue = useCallback(() => {\n    const initialValueToUse = initialValue instanceof Function ? initialValue() : initialValue;\n    if (IS_SERVER6) {\n      return initialValueToUse;\n    }\n    try {\n      const raw = window.sessionStorage.getItem(key);\n      return raw ? deserializer(raw) : initialValueToUse;\n    } catch (error) {\n      console.warn(`Error reading sessionStorage key \\u201C${key}\\u201D:`, error);\n      return initialValueToUse;\n    }\n  }, [initialValue, key, deserializer]);\n  const [storedValue, setStoredValue] = useState(() => {\n    if (initializeWithValue) {\n      return readValue();\n    }\n    return initialValue instanceof Function ? initialValue() : initialValue;\n  });\n  const setValue = useEventCallback((value) => {\n    if (IS_SERVER6) {\n      console.warn(\n        `Tried setting sessionStorage key \\u201C${key}\\u201D even though environment is not a client`\n      );\n    }\n    try {\n      const newValue = value instanceof Function ? value(readValue()) : value;\n      window.sessionStorage.setItem(key, serializer(newValue));\n      setStoredValue(newValue);\n      window.dispatchEvent(new StorageEvent(\"session-storage\", { key }));\n    } catch (error) {\n      console.warn(`Error setting sessionStorage key \\u201C${key}\\u201D:`, error);\n    }\n  });\n  const removeValue = useEventCallback(() => {\n    if (IS_SERVER6) {\n      console.warn(\n        `Tried removing sessionStorage key \\u201C${key}\\u201D even though environment is not a client`\n      );\n    }\n    const defaultValue = initialValue instanceof Function ? initialValue() : initialValue;\n    window.sessionStorage.removeItem(key);\n    setStoredValue(defaultValue);\n    window.dispatchEvent(new StorageEvent(\"session-storage\", { key }));\n  });\n  useEffect(() => {\n    setStoredValue(readValue());\n  }, [key]);\n  const handleStorageChange = useCallback(\n    (event) => {\n      if (event.key && event.key !== key) {\n        return;\n      }\n      setStoredValue(readValue());\n    },\n    [key, readValue]\n  );\n  useEventListener(\"storage\", handleStorageChange);\n  useEventListener(\"session-storage\", handleStorageChange);\n  return [storedValue, setValue, removeValue];\n}\nfunction useStep(maxStep) {\n  const [currentStep, setCurrentStep] = useState(1);\n  const canGoToNextStep = currentStep + 1 <= maxStep;\n  const canGoToPrevStep = currentStep - 1 > 0;\n  const setStep = useCallback(\n    (step) => {\n      const newStep = step instanceof Function ? step(currentStep) : step;\n      if (newStep >= 1 && newStep <= maxStep) {\n        setCurrentStep(newStep);\n        return;\n      }\n      throw new Error(\"Step not valid\");\n    },\n    [maxStep, currentStep]\n  );\n  const goToNextStep = useCallback(() => {\n    if (canGoToNextStep) {\n      setCurrentStep((step) => step + 1);\n    }\n  }, [canGoToNextStep]);\n  const goToPrevStep = useCallback(() => {\n    if (canGoToPrevStep) {\n      setCurrentStep((step) => step - 1);\n    }\n  }, [canGoToPrevStep]);\n  const reset = useCallback(() => {\n    setCurrentStep(1);\n  }, []);\n  return [\n    currentStep,\n    {\n      goToNextStep,\n      goToPrevStep,\n      canGoToNextStep,\n      canGoToPrevStep,\n      setStep,\n      reset\n    }\n  ];\n}\n\n// src/useTernaryDarkMode/useTernaryDarkMode.ts\nvar COLOR_SCHEME_QUERY2 = \"(prefers-color-scheme: dark)\";\nvar LOCAL_STORAGE_KEY2 = \"usehooks-ts-ternary-dark-mode\";\nfunction useTernaryDarkMode({\n  defaultValue = \"system\",\n  localStorageKey = LOCAL_STORAGE_KEY2,\n  initializeWithValue = true\n} = {}) {\n  const isDarkOS = useMediaQuery(COLOR_SCHEME_QUERY2, { initializeWithValue });\n  const [mode, setMode] = useLocalStorage(localStorageKey, defaultValue, {\n    initializeWithValue\n  });\n  const isDarkMode = mode === \"dark\" || mode === \"system\" && isDarkOS;\n  const toggleTernaryDarkMode = () => {\n    const modes = [\"light\", \"system\", \"dark\"];\n    setMode((prevMode) => {\n      const nextIndex = (modes.indexOf(prevMode) + 1) % modes.length;\n      return modes[nextIndex];\n    });\n  };\n  return {\n    isDarkMode,\n    ternaryDarkMode: mode,\n    setTernaryDarkMode: setMode,\n    toggleTernaryDarkMode\n  };\n}\nfunction useTimeout(callback, delay) {\n  const savedCallback = useRef(callback);\n  useIsomorphicLayoutEffect(() => {\n    savedCallback.current = callback;\n  }, [callback]);\n  useEffect(() => {\n    if (!delay && delay !== 0) {\n      return;\n    }\n    const id = setTimeout(() => {\n      savedCallback.current();\n    }, delay);\n    return () => {\n      clearTimeout(id);\n    };\n  }, [delay]);\n}\nfunction useToggle(defaultValue) {\n  const [value, setValue] = useState(!!defaultValue);\n  const toggle = useCallback(() => {\n    setValue((x) => !x);\n  }, []);\n  return [value, toggle, setValue];\n}\nvar IS_SERVER7 = typeof window === \"undefined\";\nfunction useWindowSize(options = {}) {\n  let { initializeWithValue = true } = options;\n  if (IS_SERVER7) {\n    initializeWithValue = false;\n  }\n  const [windowSize, setWindowSize] = useState(() => {\n    if (initializeWithValue) {\n      return {\n        width: window.innerWidth,\n        height: window.innerHeight\n      };\n    }\n    return {\n      width: void 0,\n      height: void 0\n    };\n  });\n  const debouncedSetWindowSize = useDebounceCallback(\n    setWindowSize,\n    options.debounceDelay\n  );\n  function handleSize() {\n    const setSize = options.debounceDelay ? debouncedSetWindowSize : setWindowSize;\n    setSize({\n      width: window.innerWidth,\n      height: window.innerHeight\n    });\n  }\n  useEventListener(\"resize\", handleSize);\n  useIsomorphicLayoutEffect(() => {\n    handleSize();\n  }, []);\n  return windowSize;\n}\n\nexport { useBoolean, useClickAnyWhere, useCopyToClipboard, useCountdown, useCounter, useDarkMode, useDebounceCallback, useDebounceValue, useDocumentTitle, useEventCallback, useEventListener, useHover, useIntersectionObserver, useInterval, useIsClient, useIsMounted, useIsomorphicLayoutEffect, useLocalStorage, useMap, useMediaQuery, useOnClickOutside, useReadLocalStorage, useResizeObserver, useScreen, useScript, useScrollLock, useSessionStorage, useStep, useTernaryDarkMode, useTimeout, useToggle, useUnmount, useWindowSize };\n", "import { RemoteTrackPublication } from 'livekit-client';\nimport * as React from 'react';\nimport { useMediaTrackBySourceOrName } from '../../hooks/useMediaTrackBySourceOrName';\nimport type { ParticipantClickEvent, TrackReference } from '@livekit/components-core';\nimport { useEnsureTrackRef } from '../../context';\nimport * as useHooks from 'usehooks-ts';\n\n/** @public */\nexport interface VideoTrackProps extends React.VideoHTMLAttributes<HTMLVideoElement> {\n  /** The track reference of the track to render. */\n  trackRef?: TrackReference;\n  onTrackClick?: (evt: ParticipantClickEvent) => void;\n  onSubscriptionStatusChanged?: (subscribed: boolean) => void;\n  manageSubscription?: boolean;\n}\n\n/**\n * The `VideoTrack` component is responsible for rendering participant video tracks like `camera` and `screen_share`.\n * This component must have access to the participant's context, or alternatively pass it a `Participant` as a property.\n *\n * @example\n * ```tsx\n * <VideoTrack trackRef={trackRef} />\n * ```\n * @see {@link @livekit/components-react#ParticipantTile | ParticipantTile}\n * @public\n */\nexport const VideoTrack: (\n  props: VideoTrackProps & React.RefAttributes<HTMLVideoElement>,\n) => React.ReactNode = /* @__PURE__ */ React.forwardRef<HTMLVideoElement, VideoTrackProps>(\n  function VideoTrack(\n    {\n      onTrackClick,\n      onClick,\n      onSubscriptionStatusChanged,\n      trackRef,\n      manageSubscription,\n      ...props\n    }: VideoTrackProps,\n    ref,\n  ) {\n    const trackReference = useEnsureTrackRef(trackRef);\n\n    const mediaEl = React.useRef<HTMLVideoElement>(null);\n    React.useImperativeHandle(ref, () => mediaEl.current as HTMLVideoElement);\n\n    const intersectionEntry = useHooks.useIntersectionObserver({ root: mediaEl.current });\n\n    const [debouncedIntersectionEntry] = useHooks.useDebounceValue(intersectionEntry, 3000);\n\n    React.useEffect(() => {\n      if (\n        manageSubscription &&\n        trackReference.publication instanceof RemoteTrackPublication &&\n        debouncedIntersectionEntry?.isIntersecting === false &&\n        intersectionEntry?.isIntersecting === false\n      ) {\n        trackReference.publication.setSubscribed(false);\n      }\n    }, [debouncedIntersectionEntry, trackReference, manageSubscription]);\n\n    React.useEffect(() => {\n      if (\n        manageSubscription &&\n        trackReference.publication instanceof RemoteTrackPublication &&\n        intersectionEntry?.isIntersecting === true\n      ) {\n        trackReference.publication.setSubscribed(true);\n      }\n    }, [intersectionEntry, trackReference, manageSubscription]);\n\n    const {\n      elementProps,\n      publication: pub,\n      isSubscribed,\n    } = useMediaTrackBySourceOrName(trackReference, {\n      element: mediaEl,\n      props,\n    });\n\n    React.useEffect(() => {\n      onSubscriptionStatusChanged?.(!!isSubscribed);\n    }, [isSubscribed, onSubscriptionStatusChanged]);\n\n    const clickHandler = (evt: React.MouseEvent<HTMLVideoElement, MouseEvent>) => {\n      onClick?.(evt);\n      onTrackClick?.({ participant: trackReference?.participant, track: pub });\n    };\n\n    return <video ref={mediaEl} {...elementProps} muted={true} onClick={clickHandler}></video>;\n  },\n);\n", "import * as React from 'react';\nimport { useMediaTrackBySourceOrName } from '../../hooks/useMediaTrackBySourceOrName';\nimport type { TrackReference } from '@livekit/components-core';\nimport { log } from '@livekit/components-core';\nimport { RemoteAudioTrack, RemoteTrackPublication } from 'livekit-client';\nimport { useEnsureTrackRef } from '../../context';\n\n/** @public */\nexport interface AudioTrackProps extends React.AudioHTMLAttributes<HTMLAudioElement> {\n  /** The track reference of the track from which the audio is to be rendered. */\n  trackRef?: TrackReference;\n\n  onSubscriptionStatusChanged?: (subscribed: boolean) => void;\n  /** Sets the volume of the audio track. By default, the range is between `0.0` and `1.0`. */\n  volume?: number;\n  /**\n   * Mutes the audio track if set to `true`.\n   * @remarks\n   * If set to `true`, the server will stop sending audio track data to the client.\n   * @alpha\n   */\n  muted?: boolean;\n}\n\n/**\n * The AudioTrack component is responsible for rendering participant audio tracks.\n * This component must have access to the participant's context, or alternatively pass it a `Participant` as a property.\n *\n * @example\n * ```tsx\n *   <ParticipantTile>\n *     <AudioTrack trackRef={trackRef} />\n *   </ParticipantTile>\n * ```\n *\n * @see `ParticipantTile` component\n * @public\n */\nexport const AudioTrack: (\n  props: AudioTrackProps & React.RefAttributes<HTMLAudioElement>,\n) => React.ReactNode = /* @__PURE__ */ React.forwardRef<HTMLAudioElement, AudioTrackProps>(\n  function AudioTrack(\n    { trackRef, onSubscriptionStatusChanged, volume, ...props }: AudioTrackProps,\n    ref,\n  ) {\n    const trackReference = useEnsureTrackRef(trackRef);\n\n    const mediaEl = React.useRef<HTMLAudioElement>(null);\n    React.useImperativeHandle(ref, () => mediaEl.current as HTMLAudioElement);\n\n    const {\n      elementProps,\n      isSubscribed,\n      track,\n      publication: pub,\n    } = useMediaTrackBySourceOrName(trackReference, {\n      element: mediaEl,\n      props,\n    });\n\n    React.useEffect(() => {\n      onSubscriptionStatusChanged?.(!!isSubscribed);\n    }, [isSubscribed, onSubscriptionStatusChanged]);\n\n    React.useEffect(() => {\n      if (track === undefined || volume === undefined) {\n        return;\n      }\n      if (track instanceof RemoteAudioTrack) {\n        track.setVolume(volume);\n      } else {\n        log.warn('Volume can only be set on remote audio tracks.');\n      }\n    }, [volume, track]);\n\n    React.useEffect(() => {\n      if (pub === undefined || props.muted === undefined) {\n        return;\n      }\n      if (pub instanceof RemoteTrackPublication) {\n        pub.setEnabled(!props.muted);\n      } else {\n        log.warn('Can only call setEnabled on remote track publications.');\n      }\n    }, [props.muted, pub, track]);\n\n    return <audio ref={mediaEl} {...elementProps} />;\n  },\n);\n", "import * as React from 'react';\nimport type { Participant } from 'livekit-client';\nimport { Track } from 'livekit-client';\nimport type { ParticipantClickEvent, TrackReferenceOrPlaceholder } from '@livekit/components-core';\nimport { isTrackReference, isTrackReferencePinned } from '@livekit/components-core';\nimport { ConnectionQualityIndicator } from './ConnectionQualityIndicator';\nimport { ParticipantName } from './ParticipantName';\nimport { TrackMutedIndicator } from './TrackMutedIndicator';\nimport {\n  ParticipantContext,\n  TrackRefContext,\n  useEnsureTrackRef,\n  useFeatureContext,\n  useMaybeLayoutContext,\n  useMaybeParticipantContext,\n  useMaybeTrackRefContext,\n} from '../../context';\nimport { FocusToggle } from '../controls/FocusToggle';\nimport { ParticipantPlaceholder } from '../../assets/images';\nimport { LockLockedIcon, ScreenShareIcon } from '../../assets/icons';\nimport { VideoTrack } from './VideoTrack';\nimport { AudioTrack } from './AudioTrack';\nimport { useParticipantTile } from '../../hooks';\nimport { useIsEncrypted } from '../../hooks/useIsEncrypted';\n\n/**\n * The `ParticipantContextIfNeeded` component only creates a `ParticipantContext`\n * if there is no `ParticipantContext` already.\n * @example\n * ```tsx\n * <ParticipantContextIfNeeded participant={trackReference.participant}>\n *  ...\n * </ParticipantContextIfNeeded>\n * ```\n * @public\n */\nexport function ParticipantContextIfNeeded(\n  props: React.PropsWithChildren<{\n    participant?: Participant;\n  }>,\n) {\n  const hasContext = !!useMaybeParticipantContext();\n  return props.participant && !hasContext ? (\n    <ParticipantContext.Provider value={props.participant}>\n      {props.children}\n    </ParticipantContext.Provider>\n  ) : (\n    <>{props.children}</>\n  );\n}\n\n/**\n * Only create a `TrackRefContext` if there is no `TrackRefContext` already.\n * @internal\n */\nexport function TrackRefContextIfNeeded(\n  props: React.PropsWithChildren<{\n    trackRef?: TrackReferenceOrPlaceholder;\n  }>,\n) {\n  const hasContext = !!useMaybeTrackRefContext();\n  return props.trackRef && !hasContext ? (\n    <TrackRefContext.Provider value={props.trackRef}>{props.children}</TrackRefContext.Provider>\n  ) : (\n    <>{props.children}</>\n  );\n}\n\n/** @public */\nexport interface ParticipantTileProps extends React.HTMLAttributes<HTMLDivElement> {\n  /** The track reference to display. */\n  trackRef?: TrackReferenceOrPlaceholder;\n  disableSpeakingIndicator?: boolean;\n\n  onParticipantClick?: (event: ParticipantClickEvent) => void;\n}\n\n/**\n * The `ParticipantTile` component is the base utility wrapper for displaying a visual representation of a participant.\n * This component can be used as a child of the `TrackLoop` component or by passing a track reference as property.\n *\n * @example Using the `ParticipantTile` component with a track reference:\n * ```tsx\n * <ParticipantTile trackRef={trackRef} />\n * ```\n * @example Using the `ParticipantTile` component as a child of the `TrackLoop` component:\n * ```tsx\n * <TrackLoop>\n *  <ParticipantTile />\n * </TrackLoop>\n * ```\n * @public\n */\nexport const ParticipantTile: (\n  props: ParticipantTileProps & React.RefAttributes<HTMLDivElement>,\n) => React.ReactNode = /* @__PURE__ */ React.forwardRef<HTMLDivElement, ParticipantTileProps>(\n  function ParticipantTile(\n    {\n      trackRef,\n      children,\n      onParticipantClick,\n      disableSpeakingIndicator,\n      ...htmlProps\n    }: ParticipantTileProps,\n    ref,\n  ) {\n    const trackReference = useEnsureTrackRef(trackRef);\n\n    const { elementProps } = useParticipantTile<HTMLDivElement>({\n      htmlProps,\n      disableSpeakingIndicator,\n      onParticipantClick,\n      trackRef: trackReference,\n    });\n    const isEncrypted = useIsEncrypted(trackReference.participant);\n    const layoutContext = useMaybeLayoutContext();\n\n    const autoManageSubscription = useFeatureContext()?.autoSubscription;\n\n    const handleSubscribe = React.useCallback(\n      (subscribed: boolean) => {\n        if (\n          trackReference.source &&\n          !subscribed &&\n          layoutContext &&\n          layoutContext.pin.dispatch &&\n          isTrackReferencePinned(trackReference, layoutContext.pin.state)\n        ) {\n          layoutContext.pin.dispatch({ msg: 'clear_pin' });\n        }\n      },\n      [trackReference, layoutContext],\n    );\n\n    return (\n      <div ref={ref} style={{ position: 'relative' }} {...elementProps}>\n        <TrackRefContextIfNeeded trackRef={trackReference}>\n          <ParticipantContextIfNeeded participant={trackReference.participant}>\n            {children ?? (\n              <>\n                {isTrackReference(trackReference) &&\n                (trackReference.publication?.kind === 'video' ||\n                  trackReference.source === Track.Source.Camera ||\n                  trackReference.source === Track.Source.ScreenShare) ? (\n                  <VideoTrack\n                    trackRef={trackReference}\n                    onSubscriptionStatusChanged={handleSubscribe}\n                    manageSubscription={autoManageSubscription}\n                  />\n                ) : (\n                  isTrackReference(trackReference) && (\n                    <AudioTrack\n                      trackRef={trackReference}\n                      onSubscriptionStatusChanged={handleSubscribe}\n                    />\n                  )\n                )}\n                <div className=\"lk-participant-placeholder\">\n                  <ParticipantPlaceholder />\n                </div>\n                <div className=\"lk-participant-metadata\">\n                  <div className=\"lk-participant-metadata-item\">\n                    {trackReference.source === Track.Source.Camera ? (\n                      <>\n                        {isEncrypted && <LockLockedIcon style={{ marginRight: '0.25rem' }} />}\n                        <TrackMutedIndicator\n                          trackRef={{\n                            participant: trackReference.participant,\n                            source: Track.Source.Microphone,\n                          }}\n                          show={'muted'}\n                        ></TrackMutedIndicator>\n                        <ParticipantName />\n                      </>\n                    ) : (\n                      <>\n                        <ScreenShareIcon style={{ marginRight: '0.25rem' }} />\n                        <ParticipantName>&apos;s screen</ParticipantName>\n                      </>\n                    )}\n                  </div>\n                  <ConnectionQualityIndicator className=\"lk-participant-metadata-item\" />\n                </div>\n              </>\n            )}\n            <FocusToggle trackRef={trackReference} />\n          </ParticipantContextIfNeeded>\n        </TrackRefContextIfNeeded>\n      </div>\n    );\n  },\n);\n", "import * as React from 'react';\nimport { mergeProps } from '../../utils';\nimport type { TrackReferenceOrPlaceholder } from '@livekit/components-core';\nimport { ParticipantTile } from '../participant/ParticipantTile';\nimport type { ParticipantClickEvent } from '@livekit/components-core';\n\n/** @public */\nexport interface FocusLayoutContainerProps extends React.HTMLAttributes<HTMLDivElement> {}\n\n/**\n * The `FocusLayoutContainer` is a layout component that expects two children:\n * A small side component: In a video conference, this is usually a carousel of participants\n * who are not in focus. And a larger main component to display the focused participant.\n * For example, with the `FocusLayout` component.\n *  @public\n */\nexport function FocusLayoutContainer(props: FocusLayoutContainerProps) {\n  const elementProps = mergeProps(props, { className: 'lk-focus-layout' });\n\n  return <div {...elementProps}>{props.children}</div>;\n}\n\n/** @public */\nexport interface FocusLayoutProps extends React.HTMLAttributes<HTMLElement> {\n  /** The track to display in the focus layout. */\n  trackRef?: TrackReferenceOrPlaceholder;\n\n  onParticipantClick?: (evt: ParticipantClickEvent) => void;\n}\n\n/**\n * The `FocusLayout` component is just a light wrapper around the `ParticipantTile` to display a single participant.\n * @public\n */\nexport function FocusLayout({ trackRef, ...htmlProps }: FocusLayoutProps) {\n  return <ParticipantTile trackRef={trackRef} {...htmlProps} />;\n}\n", "import type { TrackReference, TrackReferenceOrPlaceholder } from '@livekit/components-core';\nimport * as React from 'react';\nimport { TrackRefContext } from '../context/track-reference-context';\nimport { cloneSingleChild } from '../utils';\nimport { getTrackReferenceId } from '@livekit/components-core';\n\n/** @public */\nexport interface TrackLoopProps {\n  /** Track references to loop over. You can the use `useTracks()` hook to get TrackReferences. */\n  tracks: TrackReference[] | TrackReferenceOrPlaceholder[];\n  /** The template component to be used in the loop. */\n  children: React.ReactNode;\n}\n\n/**\n * The `TrackLoop` component loops over tracks. It is for example a easy way to loop over all participant camera and screen share tracks.\n * `TrackLoop` creates a `TrackRefContext` for each track that you can use to e.g. render the track.\n *\n * @example\n * ```tsx\n * const trackRefs = useTracks([Track.Source.Camera]);\n * <TrackLoop tracks={trackRefs} >\n *  <TrackRefContext.Consumer>\n *    {(trackRef) => trackRef && <VideoTrack trackRef={trackRef}/>}\n *  </TrackRefContext.Consumer>\n * </TrackLoop>\n * ```\n * @public\n */\nexport function TrackLoop({ tracks, ...props }: TrackLoopProps) {\n  return (\n    <>\n      {tracks.map((trackReference) => {\n        return (\n          <TrackRefContext.Provider\n            value={trackReference}\n            key={getTrackReferenceId(trackReference)}\n          >\n            {cloneSingleChild(props.children)}\n          </TrackRefContext.Provider>\n        );\n      })}\n    </>\n  );\n}\n", "import * as React from 'react';\nimport SvgChevron from '../../assets/icons/Chevron';\nimport type { usePagination } from '../../hooks';\nimport { createInteractingObservable } from '@livekit/components-core';\n\nexport interface PaginationControlProps\n  extends Pick<\n    ReturnType<typeof usePagination>,\n    'totalPageCount' | 'nextPage' | 'prevPage' | 'currentPage'\n  > {\n  /** Reference to an HTML element that holds the pages, while interacting (`mouseover`)\n   *  with it, the pagination controls will appear for a while. */\n  pagesContainer?: React.RefObject<HTMLElement>;\n}\n\nexport function PaginationControl({\n  totalPageCount,\n  nextPage,\n  prevPage,\n  currentPage,\n  pagesContainer: connectedElement,\n}: PaginationControlProps) {\n  const [interactive, setInteractive] = React.useState(false);\n  React.useEffect(() => {\n    let subscription:\n      | ReturnType<ReturnType<typeof createInteractingObservable>['subscribe']>\n      | undefined;\n    if (connectedElement) {\n      subscription = createInteractingObservable(connectedElement.current, 2000).subscribe(\n        setInteractive,\n      );\n    }\n    return () => {\n      if (subscription) {\n        subscription.unsubscribe();\n      }\n    };\n  }, [connectedElement]);\n\n  return (\n    <div className=\"lk-pagination-control\" data-lk-user-interaction={interactive}>\n      <button className=\"lk-button\" onClick={prevPage}>\n        <SvgChevron />\n      </button>\n      <span className=\"lk-pagination-count\">{`${currentPage} of ${totalPageCount}`}</span>\n      <button className=\"lk-button\" onClick={nextPage}>\n        <SvgChevron />\n      </button>\n    </div>\n  );\n}\n", "import * as React from 'react';\n\nexport interface PaginationIndicatorProps {\n  totalPageCount: number;\n  currentPage: number;\n}\n\nexport const PaginationIndicator: (\n  props: PaginationIndicatorProps & React.RefAttributes<HTMLDivElement>,\n) => React.ReactNode = /* @__PURE__ */ React.forwardRef<HTMLDivElement, PaginationIndicatorProps>(\n  function PaginationIndicator({ totalPageCount, currentPage }: PaginationIndicatorProps, ref) {\n    const bubbles = new Array(totalPageCount).fill('').map((_, index) => {\n      if (index + 1 === currentPage) {\n        return <span data-lk-active key={index} />;\n      } else {\n        return <span key={index} />;\n      }\n    });\n\n    return (\n      <div ref={ref} className=\"lk-pagination-indicator\">\n        {bubbles}\n      </div>\n    );\n  },\n);\n", "import * as React from 'react';\nimport type { UseParticipantsOptions } from '../../hooks';\nimport { useGridLayout, usePagination, useSwipe } from '../../hooks';\nimport { mergeProps } from '../../utils';\nimport type { TrackReferenceOrPlaceholder } from '@livekit/components-core';\nimport { TrackLoop } from '../TrackLoop';\nimport { PaginationControl } from '../controls/PaginationControl';\nimport { PaginationIndicator } from '../controls/PaginationIndicator';\n\n/** @public */\nexport interface GridLayoutProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    Pick<UseParticipantsOptions, 'updateOnlyOn'> {\n  children: React.ReactNode;\n  tracks: TrackReferenceOrPlaceholder[];\n}\n\n/**\n * The `GridLayout` component displays the nested participants in a grid where every participants has the same size.\n * It also supports pagination if there are more participants than the grid can display.\n * @remarks\n * To ensure visual stability when tiles are reordered due to track updates,\n * the component uses the `useVisualStableUpdate` hook.\n * @example\n * ```tsx\n * <LiveKitRoom>\n *   <GridLayout tracks={tracks}>\n *     <ParticipantTile />\n *   </GridLayout>\n * <LiveKitRoom>\n * ```\n * @public\n */\nexport function GridLayout({ tracks, ...props }: GridLayoutProps) {\n  const gridEl = React.createRef<HTMLDivElement>();\n\n  const elementProps = React.useMemo(\n    () => mergeProps(props, { className: 'lk-grid-layout' }),\n    [props],\n  );\n  const { layout } = useGridLayout(gridEl, tracks.length);\n  const pagination = usePagination(layout.maxTiles, tracks);\n\n  useSwipe(gridEl, {\n    onLeftSwipe: pagination.nextPage,\n    onRightSwipe: pagination.prevPage,\n  });\n\n  return (\n    <div ref={gridEl} data-lk-pagination={pagination.totalPageCount > 1} {...elementProps}>\n      <TrackLoop tracks={pagination.tracks}>{props.children}</TrackLoop>\n      {tracks.length > layout.maxTiles && (\n        <>\n          <PaginationIndicator\n            totalPageCount={pagination.totalPageCount}\n            currentPage={pagination.currentPage}\n          />\n          <PaginationControl pagesContainer={gridEl} {...pagination} />\n        </>\n      )}\n    </div>\n  );\n}\n", "import type { TrackReferenceOrPlaceholder } from '@livekit/components-core';\nimport { getScrollBarWidth } from '@livekit/components-core';\nimport * as React from 'react';\nimport { useSize } from '../../hooks/internal';\nimport { useVisualStableUpdate } from '../../hooks';\nimport { TrackLoop } from '../TrackLoop';\n\nconst MIN_HEIGHT = 130;\nconst MIN_WIDTH = 140;\nconst MIN_VISIBLE_TILES = 1;\nconst ASPECT_RATIO = 16 / 10;\nconst ASPECT_RATIO_INVERT = (1 - ASPECT_RATIO) * -1;\n\n/** @public */\nexport interface CarouselLayoutProps extends React.HTMLAttributes<HTMLMediaElement> {\n  tracks: TrackReferenceOrPlaceholder[];\n  children: React.ReactNode;\n  /** Place the tiles vertically or horizontally next to each other.\n   * If undefined orientation is guessed by the dimensions of the container. */\n  orientation?: 'vertical' | 'horizontal';\n}\n\n/**\n * The `CarouselLayout` component displays a list of tracks in a scroll container.\n * It will display as many tiles as possible and overflow the rest.\n * @remarks\n * To ensure visual stability when tiles are reordered due to track updates,\n * the component uses the `useVisualStableUpdate` hook.\n * @example\n * ```tsx\n * const tracks = useTracks([Track.Source.Camera]);\n * <CarouselLayout tracks={tracks}>\n *   <ParticipantTile />\n * </CarouselLayout>\n * ```\n * @public\n */\nexport function CarouselLayout({ tracks, orientation, ...props }: CarouselLayoutProps) {\n  const asideEl = React.useRef<HTMLDivElement>(null);\n  const [prevTiles, setPrevTiles] = React.useState(0);\n  const { width, height } = useSize(asideEl);\n  const carouselOrientation = orientation\n    ? orientation\n    : height >= width\n      ? 'vertical'\n      : 'horizontal';\n\n  const tileSpan =\n    carouselOrientation === 'vertical'\n      ? Math.max(width * ASPECT_RATIO_INVERT, MIN_HEIGHT)\n      : Math.max(height * ASPECT_RATIO, MIN_WIDTH);\n  const scrollBarWidth = getScrollBarWidth();\n\n  const tilesThatFit =\n    carouselOrientation === 'vertical'\n      ? Math.max((height - scrollBarWidth) / tileSpan, MIN_VISIBLE_TILES)\n      : Math.max((width - scrollBarWidth) / tileSpan, MIN_VISIBLE_TILES);\n\n  let maxVisibleTiles = Math.round(tilesThatFit);\n  if (Math.abs(tilesThatFit - prevTiles) < 0.5) {\n    maxVisibleTiles = Math.round(prevTiles);\n  } else if (prevTiles !== tilesThatFit) {\n    setPrevTiles(tilesThatFit);\n  }\n\n  const sortedTiles = useVisualStableUpdate(tracks, maxVisibleTiles);\n\n  React.useLayoutEffect(() => {\n    if (asideEl.current) {\n      asideEl.current.dataset.lkOrientation = carouselOrientation;\n      asideEl.current.style.setProperty('--lk-max-visible-tiles', maxVisibleTiles.toString());\n    }\n  }, [maxVisibleTiles, carouselOrientation]);\n\n  return (\n    <aside key={carouselOrientation} className=\"lk-carousel\" ref={asideEl} {...props}>\n      <TrackLoop tracks={sortedTiles}>{props.children}</TrackLoop>\n    </aside>\n  );\n}\n", "import type { PinState, WidgetState } from '@livekit/components-core';\nimport { log } from '@livekit/components-core';\nimport * as React from 'react';\nimport type { LayoutContextType } from '../../context';\nimport { LayoutContext, useEnsureCreateLayoutContext } from '../../context';\n\n/** @alpha */\nexport interface LayoutContextProviderProps {\n  value?: LayoutContextType;\n  onPinChange?: (state: PinState) => void;\n  onWidgetChange?: (state: WidgetState) => void;\n}\n\n/** @alpha */\nexport function LayoutContextProvider({\n  value,\n  onPinChange,\n  onWidgetChange,\n  children,\n}: React.PropsWithChildren<LayoutContextProviderProps>) {\n  const layoutContextValue = useEnsureCreateLayoutContext(value);\n\n  React.useEffect(() => {\n    log.debug('PinState Updated', { state: layoutContextValue.pin.state });\n    if (onPinChange && layoutContextValue.pin.state) onPinChange(layoutContextValue.pin.state);\n  }, [layoutContextValue.pin.state, onPinChange]);\n\n  React.useEffect(() => {\n    log.debug('Widget Updated', { widgetState: layoutContextValue.widget.state });\n    if (onWidgetChange && layoutContextValue.widget.state) {\n      onWidgetChange(layoutContextValue.widget.state);\n    }\n  }, [onWidgetChange, layoutContextValue.widget.state]);\n\n  return <LayoutContext.Provider value={layoutContextValue}>{children}</LayoutContext.Provider>;\n}\n", "import * as React from 'react';\nimport { type TrackReference } from '@livekit/components-core';\nimport { useEnsureTrackRef } from '../../context';\nimport { useMultibandTrackVolume } from '../../hooks';\n\n/**\n * @public\n * @deprecated Use BarVisualizer instead\n */\nexport interface AudioVisualizerProps extends React.HTMLAttributes<SVGElement> {\n  trackRef?: TrackReference;\n}\n\n/**\n * The AudioVisualizer component is used to visualize the audio volume of a given audio track.\n * @remarks\n * Requires a `TrackReferenceOrPlaceholder` to be provided either as a property or via the `TrackRefContext`.\n * @example\n * ```tsx\n * <AudioVisualizer />\n * ```\n * @public\n * @deprecated Use BarVisualizer instead\n */\nexport const AudioVisualizer: (\n  props: AudioVisualizerProps & React.RefAttributes<SVGSVGElement>,\n) => React.ReactNode = /* @__PURE__ */ React.forwardRef<SVGSVGElement, AudioVisualizerProps>(\n  function AudioVisualizer({ trackRef, ...props }: AudioVisualizerProps, ref) {\n    const svgWidth = 200;\n    const svgHeight = 90;\n    const barWidth = 6;\n    const barSpacing = 4;\n    const volMultiplier = 50;\n    const barCount = 7;\n    const trackReference = useEnsureTrackRef(trackRef);\n\n    const volumes = useMultibandTrackVolume(trackReference, { bands: 7, loPass: 300 });\n\n    return (\n      <svg\n        ref={ref}\n        width=\"100%\"\n        height=\"100%\"\n        viewBox={`0 0 ${svgWidth} ${svgHeight}`}\n        {...props}\n        className=\"lk-audio-visualizer\"\n      >\n        <rect x=\"0\" y=\"0\" width=\"100%\" height=\"100%\" />\n        <g\n          style={{\n            transform: `translate(${(svgWidth - barCount * (barWidth + barSpacing)) / 2}px, 0)`,\n          }}\n        >\n          {volumes.map((vol, idx) => (\n            <rect\n              key={idx}\n              x={idx * (barWidth + barSpacing)}\n              y={svgHeight / 2 - (vol * volMultiplier) / 2}\n              width={barWidth}\n              height={vol * volMultiplier}\n            ></rect>\n          ))}\n        </g>\n      </svg>\n    );\n  },\n);\n", "import type { Participant } from 'livekit-client';\nimport * as React from 'react';\nimport { ParticipantContext } from '../context';\nimport { cloneSingleChild } from '../utils';\n\n/** @public */\nexport interface ParticipantLoopProps {\n  /** The participants to loop over. Use `useParticipants()` hook to get participants. */\n  participants: Participant[];\n  /** The template component to be used in the loop. */\n  children: React.ReactNode;\n}\n\n/**\n * The `ParticipantLoop` component loops over an array of participants to create a context for every participant.\n * This component takes exactly one child component as a template.\n * By providing your own template as a child you have full control over the look and feel of your participant representations.\n *\n * @remarks\n * If you want to loop over individual tracks instead of participants, you can use the `TrackLoop` component.\n *\n * @example\n * ```tsx\n * const participants = useParticipants();\n * <ParticipantLoop participants={participants}>\n *   <ParticipantName />\n * </ParticipantLoop>\n * ```\n * @public\n */\nexport function ParticipantLoop({ participants, ...props }: ParticipantLoopProps) {\n  return (\n    <>\n      {participants.map((participant) => (\n        <ParticipantContext.Provider value={participant} key={participant.identity}>\n          {cloneSingleChild(props.children)}\n        </ParticipantContext.Provider>\n      ))}\n    </>\n  );\n}\n", "import { getTrackReferenceId } from '@livekit/components-core';\nimport { Track } from 'livekit-client';\nimport * as React from 'react';\nimport { useTracks } from '../hooks';\nimport { AudioTrack } from './participant/AudioTrack';\n\n/** @public */\nexport interface RoomAudioRendererProps {\n  /** Sets the volume for all audio tracks rendered by this component. By default, the range is between `0.0` and `1.0`. */\n  volume?: number;\n  /**\n   * If set to `true`, mutes all audio tracks rendered by the component.\n   * @remarks\n   * If set to `true`, the server will stop sending audio track data to the client.\n   * @alpha\n   */\n  muted?: boolean;\n}\n\n/**\n * The `RoomAudioRenderer` component is a drop-in solution for adding audio to your LiveKit app.\n * It takes care of handling remote participants’ audio tracks and makes sure that microphones and screen share are audible.\n *\n * @example\n * ```tsx\n * <LiveKitRoom>\n *   <RoomAudioRenderer />\n * </LiveKitRoom>\n * ```\n * @public\n */\nexport function RoomAudioRenderer({ volume, muted }: RoomAudioRendererProps) {\n  const tracks = useTracks(\n    [Track.Source.Microphone, Track.Source.ScreenShareAudio, Track.Source.Unknown],\n    {\n      updateOnlyOn: [],\n      onlySubscribed: true,\n    },\n  ).filter((ref) => !ref.participant.isLocal && ref.publication.kind === Track.Kind.Audio);\n\n  return (\n    <div style={{ display: 'none' }}>\n      {tracks.map((trackRef) => (\n        <AudioTrack\n          key={getTrackReferenceId(trackRef)}\n          trackRef={trackRef}\n          volume={volume}\n          muted={muted}\n        />\n      ))}\n    </div>\n  );\n}\n", "import * as React from 'react';\nimport { useRoomInfo } from '../hooks';\n\n/** @public */\nexport interface RoomNameProps extends React.HTMLAttributes<HTMLSpanElement> {\n  childrenPosition?: 'before' | 'after';\n}\n\n/**\n * The `RoomName` component renders the name of the connected LiveKit room inside a span tag.\n *\n * @example\n * ```tsx\n * <LiveKitRoom>\n *   <RoomName />\n * </LiveKitRoom>\n * ```\n * @public\n *\n * @param props - RoomNameProps\n */\nexport const RoomName: React.FC<RoomNameProps & React.RefAttributes<HTMLSpanElement>> =\n  /* @__PURE__ */ React.forwardRef<HTMLSpanElement, RoomNameProps>(function RoomName(\n    { childrenPosition = 'before', children, ...htmlAttributes }: RoomNameProps,\n    ref,\n  ) {\n    const { name } = useRoomInfo();\n\n    return (\n      <span ref={ref} {...htmlAttributes}>\n        {childrenPosition === 'before' && children}\n        {name}\n        {childrenPosition === 'after' && children}\n      </span>\n    );\n  });\n", "import * as React from 'react';\nimport { mergeProps } from '../utils';\n\n/**\n * The `Toast` component is a rudimentary way to display a message to the user.\n * This message should be short lived and not require user interaction.\n * For example, displaying the current connection state like `ConnectionStateToast` does.\n *\n * @example\n * ```tsx\n * <Toast>Connecting...</Toast>\n * ```\n * @public\n */\nexport function Toast(props: React.HTMLAttributes<HTMLDivElement>) {\n  const htmlProps = React.useMemo(() => mergeProps(props, { className: 'lk-toast' }), [props]);\n  return <div {...htmlProps}>{props.children}</div>;\n}\n", "export const generateConnectingSequenceBar = (columns: number): number[][] => {\n  const seq = [];\n\n  for (let x = 0; x < columns; x++) {\n    seq.push([x, columns - 1 - x]);\n  }\n\n  return seq;\n};\n", "export const generateListeningSequenceBar = (columns: number): number[][] => {\n  const center = Math.floor(columns / 2);\n  const noIndex = -1;\n\n  return [[center], [noIndex]];\n};\n", "import { useEffect, useRef, useState } from 'react';\nimport { generateConnectingSequenceBar } from '../animationSequences/connectingSequence';\nimport { generateListeningSequenceBar } from '../animationSequences/listeningSequence';\nimport type { AgentState } from '../../../hooks';\n\nexport const useBarAnimator = (\n  state: AgentState | undefined,\n  columns: number,\n  interval: number,\n): number[] => {\n  const [index, setIndex] = useState(0);\n  const [sequence, setSequence] = useState<number[][]>([[]]);\n\n  useEffect(() => {\n    if (state === 'thinking') {\n      setSequence(generateListeningSequenceBar(columns));\n    } else if (state === 'connecting' || state === 'initializing') {\n      const sequence = [...generateConnectingSequenceBar(columns)];\n      setSequence(sequence);\n    } else if (state === 'listening') {\n      setSequence(generateListeningSequenceBar(columns));\n    } else if (state === undefined) {\n      setSequence([new Array(columns).fill(0).map((_, idx) => idx)]);\n    } else {\n      setSequence([[]]);\n    }\n    setIndex(0);\n  }, [state, columns]);\n\n  const animationFrameId = useRef<number | null>(null);\n  useEffect(() => {\n    let startTime = performance.now();\n\n    const animate = (time: DOMHighResTimeStamp) => {\n      const timeElapsed = time - startTime;\n\n      if (timeElapsed >= interval) {\n        setIndex((prev) => prev + 1);\n        startTime = time;\n      }\n\n      animationFrameId.current = requestAnimationFrame(animate);\n    };\n\n    animationFrameId.current = requestAnimationFrame(animate);\n\n    return () => {\n      if (animationFrameId.current !== null) {\n        cancelAnimationFrame(animationFrameId.current);\n      }\n    };\n  }, [interval, columns, state, sequence.length]);\n\n  return sequence[index % sequence.length];\n};\n", "import * as React from 'react';\nimport { useBarAnimator } from './animators/useBarAnimator';\nimport { useMultibandTrackVolume, type AgentState } from '../../hooks';\nimport type { TrackReferenceOrPlaceholder } from '@livekit/components-core';\nimport { useMaybeTrackRefContext } from '../../context';\nimport { cloneSingleChild, mergeProps } from '../../utils';\n\n/**\n * @beta\n */\nexport type BarVisualizerOptions = {\n  /** in percentage */\n  maxHeight?: number;\n  /** in percentage */\n  minHeight?: number;\n};\n\n/**\n * @beta\n */\nexport interface BarVisualizerProps extends React.HTMLProps<HTMLDivElement> {\n  /** If set, the visualizer will transition between different voice assistant states */\n  state?: AgentState;\n  /** Number of bars that show up in the visualizer */\n  barCount?: number;\n  trackRef?: TrackReferenceOrPlaceholder;\n  options?: BarVisualizerOptions;\n  /** The template component to be used in the visualizer. */\n  children?: React.ReactNode;\n}\n\nconst sequencerIntervals = new Map<AgentState, number>([\n  ['connecting', 2000],\n  ['initializing', 2000],\n  ['listening', 500],\n  ['thinking', 150],\n]);\n\nconst getSequencerInterval = (\n  state: AgentState | undefined,\n  barCount: number,\n): number | undefined => {\n  if (state === undefined) {\n    return 1000;\n  }\n  let interval = sequencerIntervals.get(state);\n  if (interval) {\n    switch (state) {\n      case 'connecting':\n        // case 'thinking':\n        interval /= barCount;\n        break;\n\n      default:\n        break;\n    }\n  }\n  return interval;\n};\n/**\n * Visualizes audio signals from a TrackReference as bars.\n * If the `state` prop is set, it automatically transitions between VoiceAssistant states.\n * @beta\n *\n * @remarks For VoiceAssistant state transitions this component requires a voice assistant agent running with livekit-agents \\>= 0.9.0\n *\n * @example\n * ```tsx\n * function SimpleVoiceAssistant() {\n *   const { state, audioTrack } = useVoiceAssistant();\n *   return (\n *    <BarVisualizer\n *      state={state}\n *      trackRef={audioTrack}\n *    />\n *   );\n * }\n * ```\n */\nexport const BarVisualizer = /* @__PURE__ */ React.forwardRef<HTMLDivElement, BarVisualizerProps>(\n  function BarVisualizer(\n    { state, options, barCount = 15, trackRef, children, ...props }: BarVisualizerProps,\n    ref,\n  ) {\n    const elementProps = mergeProps(props, { className: 'lk-audio-bar-visualizer' });\n    let trackReference = useMaybeTrackRefContext();\n\n    if (trackRef) {\n      trackReference = trackRef;\n    }\n\n    const volumeBands = useMultibandTrackVolume(trackReference, {\n      bands: barCount,\n      loPass: 100,\n      hiPass: 200,\n    });\n    const minHeight = options?.minHeight ?? 20;\n    const maxHeight = options?.maxHeight ?? 100;\n\n    const highlightedIndices = useBarAnimator(\n      state,\n      barCount,\n      getSequencerInterval(state, barCount) ?? 100,\n    );\n\n    return (\n      <div ref={ref} {...elementProps} data-lk-va-state={state}>\n        {volumeBands.map((volume, idx) =>\n          children ? (\n            cloneSingleChild(children, {\n              'data-lk-highlighted': highlightedIndices.includes(idx),\n              'data-lk-bar-index': idx,\n              className: 'lk-audio-bar',\n              style: { height: `${Math.min(maxHeight, Math.max(minHeight, volume * 100 + 5))}%` },\n            })\n          ) : (\n            <span\n              key={idx}\n              data-lk-highlighted={highlightedIndices.includes(idx)}\n              data-lk-bar-index={idx}\n              className={`lk-audio-bar ${highlightedIndices.includes(idx) && 'lk-highlighted'}`}\n              style={{\n                // TODO transform animations would be more performant, however the border-radius gets distorted when using scale transforms. a 9-slice approach (or 3 in this case) could work\n                // transform: `scale(1, ${Math.min(maxHeight, Math.max(minHeight, volume))}`,\n                height: `${Math.min(maxHeight, Math.max(minHeight, volume * 100 + 5))}%`,\n              }}\n            ></span>\n          ),\n        )}\n      </div>\n    );\n  },\n);\n", "import * as React from 'react';\n\nimport { ConnectionQualityIndicator } from './ConnectionQualityIndicator';\nimport { ParticipantName } from './ParticipantName';\nimport { TrackMutedIndicator } from './TrackMutedIndicator';\nimport { TrackRefContext, useEnsureTrackRef } from '../../context';\n\nimport type { ParticipantTileProps } from './ParticipantTile';\nimport { AudioTrack } from './AudioTrack';\nimport { useParticipantTile } from '../../hooks';\nimport { isTrackReference } from '@livekit/components-core';\nimport { BarVisualizer } from './BarVisualizer';\n\n/**\n * The `ParticipantAudioTile` component is the base utility wrapper for displaying a visual representation of a participant.\n * This component can be used as a child of the `TileLoop` or independently if a participant is passed as a property.\n *\n * @example\n * ```tsx\n * <ParticipantAudioTile />\n * ```\n * @public\n */\nexport const ParticipantAudioTile: (\n  props: ParticipantTileProps & React.RefAttributes<HTMLDivElement>,\n) => React.ReactNode = /* @__PURE__ */ React.forwardRef<HTMLDivElement, ParticipantTileProps>(\n  function ParticipantAudioTile(\n    {\n      children,\n      disableSpeakingIndicator,\n      onParticipantClick,\n      trackRef,\n      ...htmlProps\n    }: ParticipantTileProps,\n    ref,\n  ) {\n    const trackReference = useEnsureTrackRef(trackRef);\n    const { elementProps } = useParticipantTile({\n      trackRef: trackReference,\n      htmlProps,\n      disableSpeakingIndicator,\n      onParticipantClick,\n    });\n\n    return (\n      <div ref={ref} style={{ position: 'relative', minHeight: '160px' }} {...elementProps}>\n        <TrackRefContext.Provider value={trackReference}>\n          {children ?? (\n            <>\n              {isTrackReference(trackReference) && (\n                <AudioTrack trackRef={trackReference}></AudioTrack>\n              )}\n              <BarVisualizer barCount={7} options={{ minHeight: 8 }} />\n              <div className=\"lk-participant-metadata\">\n                <div className=\"lk-participant-metadata-item\">\n                  <TrackMutedIndicator trackRef={trackReference}></TrackMutedIndicator>\n                  <ParticipantName />\n                </div>\n                <ConnectionQualityIndicator className=\"lk-participant-metadata-item\" />\n              </div>\n            </>\n          )}\n        </TrackRefContext.Provider>\n      </div>\n    );\n  },\n);\n", "import type { Room } from 'livekit-client';\nimport { ConnectionState } from 'livekit-client';\nimport * as React from 'react';\nimport { SpinnerIcon } from '../assets/icons';\nimport { useConnectionState } from '../hooks';\nimport { Toast } from './Toast';\n\n/** @public */\nexport interface ConnectionStateToastProps extends React.HTMLAttributes<HTMLDivElement> {\n  room?: Room;\n}\n\n/**\n * The `ConnectionStateToast` component displays a toast\n * notification indicating the current connection state of the room.\n * @public\n */\nexport function ConnectionStateToast(props: ConnectionStateToastProps) {\n  const [notification, setNotification] = React.useState<React.ReactElement | undefined>(undefined);\n  const state = useConnectionState(props.room);\n\n  React.useEffect(() => {\n    switch (state) {\n      case ConnectionState.Reconnecting:\n        setNotification(\n          <>\n            <SpinnerIcon className=\"lk-spinner\" /> Reconnecting\n          </>,\n        );\n        break;\n      case ConnectionState.Connecting:\n        setNotification(\n          <>\n            <SpinnerIcon className=\"lk-spinner\" /> Connecting\n          </>,\n        );\n        break;\n      case ConnectionState.Disconnected:\n        setNotification(<>Disconnected</>);\n        break;\n      default:\n        setNotification(undefined);\n        break;\n    }\n  }, [state]);\n  return notification ? <Toast className=\"lk-toast-connection-state\">{notification}</Toast> : <></>;\n}\n", "import type { ReceivedChatMessage } from '@livekit/components-core';\nimport { tokenize, createDefaultGrammar } from '@livekit/components-core';\nimport * as React from 'react';\n\n/** @public */\nexport type MessageFormatter = (message: string) => React.ReactNode;\n\n/**\n * Chat<PERSON>ntry composes the HTML div element under the hood, so you can pass all its props.\n * These are the props specific to the ChatEntry component:\n * @public\n */\nexport interface ChatEntryProps extends React.HTMLAttributes<HTMLLIElement> {\n  /** The chat massage object to display. */\n  entry: ReceivedChatMessage;\n  /** Hide sender name. Useful when displaying multiple consecutive chat messages from the same person. */\n  hideName?: boolean;\n  /** Hide message timestamp. */\n  hideTimestamp?: boolean;\n  /** An optional formatter for the message body. */\n  messageFormatter?: MessageFormatter;\n}\n\n/**\n * The `ChatEntry` component holds and displays one chat message.\n *\n * @example\n * ```tsx\n * <Chat>\n *   <ChatEntry />\n * </Chat>\n * ```\n * @see `Chat`\n * @public\n */\nexport const ChatEntry: (\n  props: ChatEntryProps & React.RefAttributes<HTMLLIElement>,\n) => React.ReactNode = /* @__PURE__ */ React.forwardRef<HTMLLIElement, ChatEntryProps>(\n  function ChatEntry(\n    { entry, hideName = false, hideTimestamp = false, messageFormatter, ...props }: ChatEntryProps,\n    ref,\n  ) {\n    const formattedMessage = React.useMemo(() => {\n      return messageFormatter ? messageFormatter(entry.message) : entry.message;\n    }, [entry.message, messageFormatter]);\n    const hasBeenEdited = !!entry.editTimestamp;\n    const time = new Date(entry.timestamp);\n    const locale = navigator ? navigator.language : 'en-US';\n\n    return (\n      <li\n        ref={ref}\n        className=\"lk-chat-entry\"\n        title={time.toLocaleTimeString(locale, { timeStyle: 'full' })}\n        data-lk-message-origin={entry.from?.isLocal ? 'local' : 'remote'}\n        {...props}\n      >\n        {(!hideTimestamp || !hideName || hasBeenEdited) && (\n          <span className=\"lk-meta-data\">\n            {!hideName && (\n              <strong className=\"lk-participant-name\">\n                {entry.from?.name ?? entry.from?.identity}\n              </strong>\n            )}\n\n            {(!hideTimestamp || hasBeenEdited) && (\n              <span className=\"lk-timestamp\">\n                {hasBeenEdited && 'edited '}\n                {time.toLocaleTimeString(locale, { timeStyle: 'short' })}\n              </span>\n            )}\n          </span>\n        )}\n\n        <span className=\"lk-message-body\">{formattedMessage}</span>\n      </li>\n    );\n  },\n);\n\n/** @public */\nexport function formatChatMessageLinks(message: string): React.ReactNode {\n  return tokenize(message, createDefaultGrammar()).map((tok, i) => {\n    if (typeof tok === `string`) {\n      return tok;\n    } else {\n      const content = tok.content.toString();\n      const href =\n        tok.type === `url`\n          ? /^http(s?):\\/\\//.test(content)\n            ? content\n            : `https://${content}`\n          : `mailto:${content}`;\n      return (\n        <a className=\"lk-chat-link\" key={i} href={href} target=\"_blank\" rel=\"noreferrer\">\n          {content}\n        </a>\n      );\n    }\n  });\n}\n"], "names": ["ClearPinButton", "React", "props", "ref", "buttonProps", "useClearPinButton", "ConnectionState", "room", "connectionState", "useConnectionState", "<PERSON><PERSON><PERSON>og<PERSON>", "mergedProps", "useChatToggle", "DisconnectButton", "useDisconnectButton", "SvgCameraDisabledIcon", "SvgCameraIcon", "SvgChatCloseIcon", "SvgChatIcon", "SvgChevron", "SvgFocusToggleIcon", "SvgGearIcon", "SvgLeaveIcon", "SvgLockLockedIcon", "SvgMicDisabledIcon", "SvgMicIcon", "SvgQualityExcellentIcon", "SvgQualityGoodIcon", "SvgQualityPoorIcon", "SvgQualityUnknownIcon", "SvgScreenShareIcon", "SvgScreenShareStopIcon", "SvgSpinnerIcon", "SvgUnfocusToggleIcon", "FocusToggle", "trackRef", "trackRefFromContext", "useMaybeTrackRefContext", "inFocus", "useFocusToggle", "LayoutContext", "layoutContext", "UnfocusToggleIcon", "FocusToggleIcon", "MediaDeviceSelect", "kind", "initialSelection", "onActiveDeviceChange", "onDeviceListChange", "onDeviceSelectError", "exactMatch", "track", "requestPermissions", "onError", "useMaybeRoomContext", "handleError", "e", "RoomEvent", "devices", "activeDeviceId", "setActiveMediaDevice", "className", "useMediaDeviceSelect", "handleActiveDeviceChange", "deviceId", "mergeProps", "<PERSON><PERSON><PERSON><PERSON>", "info", "isActive", "index", "device", "StartAudio", "label", "useRoomContext", "useStartAudio", "StartMediaButton", "audioProps", "canPlayAudio", "canPlayVideo", "useStartVideo", "style", "restProps", "getSourceIcon", "source", "enabled", "Track", "MicIcon", "MicDisabledIcon", "CameraIcon", "CameraDisabledIcon", "ScreenShareStopIcon", "ScreenShareIcon", "getConnectionQualityIcon", "quality", "ConnectionQuality", "QualityExcellentIcon", "QualityGoodIcon", "QualityPoorIcon", "QualityUnknownIcon", "TrackToggle", "showIcon", "useTrackToggle", "isClient", "setIsClient", "ConnectionQualityIndicator", "useConnectionQualityIndicator", "elementProps", "ParticipantName", "participant", "p", "useEnsureParticipant", "infoObserver", "setupParticipantName", "identity", "name", "useObservableState", "TrackMutedIndicator", "show", "isMuted", "useTrackMutedIndicator", "showIndicator", "htmlProps", "SvgParticipantPlaceholder", "useMediaTrackBySourceOrName", "observerOptions", "options", "publication", "setPublication", "getTrackByIdentifier", "setMuted", "isSubscribed", "setSubscribed", "setTrack", "orientation", "setOrientation", "previousElement", "trackObserver", "setupMediaTrack", "isTrackReference", "subscription", "log", "_a", "_b", "orientation_", "FUNC_ERROR_TEXT", "NAN", "symbolTag", "reTrim", "reIsBadHex", "reIsBinary", "reIsOctal", "freeParseInt", "freeGlobal", "global", "freeSelf", "root", "objectProto", "objectToString", "nativeMax", "nativeMin", "now", "debounce", "func", "wait", "lastArgs", "lastThis", "max<PERSON><PERSON>", "result", "timerId", "lastCallTime", "lastInvokeTime", "leading", "maxing", "trailing", "toNumber", "isObject", "invokeFunc", "time", "args", "thisArg", "leading<PERSON>dge", "timerExpired", "remainingWait", "timeSinceLastCall", "timeSinceLastInvoke", "shouldInvoke", "trailingEdge", "cancel", "flush", "debounced", "isInvoking", "value", "type", "isObjectLike", "isSymbol", "other", "isBinary", "lodash_debounce", "useUnmount", "funcRef", "useRef", "useEffect", "useDebounceCallback", "delay", "debounced<PERSON>un<PERSON>", "useMemo", "debouncedFuncInstance", "wrappedFunc", "useDebounceValue", "initialValue", "eq", "left", "right", "unwrappedInitialValue", "debounced<PERSON><PERSON><PERSON>", "setDebouncedValue", "useState", "previousValueRef", "updateDebouncedValue", "useIntersectionObserver", "threshold", "rootMargin", "freezeOnceVisible", "initialIsIntersecting", "onChange", "setRef", "state", "setState", "callback<PERSON><PERSON>", "frozen", "unobserve", "observer", "entries", "thresholds", "entry", "isIntersecting", "threshold2", "prevRef", "_a2", "VideoTrack", "onTrackClick", "onClick", "onSubscriptionStatusChanged", "manageSubscription", "trackReference", "useEnsureTrackRef", "mediaEl", "intersectionEntry", "useHooks.useIntersectionObserver", "debouncedIntersectionEntry", "useHooks.useDebounceValue", "RemoteTrackPublication", "pub", "clickHandler", "evt", "AudioTrack", "volume", "RemoteAudioTrack", "ParticipantContextIfNeeded", "hasContext", "useMaybeParticipantContext", "ParticipantContext", "TrackRefContextIfNeeded", "TrackRefContext", "ParticipantTile", "children", "onParticipantClick", "disableSpeakingIndicator", "useParticipantTile", "isEncrypted", "useIsEncrypted", "useMaybeLayoutContext", "autoManageSubscription", "useFeatureContext", "handleSubscribe", "subscribed", "isTrackReferencePinned", "ParticipantPlaceholder", "LockLockedIcon", "FocusLayoutContainer", "FocusLayout", "TrackLoop", "tracks", "getTrackReferenceId", "cloneSingleChild", "PaginationControl", "totalPageCount", "nextPage", "prevPage", "currentPage", "connectedElement", "interactive", "setInteractive", "createInteractingObservable", "PaginationIndicator", "bubbles", "_", "GridLayout", "gridEl", "layout", "useGridLayout", "pagination", "usePagination", "useSwipe", "MIN_HEIGHT", "MIN_WIDTH", "MIN_VISIBLE_TILES", "ASPECT_RATIO", "ASPECT_RATIO_INVERT", "CarouselLayout", "asideEl", "prevTiles", "setPrevTiles", "width", "height", "useSize", "carouselOrientation", "tileSpan", "scrollBarWidth", "getScrollBarWidth", "tilesThatFit", "maxVisibleTiles", "sortedTiles", "useVisualStableUpdate", "LayoutContextProvider", "onPinChange", "onWidgetChange", "layoutContextValue", "useEnsureCreateLayoutContext", "AudioVisualizer", "volumes", "useMultibandTrackVolume", "vol", "idx", "ParticipantLoop", "participants", "RoomAudio<PERSON><PERSON><PERSON>", "muted", "useTracks", "RoomName", "childrenPosition", "htmlAttributes", "useRoomInfo", "Toast", "generateConnectingSequenceBar", "columns", "seq", "x", "generateListeningSequenceBar", "useBarAnimator", "interval", "setIndex", "sequence", "setSequence", "animationFrameId", "startTime", "animate", "prev", "sequencerIntervals", "getSequencerInterval", "barCount", "BarVisualizer", "volumeBands", "minHeight", "maxHeight", "highlightedIndices", "ParticipantAudioTile", "ConnectionStateToast", "notification", "setNotification", "SpinnerIcon", "ChatEntry", "<PERSON><PERSON><PERSON>", "hideTimestamp", "messageF<PERSON>att<PERSON>", "formattedMessage", "hasBeenEdited", "locale", "_c", "formatChatMessageLinks", "message", "tokenize", "createDefaultGrammar", "tok", "i", "content", "href"], "mappings": ";;;;;;AAoBO,MAAMA,KAEgC,gBAAAC,EAAA;AAAA,EAC3C,SAAwBC,GAA4BC,GAAK;AACvD,UAAM,EAAE,aAAAC,EAAA,IAAgBC,GAAkBH,CAAK;AAC/C,2CACG,UAAO,EAAA,KAAAC,GAAW,GAAGC,EAAA,GACnBF,EAAM,QACT;AAAA,EAAA;AAGN,GCPaI,KAEgC,gBAAAL,EAAA;AAAA,EAC3C,SAAyB,EAAE,MAAAM,GAAM,GAAGL,EAAA,GAAgCC,GAAK;AACjE,UAAAK,IAAkBC,EAAmBF,CAAI;AAC/C,WACG,gBAAAN,EAAA,cAAA,OAAA,EAAI,KAAAE,GAAW,GAAGD,KAChBM,CACH;AAAA,EAAA;AAGN,GChBaE,KAEgC,gBAAAT,EAAA;AAAA,EAC3C,SAAoBC,GAAwBC,GAAK;AAC/C,UAAM,EAAE,aAAAQ,EAAY,IAAIC,GAAc,EAAE,OAAAV,GAAO;AAE/C,2CACG,UAAO,EAAA,KAAAC,GAAW,GAAGQ,EAAA,GACnBT,EAAM,QACT;AAAA,EAAA;AAGN,GCXaW,KAEgC,gBAAAZ,EAAA;AAAA,EAC3C,SAA0BC,GAA8BC,GAAK;AAC3D,UAAM,EAAE,aAAAC,EAAA,IAAgBU,GAAoBZ,CAAK;AACjD,2CACG,UAAO,EAAA,KAAAC,GAAW,GAAGC,EAAA,GACnBF,EAAM,QACT;AAAA,EAAA;AAGN,GCvBMa,KAAwB,CAACb,MAC5B,gBAAAD,EAAA,cAAA,OAAA,EAAI,OAAM,8BAA6B,OAAO,IAAI,QAAQ,IAAI,MAAK,gBAAgB,GAAGC,EAAA,GACpF,gBAAAD,EAAA,cAAA,QAAA,EAAK,GAAE,mMAAA,CAAmM,GAC1M,gBAAAA,EAAA,cAAA,QAAA,EAAK,GAAE,iGAAA,CAAiG,CAC3G,GCJIe,KAAgB,CAACd,sCACpB,OAAI,EAAA,OAAM,8BAA6B,OAAO,IAAI,QAAQ,IAAI,MAAK,gBAAgB,GAAGA,qCACpF,QAAK,EAAA,GAAE,oMAAmM,CAC7M,GCHIe,KAAmB,CAACf,MACxB,gBAAAD,EAAA,cAAC,SAAI,OAAM,8BAA6B,OAAO,IAAI,QAAQ,IAAI,SAAQ,aAAa,GAAGC,EACrF,GAAA,gBAAAD,EAAA;AAAA,EAAC;AAAA,EAAA;AAAA,IACC,MAAK;AAAA,IACL,GAAE;AAAA,EAAA;AACJ,CACF,GCNIiB,KAAc,CAAChB,MACnB,gBAAAD,EAAA,cAAC,SAAI,OAAM,8BAA6B,OAAO,IAAI,QAAQ,IAAI,MAAK,QAAQ,GAAGC,EAC7E,GAAA,gBAAAD,EAAA;AAAA,EAAC;AAAA,EAAA;AAAA,IACC,MAAK;AAAA,IACL,UAAS;AAAA,IACT,GAAE;AAAA,IACF,UAAS;AAAA,EAAA;AACX,GACA,gBAAAA,EAAA;AAAA,EAAC;AAAA,EAAA;AAAA,IACC,MAAK;AAAA,IACL,UAAS;AAAA,IACT,GAAE;AAAA,IACF,UAAS;AAAA,EAAA;AACX,CACF,GCdIkB,IAAa,CAACjB,MAClB,gBAAAD,EAAA,cAAC,SAAI,OAAM,8BAA6B,OAAO,IAAI,QAAQ,IAAI,MAAK,QAAQ,GAAGC,EAC7E,GAAA,gBAAAD,EAAA;AAAA,EAAC;AAAA,EAAA;AAAA,IACC,MAAK;AAAA,IACL,UAAS;AAAA,IACT,GAAE;AAAA,IACF,UAAS;AAAA,EAAA;AACX,CACF,GCRImB,KAAqB,CAAClB,MAC1B,gBAAAD,EAAA,cAAC,OAAI,EAAA,OAAM,8BAA6B,OAAO,IAAI,QAAQ,IAAI,MAAK,QAAQ,GAAGC,EAAA,GAC5E,gBAAAD,EAAA,cAAA,KAAA,EAAE,QAAO,gBAAe,eAAc,SAAQ,gBAAe,SAAQ,aAAa,IAAA,GAChF,gBAAAA,EAAA,cAAA,QAAA,EAAK,GAAE,gEAAA,CAAgE,CAC1E,CACF,GCLIoB,KAAc,CAACnB,MACnB,gBAAAD,EAAA,cAAC,SAAI,OAAM,8BAA6B,OAAO,IAAI,QAAQ,IAAI,MAAK,QAAQ,GAAGC,EAC7E,GAAA,gBAAAD,EAAA;AAAA,EAAC;AAAA,EAAA;AAAA,IACC,MAAK;AAAA,IACL,UAAS;AAAA,IACT,GAAE;AAAA,IACF,UAAS;AAAA,EAAA;AACX,CACF,GCRIqB,KAAe,CAACpB,MACpB,gBAAAD,EAAA,cAAC,SAAI,OAAM,8BAA6B,OAAO,IAAI,QAAQ,IAAI,MAAK,QAAQ,GAAGC,EAC7E,GAAA,gBAAAD,EAAA;AAAA,EAAC;AAAA,EAAA;AAAA,IACC,MAAK;AAAA,IACL,UAAS;AAAA,IACT,GAAE;AAAA,IACF,UAAS;AAAA,EAAA;AACX,GACA,gBAAAA,EAAA;AAAA,EAAC;AAAA,EAAA;AAAA,IACC,MAAK;AAAA,IACL,UAAS;AAAA,IACT,GAAE;AAAA,IACF,UAAS;AAAA,EAAA;AACX,CACF,GCdIsB,KAAoB,CAACrB,MACzB,gBAAAD,EAAA,cAAC,SAAI,OAAM,8BAA6B,OAAO,IAAI,QAAQ,IAAI,MAAK,QAAQ,GAAGC,EAC7E,GAAA,gBAAAD,EAAA;AAAA,EAAC;AAAA,EAAA;AAAA,IACC,MAAK;AAAA,IACL,UAAS;AAAA,IACT,GAAE;AAAA,IACF,UAAS;AAAA,EAAA;AACX,CACF,GCRIuB,KAAqB,CAACtB,MACzB,gBAAAD,EAAA,cAAA,OAAA,EAAI,OAAM,8BAA6B,OAAO,IAAI,QAAQ,IAAI,MAAK,gBAAgB,GAAGC,EAAA,GACpF,gBAAAD,EAAA,cAAA,QAAA,EAAK,GAAE,iZAAA,CAAiZ,GACxZ,gBAAAA,EAAA,cAAA,QAAA,EAAK,GAAE,iDAAA,CAAiD,CAC3D,GCJIwB,KAAa,CAACvB,MAClB,gBAAAD,EAAA,cAAC,SAAI,OAAM,8BAA6B,OAAO,IAAI,QAAQ,IAAI,MAAK,gBAAgB,GAAGC,EACrF,GAAA,gBAAAD,EAAA;AAAA,EAAC;AAAA,EAAA;AAAA,IACC,UAAS;AAAA,IACT,GAAE;AAAA,IACF,UAAS;AAAA,EAAA;AACX,GACC,gBAAAA,EAAA,cAAA,QAAA,EAAK,GAAE,sCAAA,CAAsC,CAChD,GCRIyB,KAA0B,CAACxB,MAC9B,gBAAAD,EAAA,cAAA,OAAA,EAAI,OAAM,8BAA6B,OAAO,IAAI,QAAQ,IAAI,MAAK,gBAAgB,GAAGC,EAAA,GACpF,gBAAAD,EAAA,cAAA,QAAA,EAAK,GAAE,iPAAA,CAAiP,GACxP,gBAAAA,EAAA,cAAA,QAAA,EAAK,GAAE,iPAAA,CAAiP,CAC3P,GCJI0B,KAAqB,CAACzB,MACzB,gBAAAD,EAAA,cAAA,OAAA,EAAI,OAAM,8BAA6B,OAAO,IAAI,QAAQ,IAAI,MAAK,gBAAgB,GAAGC,EACrF,GAAA,gBAAAD,EAAA,cAAC,UAAK,GAAE,kKAAA,CAAkK,GAC1K,gBAAAA,EAAA,cAAC,UAAK,GAAE,kKAAA,CAAkK,GACzK,gBAAAA,EAAA,cAAA,KAAA,EAAE,SAAS,KACV,GAAA,gBAAAA,EAAA,cAAC,QAAK,EAAA,GAAE,qFAAoF,GAC5F,gBAAAA,EAAA,cAAC,UAAK,GAAE,oFAAoF,CAAA,CAC9F,CACF,GCRI2B,KAAqB,CAAC1B,MAC1B,gBAAAD,EAAA,cAAC,SAAI,OAAM,8BAA6B,OAAO,IAAI,QAAQ,IAAI,MAAK,gBAAgB,GAAGC,EACrF,GAAA,gBAAAD,EAAA,cAAC,UAAK,GAAE,oFAAA,CAAoF,GAC3F,gBAAAA,EAAA,cAAA,QAAA,EAAK,GAAE,oFAAoF,CAAA,mCAC3F,KAAE,EAAA,SAAS,QACT,gBAAAA,EAAA,cAAA,QAAA,EAAK,GAAE,mFAAmF,CAAA,mCAC1F,QAAK,EAAA,GAAE,mKAAkK,GAC1K,gBAAAA,EAAA,cAAC,UAAK,GAAE,oFAAoF,CAAA,CAC9F,CACF,GCTI4B,KAAwB,CAAC3B,MAC7B,gBAAAD,EAAA,cAAC,OAAI,EAAA,OAAM,8BAA6B,OAAO,IAAI,QAAQ,IAAI,MAAK,gBAAgB,GAAGC,EACrF,GAAA,gBAAAD,EAAA,cAAC,KAAE,EAAA,SAAS,KACV,GAAA,gBAAAA,EAAA,cAAC,QAAK,EAAA,GAAE,0PAA0P,CAAA,GACjQ,gBAAAA,EAAA,cAAA,QAAA,EAAK,GAAE,0PAAA,CAA0P,CACpQ,CACF,GCNI6B,KAAqB,CAAC5B,MAC1B,gBAAAD,EAAA,cAAC,SAAI,OAAM,8BAA6B,OAAO,IAAI,QAAQ,IAAI,MAAK,QAAQ,GAAGC,EAC7E,GAAA,gBAAAD,EAAA;AAAA,EAAC;AAAA,EAAA;AAAA,IACC,MAAK;AAAA,IACL,UAAS;AAAA,IACT,GAAE;AAAA,IACF,UAAS;AAAA,EAAA;AACX,GACA,gBAAAA,EAAA;AAAA,EAAC;AAAA,EAAA;AAAA,IACC,MAAK;AAAA,IACL,UAAS;AAAA,IACT,GAAE;AAAA,IACF,UAAS;AAAA,EAAA;AACX,CACF,GCdI8B,KAAyB,CAAC7B,MAC9B,gBAAAD,EAAA,cAAC,SAAI,OAAM,8BAA6B,OAAO,IAAI,QAAQ,IAAI,MAAK,QAAQ,GAAGC,EAC7E,GAAA,gBAAAD,EAAA,cAAC,KAAE,EAAA,MAAK,kBACL,gBAAAA,EAAA,cAAA,QAAA,EAAK,GAAE,wKAAA,CAAwK,GAChL,gBAAAA,EAAA;AAAA,EAAC;AAAA,EAAA;AAAA,IACC,UAAS;AAAA,IACT,GAAE;AAAA,IACF,UAAS;AAAA,EAAA;AACX,CACF,CACF,GCVI+B,IAAiB,CAAC9B,MACtB,gBAAAD,EAAA,cAAC,SAAI,OAAM,8BAA6B,OAAO,IAAI,QAAQ,IAAI,MAAK,QAAQ,GAAGC,EAC7E,GAAA,gBAAAD,EAAA;AAAA,EAAC;AAAA,EAAA;AAAA,IACC,MAAK;AAAA,IACL,UAAS;AAAA,IACT,GAAE;AAAA,IACF,UAAS;AAAA,EAAA;AACX,GACA,gBAAAA,EAAA;AAAA,EAAC;AAAA,EAAA;AAAA,IACC,MAAK;AAAA,IACL,UAAS;AAAA,IACT,GAAE;AAAA,IACF,UAAS;AAAA,IACT,SAAS;AAAA,EAAA;AACX,GACA,gBAAAA,EAAA;AAAA,EAAC;AAAA,EAAA;AAAA,IACC,MAAK;AAAA,IACL,UAAS;AAAA,IACT,GAAE;AAAA,IACF,UAAS;AAAA,EAAA;AACX,GACA,gBAAAA,EAAA;AAAA,EAAC;AAAA,EAAA;AAAA,IACC,MAAK;AAAA,IACL,UAAS;AAAA,IACT,GAAE;AAAA,IACF,UAAS;AAAA,IACT,SAAS;AAAA,EAAA;AACX,GACA,gBAAAA,EAAA;AAAA,EAAC;AAAA,EAAA;AAAA,IACC,MAAK;AAAA,IACL,UAAS;AAAA,IACT,GAAE;AAAA,IACF,UAAS;AAAA,EAAA;AACX,GACA,gBAAAA,EAAA;AAAA,EAAC;AAAA,EAAA;AAAA,IACC,MAAK;AAAA,IACL,UAAS;AAAA,IACT,GAAE;AAAA,IACF,UAAS;AAAA,IACT,SAAS;AAAA,EAAA;AACX,GACA,gBAAAA,EAAA;AAAA,EAAC;AAAA,EAAA;AAAA,IACC,MAAK;AAAA,IACL,UAAS;AAAA,IACT,GAAE;AAAA,IACF,UAAS;AAAA,EAAA;AACX,GACA,gBAAAA,EAAA;AAAA,EAAC;AAAA,EAAA;AAAA,IACC,MAAK;AAAA,IACL,UAAS;AAAA,IACT,GAAE;AAAA,IACF,UAAS;AAAA,IACT,SAAS;AAAA,EAAA;AACX,GACA,gBAAAA,EAAA;AAAA,EAAC;AAAA,EAAA;AAAA,IACC,MAAK;AAAA,IACL,UAAS;AAAA,IACT,GAAE;AAAA,IACF,UAAS;AAAA,IACT,SAAS;AAAA,EAAA;AACX,GACA,gBAAAA,EAAA;AAAA,EAAC;AAAA,EAAA;AAAA,IACC,MAAK;AAAA,IACL,UAAS;AAAA,IACT,GAAE;AAAA,IACF,UAAS;AAAA,IACT,SAAS;AAAA,EAAA;AACX,GACA,gBAAAA,EAAA;AAAA,EAAC;AAAA,EAAA;AAAA,IACC,MAAK;AAAA,IACL,UAAS;AAAA,IACT,GAAE;AAAA,IACF,UAAS;AAAA,IACT,SAAS;AAAA,EAAA;AACX,GACA,gBAAAA,EAAA;AAAA,EAAC;AAAA,EAAA;AAAA,IACC,MAAK;AAAA,IACL,UAAS;AAAA,IACT,GAAE;AAAA,IACF,UAAS;AAAA,IACT,SAAS;AAAA,EAAA;AACX,CACF,GClFIgC,KAAuB,CAAC/B,MAC5B,gBAAAD,EAAA,cAAC,OAAI,EAAA,OAAM,8BAA6B,OAAO,IAAI,QAAQ,IAAI,MAAK,QAAQ,GAAGC,EAAA,GAC5E,gBAAAD,EAAA,cAAA,KAAA,EAAE,QAAO,gBAAe,eAAc,SAAQ,gBAAe,SAAQ,aAAa,IAAA,GAChF,gBAAAA,EAAA,cAAA,QAAA,EAAK,GAAE,qEAAA,CAAqE,CAC/E,CACF,GCWWiC,KAEgC,gBAAAjC,EAAA;AAAA,EAC3C,SAAqB,EAAE,UAAAkC,GAAU,GAAGjC,EAAA,GAA2BC,GAAK;AAClE,UAAMiC,IAAsBC,EAAwB,GAE9C,EAAE,aAAA1B,GAAa,SAAA2B,EAAQ,IAAIC,GAAe;AAAA,MAC9C,UAAUJ,KAAYC;AAAA,MACtB,OAAAlC;AAAA,IAAA,CACD;AAGC,WAAA,gBAAAD,EAAA,cAACuC,GAAc,UAAd,MACE,CAACC,MACAA,MAAkB,UACf,gBAAAxC,EAAA,cAAA,UAAA,EAAO,KAAAE,GAAW,GAAGQ,KACnBT,EAAM,WACLA,EAAM,WACJoC,IACF,gBAAArC,EAAA,cAACyC,QAAkB,IAEnB,gBAAAzC,EAAA,cAAC0C,IAAgB,IAAA,CAErB,CAGN;AAAA,EAAA;AAGN,GCTaC,KAEgC,gBAAA3C,EAAA;AAAA,EAC3C,SACE;AAAA,IACE,MAAA4C;AAAA,IACA,kBAAAC;AAAA,IACA,sBAAAC;AAAA,IACA,oBAAAC;AAAA,IACA,qBAAAC;AAAA,IACA,YAAAC;AAAA,IACA,OAAAC;AAAA,IACA,oBAAAC;AAAA,IACA,SAAAC;AAAA,IACA,GAAGnD;AAAA,KAELC,GACA;AACA,UAAMI,IAAO+C,GAAoB,GAC3BC,IAActD,EAAM;AAAA,MACxB,CAACuD,MAAa;AACZ,QAAIjD,KAEGA,EAAA,KAAKkD,GAAU,mBAAmBD,CAAC,GAE1CH,KAAA,QAAAA,EAAUG;AAAA,MACZ;AAAA,MACA,CAACjD,GAAM8C,CAAO;AAAA,IAChB,GACM,EAAE,SAAAK,GAAS,gBAAAC,GAAgB,sBAAAC,GAAsB,WAAAC,EAAA,IAAcC,GAAqB;AAAA,MACxF,MAAAjB;AAAA,MACA,MAAAtC;AAAA,MACA,OAAA4C;AAAA,MACA,oBAAAC;AAAA,MACA,SAASG;AAAA,IAAA,CACV;AACD,IAAAtD,EAAM,UAAU,MAAM;AACpB,MAAI6C,MAAqB,UACvBc,EAAqBd,CAAgB;AAAA,IACvC,GACC,CAACc,CAAoB,CAAC,GAEzB3D,EAAM,UAAU,MAAM;AAChB,MAAA,OAAO+C,KAAuB,cAChCA,EAAmBU,CAAO;AAAA,IAC5B,GACC,CAACV,GAAoBU,CAAO,CAAC,GAEhCzD,EAAM,UAAU,MAAM;AAChB,MAAA0D,KAAkBA,MAAmB,OACvCZ,KAAA,QAAAA,EAAuBY;AAAA,IACzB,GACC,CAACA,CAAc,CAAC;AAEb,UAAAI,IAA2B,OAAOC,MAAqB;AACvD,UAAA;AACF,cAAMJ,EAAqBI,GAAU,EAAE,OAAOd,GAAY;AAAA,eACnDM,GAAG;AACV,YAAIA,aAAa;AACf,UAAAP,KAAA,QAAAA,EAAsBO;AAAA;AAEhB,gBAAAA;AAAA,MACR;AAAA,IAEJ,GAEM7C,IAAcV,EAAM;AAAA,MACxB,MAAMgE,EAAW/D,GAAO,EAAE,WAAA2D,EAAa,GAAA,EAAE,WAAW,WAAW;AAAA,MAC/D,CAACA,GAAW3D,CAAK;AAAA,IACnB,GAEMgE,IAAa,CAAC,CAACR,EAAQ,KAAK,CAACS,MAASA,EAAK,MAAM,YAAA,EAAc,WAAW,SAAS,CAAC;AAEjF,aAAAC,EAASJ,GAAkBL,GAAwBU,GAAe;AACzE,aACEL,MAAaL,KAAmB,CAACO,KAAcG,MAAU,KAAKV,MAAmB;AAAA,IAAA;AAKnF,WAAA,gBAAA1D,EAAA,cAAC,QAAG,KAAAE,GAAW,GAAGQ,KACf+C,EAAQ,IAAI,CAACY,GAAQD,MACpB,gBAAApE,EAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACC,KAAKqE,EAAO;AAAA,QACZ,IAAIA,EAAO;AAAA,QACX,kBAAgBF,EAASE,EAAO,UAAUX,GAAgBU,CAAK;AAAA,QAC/D,iBAAeD,EAASE,EAAO,UAAUX,GAAgBU,CAAK;AAAA,QAC9D,MAAK;AAAA,MAAA;AAAA,MAEL,gBAAApE,EAAA,cAAC,UAAO,EAAA,WAAU,aAAY,SAAS,MAAM8D,EAAyBO,EAAO,QAAQ,EAClF,GAAAA,EAAO,KACV;AAAA,IAAA,CAEH,CACH;AAAA,EAAA;AAGN,GCrHaC,KAEgC,gBAAAtE,EAAA;AAAA,EAC3C,SAAoB,EAAE,OAAAuE,IAAQ,eAAe,GAAGtE,KAAkCC,GAAK;AACrF,UAAMI,IAAOkE,GAAe,GACtB,EAAE,aAAA9D,EAAY,IAAI+D,GAAc,EAAE,MAAAnE,GAAM,OAAAL,GAAO;AAErD,WACG,gBAAAD,EAAA,cAAA,UAAA,EAAO,KAAAE,GAAW,GAAGQ,KACnB6D,CACH;AAAA,EAAA;AAGN,GCbaG,KAEgC,gBAAA1E,EAAA;AAAA,EAC3C,SAA0B,EAAE,OAAAuE,GAAO,GAAGtE,EAAA,GAAkCC,GAAK;AAC3E,UAAMI,IAAOkE,GAAe,GACtB,EAAE,aAAaG,GAAY,cAAAC,EAAA,IAAiBH,GAAc,EAAE,MAAAnE,GAAM,OAAAL,GAAO,GACzE,EAAE,aAAAS,GAAa,cAAAmE,MAAiBC,GAAc,EAAE,MAAAxE,GAAM,OAAOqE,GAAY,GACzE,EAAE,OAAAI,GAAO,GAAGC,EAAA,IAActE;AAC1B,WAAAqE,EAAA,UAAUH,KAAgBC,IAAe,SAAS,SAGrD,gBAAA7E,EAAA,cAAA,UAAA,EAAO,KAAAE,GAAU,OAAA6E,GAAe,GAAGC,EAAA,GACjCT,KAAS,SAAUK,IAAyB,UAAV,OAAiB,EACtD;AAAA,EAAA;AAGN;ACrBgB,SAAAK,GAAcC,GAAsBC,GAAkB;AACpE,UAAQD,GAAQ;AAAA,IACd,KAAKE,EAAM,OAAO;AAChB,aAAOD,IAAU,gBAAAnF,EAAA,cAACqF,IAAQ,IAAA,oCAAMC,IAAgB,IAAA;AAAA,IAClD,KAAKF,EAAM,OAAO;AAChB,aAAOD,IAAU,gBAAAnF,EAAA,cAACuF,IAAW,IAAA,oCAAMC,IAAmB,IAAA;AAAA,IACxD,KAAKJ,EAAM,OAAO;AAChB,aAAOD,IAAU,gBAAAnF,EAAA,cAACyF,IAAoB,IAAA,oCAAMC,IAAgB,IAAA;AAAA,IAC9D;AACS;AAAA,EAAA;AAEb;AAKO,SAASC,GAAyBC,GAA4B;AACnE,UAAQA,GAAS;AAAA,IACf,KAAKC,EAAkB;AACrB,6CAAQC,IAAqB,IAAA;AAAA,IAC/B,KAAKD,EAAkB;AACrB,6CAAQE,IAAgB,IAAA;AAAA,IAC1B,KAAKF,EAAkB;AACrB,6CAAQG,IAAgB,IAAA;AAAA,IAC1B;AACE,6CAAQC,IAAmB,IAAA;AAAA,EAAA;AAEjC;ACXa,MAAAC,KAEgC,gBAAAlG,EAAA,WAAW,SAEtD,EAAE,UAAAmG,GAAU,GAAGlG,EAAM,GAAwBC,GAA4C;AACzF,QAAM,EAAE,aAAAC,GAAa,SAAAgF,MAAYiB,GAAenG,CAAK,GAC/C,CAACoG,GAAUC,CAAW,IAAItG,EAAM,SAAS,EAAK;AACpD,SAAAA,EAAM,UAAU,MAAM;AACpB,IAAAsG,EAAY,EAAI;AAAA,EAClB,GAAG,EAAE,GAEHD,KACE,gBAAArG,EAAA,cAAC,UAAO,EAAA,KAAAE,GAAW,GAAGC,EAClB,IAAAgG,KAAY,OAASlB,GAAchF,EAAM,QAAQkF,CAAO,GACzDlF,EAAM,QACT;AAGN,CAAC,GCjCYsG,KAEgC,gBAAAvG,EAAA,WAG3C,SAAoCC,GAAwCC,GAAK;AACjF,QAAM,EAAE,WAAA0D,GAAW,SAAAgC,MAAYY,GAA8BvG,CAAK,GAC5DwG,IAAezG,EAAM,QAAQ,OAC1B,EAAE,GAAGgE,EAAW/D,GAAO,EAAE,WAAA2D,GAAgC,GAAG,mBAAmBgC,EAAQ,IAC7F,CAACA,GAAS3F,GAAO2D,CAAS,CAAC;AAE5B,SAAA,gBAAA5D,EAAA,cAAC,SAAI,KAAAE,GAAW,GAAGuG,KAChBxG,EAAM,YAAY0F,GAAyBC,CAAO,CACrD;AAEJ,CAAC,GCbYc,IAEgC,gBAAA1G,EAAA;AAAA,EAC3C,SAAyB,EAAE,aAAA2G,GAAa,GAAG1G,EAAA,GAA+BC,GAAK;AACvE,UAAA0G,IAAIC,GAAqBF,CAAW,GAEpC,EAAE,WAAA/C,GAAW,cAAAkD,EAAiB,IAAA9G,EAAM,QAAQ,MACzC+G,GAAqBH,CAAC,GAC5B,CAACA,CAAC,CAAC,GAEA,EAAE,UAAAI,GAAU,MAAAC,MAASC,GAAmBJ,GAAc;AAAA,MAC1D,MAAMF,EAAE;AAAA,MACR,UAAUA,EAAE;AAAA,MACZ,UAAUA,EAAE;AAAA,IAAA,CACb,GAEKlG,IAAcV,EAAM,QAAQ,MACzBgE,EAAW/D,GAAO,EAAE,WAAA2D,GAAW,4BAA4BqD,GAAM,GACvE,CAAChH,GAAO2D,GAAWqD,CAAI,CAAC;AAGzB,WAAA,gBAAAjH,EAAA,cAAC,QAAK,EAAA,KAAAE,GAAW,GAAGQ,KACjBuG,MAAS,KAAKA,IAAOD,GACrB/G,EAAM,QACT;AAAA,EAAA;AAGN,GC3BakH,KAEgC,gBAAAnH,EAAA;AAAA,EAC3C,SACE,EAAE,UAAAkC,GAAU,MAAAkF,IAAO,UAAU,GAAGnH,EAAM,GACtCC,GACA;AACA,UAAM,EAAE,WAAA0D,GAAW,SAAAyD,MAAYC,GAAuBpF,CAAQ,GAExDqF,IACJH,MAAS,YAAaA,MAAS,WAAWC,KAAaD,MAAS,aAAa,CAACC,GAE1EG,IAAYxH,EAAM;AAAA,MACtB,MACEgE,EAAW/D,GAAO;AAAA,QAChB,WAAA2D;AAAA,MAAA,CACD;AAAA,MACH,CAACA,GAAW3D,CAAK;AAAA,IACnB;AAEA,WAAKsH,IAKF,gBAAAvH,EAAA,cAAA,OAAA,EAAI,KAAAE,GAAW,GAAGsH,GAAW,iBAAeH,EAAA,GAC1CpH,EAAM,YAAYgF,GAAc/C,EAAS,QAAQ,CAACmF,CAAO,CAC5D,IANO;AAAA,EAMP;AAGN,GC5CMI,KAA4B,CAACxH,MACjC,gBAAAD,EAAA;AAAA,EAAC;AAAA,EAAA;AAAA,IACC,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,SAAQ;AAAA,IACR,qBAAoB;AAAA,IACpB,MAAK;AAAA,IACL,OAAM;AAAA,IACL,GAAGC;AAAA,EAAA;AAAA,EAEJ,gBAAAD,EAAA;AAAA,IAAC;AAAA,IAAA;AAAA,MACC,GAAE;AAAA,MACF,MAAK;AAAA,MACL,aAAa;AAAA,IAAA;AAAA,EACf;AAAA,EACA,gBAAAA,EAAA;AAAA,IAAC;AAAA,IAAA;AAAA,MACC,GAAE;AAAA,MACF,MAAK;AAAA,MACL,aAAa;AAAA,IAAA;AAAA,EAAA;AAEjB;ACTK,SAAS0H,GACdC,GACAC,IAAgC,IAChC;AACM,QAAA,CAACC,GAAaC,CAAc,IAAI9H,EAAM,SAAS+H,GAAqBJ,CAAe,CAAC,GAEpF,CAACN,GAASW,CAAQ,IAAIhI,EAAM,SAAS6H,KAAA,gBAAAA,EAAa,OAAO,GACzD,CAACI,GAAcC,CAAa,IAAIlI,EAAM,SAAS6H,KAAA,gBAAAA,EAAa,YAAY,GAExE,CAAC3E,GAAOiF,CAAQ,IAAInI,EAAM,SAAS6H,KAAA,gBAAAA,EAAa,KAAK,GACrD,CAACO,GAAaC,CAAc,IAAIrI,EAAM,SAAmC,WAAW,GACpFsI,IAAkBtI,EAAM,OAA4C,GAEpE,EAAE,WAAA4D,GAAW,eAAA2E,EAAkB,IAAAvI,EAAM,QAAQ,MAC1CwI,GAAgBb,CAAe,GACrC;AAAA,IACDA,EAAgB,YAAY,OAAOA,EAAgB,YAAY;AAAA,IAC/DA,EAAgB;AAAA,IAChBc,EAAiBd,CAAe,KAAKA,EAAgB,YAAY;AAAA,EAAA,CAClE;AAED,SAAA3H,EAAM,UAAU,MAAM;AACpB,UAAM0I,IAAeH,EAAc,UAAU,CAACV,MAAgB;AACxD,MAAAc,EAAA,MAAM,gBAAgBd,CAAW,GACrCC,EAAeD,CAAW,GAC1BG,EAASH,KAAAA,gBAAAA,EAAa,OAAO,GAC7BK,EAAcL,KAAAA,gBAAAA,EAAa,YAAY,GACvCM,EAASN,KAAAA,gBAAAA,EAAa,KAAK;AAAA,IAAA,CAC5B;AACM,WAAA,MAAMa,KAAA,gBAAAA,EAAc;AAAA,EAAY,GACtC,CAACH,CAAa,CAAC,GAElBvI,EAAM,UAAU,MAAM;;AACpB,WAAIkD,MACEoF,EAAgB,WACZpF,EAAA,OAAOoF,EAAgB,OAAO,IAGpCM,IAAAhB,EAAQ,YAAR,QAAAgB,EAAiB,WACjB,EAAEjB,EAAgB,YAAY,YAAWzE,KAAA,gBAAAA,EAAO,UAAS,YAEnDA,EAAA,OAAO0E,EAAQ,QAAQ,OAAO,IAGxBU,EAAA,WAAUO,IAAAjB,EAAQ,YAAR,gBAAAiB,EAAiB,SACpC,MAAM;AACX,MAAIP,EAAgB,YACXpF,KAAA,QAAAA,EAAA,OAAOoF,EAAgB;AAAA,IAElC;AAAA,EACC,GAAA,CAACpF,GAAO0E,EAAQ,OAAO,CAAC,GAE3B5H,EAAM,UAAU,MAAM;;AAIlB,QAAA,SAAO4I,IAAAf,KAAA,gBAAAA,EAAa,eAAb,gBAAAe,EAAyB,UAAU,YAC1C,SAAOC,IAAAhB,KAAA,gBAAAA,EAAa,eAAb,gBAAAgB,EAAyB,WAAW,UAC3C;AACA,YAAMC,IACJjB,EAAY,WAAW,QAAQA,EAAY,WAAW,SAAS,cAAc;AAC/E,MAAAQ,EAAeS,CAAY;AAAA,IAAA;AAAA,EAC7B,GACC,CAACjB,CAAW,CAAC,GAET;AAAA,IACL,aAAAA;AAAA,IACA,SAAAR;AAAA,IACA,cAAAY;AAAA,IACA,OAAA/E;AAAA,IACA,cAAcc,EAAW4D,EAAQ,OAAO;AAAA,MACtC,WAAAhE;AAAA,MACA,6BAA6B+D,EAAgB,YAAY;AAAA,MACzD,kBAAkBE,KAAA,gBAAAA,EAAa;AAAA,MAC/B,IAAIA,KAAA,gBAAAA,EAAa,UAAS,WAAW,EAAE,uBAAuBO,EAAY;AAAA,IAC3E,CAAA;AAAA,EACH;AACF;ACtFA,IAAIW,KAAkB,uBAGlBC,IAAM,KAGNC,KAAY,mBAGZC,KAAS,cAGTC,KAAa,sBAGbC,KAAa,cAGbC,KAAY,eAGZC,KAAe,UAGfC,KAAa,OAAOC,KAAU,YAAYA,KAAUA,EAAO,WAAW,UAAUA,GAGhFC,KAAW,OAAO,QAAQ,YAAY,QAAQ,KAAK,WAAW,UAAU,MAGxEC,KAAOH,MAAcE,MAAY,SAAS,aAAa,EAAG,GAG1DE,KAAc,OAAO,WAOrBC,KAAiBD,GAAY,UAG7BE,KAAY,KAAK,KACjBC,KAAY,KAAK,KAkBjBC,IAAM,WAAW;AACnB,SAAOL,GAAK,KAAK,IAAK;AACxB;AAwDA,SAASM,GAASC,GAAMC,GAAMtC,GAAS;AACrC,MAAIuC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,IAAiB,GACjBC,IAAU,IACVC,IAAS,IACTC,IAAW;AAEf,MAAI,OAAOX,KAAQ;AACjB,UAAM,IAAI,UAAUlB,EAAe;AAErC,EAAAmB,IAAOW,EAASX,CAAI,KAAK,GACrBY,EAASlD,CAAO,MAClB8C,IAAU,CAAC,CAAC9C,EAAQ,SACpB+C,IAAS,aAAa/C,GACtByC,IAAUM,IAASd,GAAUgB,EAASjD,EAAQ,OAAO,KAAK,GAAGsC,CAAI,IAAIG,GACrEO,IAAW,cAAchD,IAAU,CAAC,CAACA,EAAQ,WAAWgD;AAG1D,WAASG,EAAWC,GAAM;AACxB,QAAIC,IAAOd,GACPe,IAAUd;AAEd,WAAAD,IAAWC,IAAW,QACtBK,IAAiBO,GACjBV,IAASL,EAAK,MAAMiB,GAASD,CAAI,GAC1BX;AAAA,EACX;AAEE,WAASa,EAAYH,GAAM;AAEzB,WAAAP,IAAiBO,GAEjBT,IAAU,WAAWa,GAAclB,CAAI,GAEhCQ,IAAUK,EAAWC,CAAI,IAAIV;AAAA,EACxC;AAEE,WAASe,EAAcL,GAAM;AAC3B,QAAIM,IAAoBN,IAAOR,GAC3Be,IAAsBP,IAAOP,GAC7BH,IAASJ,IAAOoB;AAEpB,WAAOX,IAASb,GAAUQ,GAAQD,IAAUkB,CAAmB,IAAIjB;AAAA,EACvE;AAEE,WAASkB,EAAaR,GAAM;AAC1B,QAAIM,IAAoBN,IAAOR,GAC3Be,IAAsBP,IAAOP;AAKjC,WAAQD,MAAiB,UAAcc,KAAqBpB,KACzDoB,IAAoB,KAAOX,KAAUY,KAAuBlB;AAAA,EACnE;AAEE,WAASe,IAAe;AACtB,QAAIJ,IAAOjB,EAAK;AAChB,QAAIyB,EAAaR,CAAI;AACnB,aAAOS,EAAaT,CAAI;AAG1B,IAAAT,IAAU,WAAWa,GAAcC,EAAcL,CAAI,CAAC;AAAA,EAC1D;AAEE,WAASS,EAAaT,GAAM;AAK1B,WAJAT,IAAU,QAINK,KAAYT,IACPY,EAAWC,CAAI,KAExBb,IAAWC,IAAW,QACfE;AAAA,EACX;AAEE,WAASoB,IAAS;AAChB,IAAInB,MAAY,UACd,aAAaA,CAAO,GAEtBE,IAAiB,GACjBN,IAAWK,IAAeJ,IAAWG,IAAU;AAAA,EACnD;AAEE,WAASoB,IAAQ;AACf,WAAOpB,MAAY,SAAYD,IAASmB,EAAa1B,EAAG,CAAE;AAAA,EAC9D;AAEE,WAAS6B,IAAY;AACnB,QAAIZ,IAAOjB,EAAK,GACZ8B,IAAaL,EAAaR,CAAI;AAMlC,QAJAb,IAAW,WACXC,IAAW,MACXI,IAAeQ,GAEXa,GAAY;AACd,UAAItB,MAAY;AACd,eAAOY,EAAYX,CAAY;AAEjC,UAAIG;AAEF,eAAAJ,IAAU,WAAWa,GAAclB,CAAI,GAChCa,EAAWP,CAAY;AAAA,IAEtC;AACI,WAAID,MAAY,WACdA,IAAU,WAAWa,GAAclB,CAAI,IAElCI;AAAA,EACX;AACE,SAAAsB,EAAU,SAASF,GACnBE,EAAU,QAAQD,GACXC;AACT;AA2BA,SAASd,EAASgB,GAAO;AACvB,MAAIC,IAAO,OAAOD;AAClB,SAAO,CAAC,CAACA,MAAUC,KAAQ,YAAYA,KAAQ;AACjD;AA0BA,SAASC,GAAaF,GAAO;AAC3B,SAAO,CAAC,CAACA,KAAS,OAAOA,KAAS;AACpC;AAmBA,SAASG,GAASH,GAAO;AACvB,SAAO,OAAOA,KAAS,YACpBE,GAAaF,CAAK,KAAKlC,GAAe,KAAKkC,CAAK,KAAK7C;AAC1D;AAyBA,SAAS4B,EAASiB,GAAO;AACvB,MAAI,OAAOA,KAAS;AAClB,WAAOA;AAET,MAAIG,GAASH,CAAK;AAChB,WAAO9C;AAET,MAAI8B,EAASgB,CAAK,GAAG;AACnB,QAAII,IAAQ,OAAOJ,EAAM,WAAW,aAAaA,EAAM,QAAO,IAAKA;AACnE,IAAAA,IAAQhB,EAASoB,CAAK,IAAKA,IAAQ,KAAMA;AAAA,EAC7C;AACE,MAAI,OAAOJ,KAAS;AAClB,WAAOA,MAAU,IAAIA,IAAQ,CAACA;AAEhC,EAAAA,IAAQA,EAAM,QAAQ5C,IAAQ,EAAE;AAChC,MAAIiD,IAAW/C,GAAW,KAAK0C,CAAK;AACpC,SAAQK,KAAY9C,GAAU,KAAKyC,CAAK,IACpCxC,GAAawC,EAAM,MAAM,CAAC,GAAGK,IAAW,IAAI,CAAC,IAC5ChD,GAAW,KAAK2C,CAAK,IAAI9C,IAAM,CAAC8C;AACvC;AAEA,IAAAM,KAAiBpC;;ACnDjB,SAASqC,GAAWpC,GAAM;AACxB,QAAMqC,IAAUC,EAAOtC,CAAI;AAC3B,EAAAqC,EAAQ,UAAUrC,GAClBuC;AAAA,IACE,MAAM,MAAM;AACV,MAAAF,EAAQ,QAAS;AAAA,IAClB;AAAA,IACD,CAAA;AAAA,EACD;AACH;AAGA,SAASG,GAAoBxC,GAAMyC,IAAQ,KAAK9E,GAAS;AACvD,QAAM+E,IAAgBJ,EAAQ;AAC9B,EAAAF,GAAW,MAAM;AACf,IAAIM,EAAc,WAChBA,EAAc,QAAQ,OAAQ;AAAA,EAEpC,CAAG;AACD,QAAMf,IAAYgB,GAAQ,MAAM;AAC9B,UAAMC,IAAwB7C,EAASC,GAAMyC,GAAO9E,CAAO,GACrDkF,IAAc,IAAI7B,MACf4B,EAAsB,GAAG5B,CAAI;AAEtC,WAAA6B,EAAY,SAAS,MAAM;AACzB,MAAAD,EAAsB,OAAQ;AAAA,IAC/B,GACDC,EAAY,YAAY,MACf,CAAC,CAACH,EAAc,SAEzBG,EAAY,QAAQ,MACXD,EAAsB,MAAO,GAE/BC;AAAA,EACR,GAAE,CAAC7C,GAAMyC,GAAO9E,CAAO,CAAC;AACzB,SAAA4E,EAAU,MAAM;AACd,IAAAG,EAAc,UAAU3C,EAASC,GAAMyC,GAAO9E,CAAO;AAAA,EACtD,GAAE,CAACqC,GAAMyC,GAAO9E,CAAO,CAAC,GAClBgE;AACT;AACA,SAASmB,GAAiBC,GAAcN,GAAO9E,GAAS;AACtD,QAAMqF,IAAyD,CAACC,GAAMC,MAAUD,MAASC,GACnFC,IAAwBJ,aAAwB,WAAWA,EAAc,IAAGA,GAC5E,CAACK,GAAgBC,CAAiB,IAAIC,EAASH,CAAqB,GACpEI,IAAmBjB,EAAOa,CAAqB,GAC/CK,IAAuBhB;AAAA,IAC3Ba;AAAA,IACAZ;AAAA,IACA9E;AAAA,EACD;AACD,SAAKqF,EAAGO,EAAiB,SAASJ,CAAqB,MACrDK,EAAqBL,CAAqB,GAC1CI,EAAiB,UAAUJ,IAEtB,CAACC,GAAgBI,CAAoB;AAC9C;AA4BA,SAASC,GAAwB;AAAA,EAC/B,WAAAC,IAAY;AAAA,EACZ,MAAAjE,IAAO;AAAA,EACP,YAAAkE,IAAa;AAAA,EACb,mBAAAC,IAAoB;AAAA,EACpB,uBAAAC,IAAwB;AAAA,EACxB,UAAAC;AACF,IAAI,IAAI;AACN,MAAInF;AACJ,QAAM,CAAC1I,GAAK8N,CAAM,IAAIT,EAAS,IAAI,GAC7B,CAACU,GAAOC,CAAQ,IAAIX,EAAS,OAAO;AAAA,IACxC,gBAAgBO;AAAA,IAChB,OAAO;AAAA,EACX,EAAI,GACIK,IAAc5B,EAAQ;AAC5B,EAAA4B,EAAY,UAAUJ;AACtB,QAAMK,MAAWxF,IAAKqF,EAAM,UAAU,OAAO,SAASrF,EAAG,mBAAmBiF;AAC5E,EAAArB,EAAU,MAAM;AAKd,QAJI,CAACtM,KAED,EAAE,0BAA0B,WAE5BkO;AACF;AACF,QAAIC;AACJ,UAAMC,IAAW,IAAI;AAAA,MACnB,CAACC,MAAY;AACX,cAAMC,IAAa,MAAM,QAAQF,EAAS,UAAU,IAAIA,EAAS,aAAa,CAACA,EAAS,UAAU;AAClG,QAAAC,EAAQ,QAAQ,CAACE,MAAU;AACzB,gBAAMC,IAAiBD,EAAM,kBAAkBD,EAAW,KAAK,CAACG,MAAeF,EAAM,qBAAqBE,CAAU;AACpH,UAAAT,EAAS,EAAE,gBAAAQ,GAAgB,OAAAD,GAAO,GAC9BN,EAAY,WACdA,EAAY,QAAQO,GAAgBD,CAAK,GAEvCC,KAAkBb,KAAqBQ,MACzCA,EAAW,GACXA,IAAY;AAAA,QAExB,CAAS;AAAA,MACF;AAAA,MACD,EAAE,WAAAV,GAAW,MAAAjE,GAAM,YAAAkE,EAAU;AAAA,IAC9B;AACD,WAAAU,EAAS,QAAQpO,CAAG,GACb,MAAM;AACX,MAAAoO,EAAS,WAAY;AAAA,IACtB;AAAA,EACL,GAAK;AAAA,IACDpO;AAAA;AAAA,IAEA,KAAK,UAAUyN,CAAS;AAAA,IACxBjE;AAAA,IACAkE;AAAA,IACAQ;AAAA,IACAP;AAAA,EACJ,CAAG;AACD,QAAMe,IAAUrC,EAAO,IAAI;AAC3B,EAAAC,EAAU,MAAM;AACd,QAAIqC;AACJ,IAAI,CAAC3O,OAAS2O,IAAMZ,EAAM,UAAU,QAAgBY,EAAI,WAAW,CAAChB,KAAqB,CAACO,KAAUQ,EAAQ,YAAYX,EAAM,MAAM,WAClIW,EAAQ,UAAUX,EAAM,MAAM,QAC9BC,EAAS,EAAE,gBAAgBJ,GAAuB,OAAO,OAAM,CAAE;AAAA,EAEvE,GAAK,CAAC5N,GAAK+N,EAAM,OAAOJ,GAAmBO,GAAQN,CAAqB,CAAC;AACvE,QAAMxD,IAAS;AAAA,IACb0D;AAAA,IACA,CAAC,CAACC,EAAM;AAAA,IACRA,EAAM;AAAA,EACP;AACD,SAAA3D,EAAO,MAAMA,EAAO,CAAC,GACrBA,EAAO,iBAAiBA,EAAO,CAAC,GAChCA,EAAO,QAAQA,EAAO,CAAC,GAChBA;AACT;ACrcO,MAAMwE,KAEgC,gBAAA9O,EAAA;AAAA,EAC3C,SACE;AAAA,IACE,cAAA+O;AAAA,IACA,SAAAC;AAAA,IACA,6BAAAC;AAAA,IACA,UAAA/M;AAAA,IACA,oBAAAgN;AAAA,IACA,GAAGjP;AAAA,KAELC,GACA;AACM,UAAAiP,IAAiBC,EAAkBlN,CAAQ,GAE3CmN,IAAUrP,EAAM,OAAyB,IAAI;AACnD,IAAAA,EAAM,oBAAoBE,GAAK,MAAMmP,EAAQ,OAA2B;AAExE,UAAMC,IAAoBC,GAAiC,EAAE,MAAMF,EAAQ,SAAS,GAE9E,CAACG,CAA0B,IAAIC,GAA0BH,GAAmB,GAAI;AAEtF,IAAAtP,EAAM,UAAU,MAAM;AAElB,MAAAkP,KACAC,EAAe,uBAAuBO,MACtCF,KAAA,gBAAAA,EAA4B,oBAAmB,OAC/CF,KAAA,gBAAAA,EAAmB,oBAAmB,MAEvBH,EAAA,YAAY,cAAc,EAAK;AAAA,IAE/C,GAAA,CAACK,GAA4BL,GAAgBD,CAAkB,CAAC,GAEnElP,EAAM,UAAU,MAAM;AACpB,MACEkP,KACAC,EAAe,uBAAuBO,MACtCJ,KAAA,gBAAAA,EAAmB,oBAAmB,MAEvBH,EAAA,YAAY,cAAc,EAAI;AAAA,IAE9C,GAAA,CAACG,GAAmBH,GAAgBD,CAAkB,CAAC;AAEpD,UAAA;AAAA,MACJ,cAAAzI;AAAA,MACA,aAAakJ;AAAA,MACb,cAAA1H;AAAA,IAAA,IACEP,GAA4ByH,GAAgB;AAAA,MAC9C,SAASE;AAAA,MACT,OAAApP;AAAA,IAAA,CACD;AAED,IAAAD,EAAM,UAAU,MAAM;AACU,MAAAiP,KAAA,QAAAA,EAAA,CAAC,CAAChH;AAAA,IAAY,GAC3C,CAACA,GAAcgH,CAA2B,CAAC;AAExC,UAAAW,IAAe,CAACC,MAAwD;AAC5E,MAAAb,KAAA,QAAAA,EAAUa,IACVd,KAAA,QAAAA,EAAe,EAAE,aAAaI,KAAA,gBAAAA,EAAgB,aAAa,OAAOQ;IACpE;AAEO,WAAA,gBAAA3P,EAAA,cAAC,WAAM,KAAKqP,GAAU,GAAG5I,GAAc,OAAO,IAAM,SAASmJ,EAAc,CAAA;AAAA,EAAA;AAEtF,GCrDaE,IAEgC,gBAAA9P,EAAA;AAAA,EAC3C,SACE,EAAE,UAAAkC,GAAU,6BAAA+M,GAA6B,QAAAc,GAAQ,GAAG9P,EAAM,GAC1DC,GACA;AACM,UAAAiP,IAAiBC,EAAkBlN,CAAQ,GAE3CmN,IAAUrP,EAAM,OAAyB,IAAI;AACnD,IAAAA,EAAM,oBAAoBE,GAAK,MAAMmP,EAAQ,OAA2B;AAElE,UAAA;AAAA,MACJ,cAAA5I;AAAA,MACA,cAAAwB;AAAA,MACA,OAAA/E;AAAA,MACA,aAAayM;AAAA,IAAA,IACXjI,GAA4ByH,GAAgB;AAAA,MAC9C,SAASE;AAAA,MACT,OAAApP;AAAA,IAAA,CACD;AAED,WAAAD,EAAM,UAAU,MAAM;AACU,MAAAiP,KAAA,QAAAA,EAAA,CAAC,CAAChH;AAAA,IAAY,GAC3C,CAACA,GAAcgH,CAA2B,CAAC,GAE9CjP,EAAM,UAAU,MAAM;AAChB,MAAAkD,MAAU,UAAa6M,MAAW,WAGlC7M,aAAiB8M,KACnB9M,EAAM,UAAU6M,CAAM,IAEtBpH,EAAI,KAAK,gDAAgD;AAAA,IAC3D,GACC,CAACoH,GAAQ7M,CAAK,CAAC,GAElBlD,EAAM,UAAU,MAAM;AACpB,MAAI2P,MAAQ,UAAa1P,EAAM,UAAU,WAGrC0P,aAAeD,IACbC,EAAA,WAAW,CAAC1P,EAAM,KAAK,IAE3B0I,EAAI,KAAK,wDAAwD;AAAA,OAElE,CAAC1I,EAAM,OAAO0P,GAAKzM,CAAK,CAAC,GAEpB,gBAAAlD,EAAA,cAAA,SAAA,EAAM,KAAKqP,GAAU,GAAG5I,GAAc;AAAA,EAAA;AAElD;ACpDO,SAASwJ,GACdhQ,GAGA;AACM,QAAAiQ,IAAa,CAAC,CAACC,GAA2B;AAChD,SAAOlQ,EAAM,eAAe,CAACiQ,IAC3B,gBAAAlQ,EAAA,cAACoQ,GAAmB,UAAnB,EAA4B,OAAOnQ,EAAM,eACvCA,EAAM,QACT,IAEA,gBAAAD,EAAA,cAAAA,EAAA,UAAA,MAAGC,EAAM,QAAS;AAEtB;AAMO,SAASoQ,GACdpQ,GAGA;AACM,QAAAiQ,IAAa,CAAC,CAAC9N,EAAwB;AAC7C,SAAOnC,EAAM,YAAY,CAACiQ,IACxB,gBAAAlQ,EAAA,cAACsQ,EAAgB,UAAhB,EAAyB,OAAOrQ,EAAM,YAAWA,EAAM,QAAS,IAEjE,gBAAAD,EAAA,cAAAA,EAAA,UAAA,MAAGC,EAAM,QAAS;AAEtB;AA2BO,MAAMsQ,KAEgC,gBAAAvQ,EAAA;AAAA,EAC3C,SACE;AAAA,IACE,UAAAkC;AAAA,IACA,UAAAsO;AAAA,IACA,oBAAAC;AAAA,IACA,0BAAAC;AAAA,IACA,GAAGlJ;AAAA,KAELtH,GACA;;AACM,UAAAiP,IAAiBC,EAAkBlN,CAAQ,GAE3C,EAAE,cAAAuE,EAAa,IAAIkK,GAAmC;AAAA,MAC1D,WAAAnJ;AAAA,MACA,0BAAAkJ;AAAA,MACA,oBAAAD;AAAA,MACA,UAAUtB;AAAA,IAAA,CACX,GACKyB,IAAcC,GAAe1B,EAAe,WAAW,GACvD3M,IAAgBsO,GAAsB,GAEtCC,KAAyBnI,IAAAoI,SAAA,gBAAApI,EAAqB,kBAE9CqI,IAAkBjR,EAAM;AAAA,MAC5B,CAACkR,MAAwB;AACvB,QACE/B,EAAe,UACf,CAAC+B,KACD1O,KACAA,EAAc,IAAI,YAClB2O,GAAuBhC,GAAgB3M,EAAc,IAAI,KAAK,KAE9DA,EAAc,IAAI,SAAS,EAAE,KAAK,aAAa;AAAA,MAEnD;AAAA,MACA,CAAC2M,GAAgB3M,CAAa;AAAA,IAChC;AAEA,2CACG,OAAI,EAAA,KAAAtC,GAAU,OAAO,EAAE,UAAU,cAAe,GAAGuG,EAAA,mCACjD4J,IAAwB,EAAA,UAAUlB,KACjC,gBAAAnP,EAAA,cAACiQ,MAA2B,aAAad,EAAe,YACrD,GAAAqB,uDAEI/H,EAAiB0G,CAAc,QAC/BtG,IAAAsG,EAAe,gBAAf,gBAAAtG,EAA4B,UAAS,WACpCsG,EAAe,WAAW/J,EAAM,OAAO,UACvC+J,EAAe,WAAW/J,EAAM,OAAO,eACvC,gBAAApF,EAAA;AAAA,MAAC8O;AAAA,MAAA;AAAA,QACC,UAAUK;AAAA,QACV,6BAA6B8B;AAAA,QAC7B,oBAAoBF;AAAA,MAAA;AAAA,IACtB,IAEAtI,EAAiB0G,CAAc,KAC7B,gBAAAnP,EAAA;AAAA,MAAC8P;AAAA,MAAA;AAAA,QACC,UAAUX;AAAA,QACV,6BAA6B8B;AAAA,MAAA;AAAA,IAAA,GAInC,gBAAAjR,EAAA,cAAC,OAAI,EAAA,WAAU,6BACb,GAAA,gBAAAA,EAAA,cAACoR,IAAuB,IAAA,CAC1B,GACA,gBAAApR,EAAA,cAAC,OAAI,EAAA,WAAU,6BACZ,gBAAAA,EAAA,cAAA,OAAA,EAAI,WAAU,+BAAA,GACZmP,EAAe,WAAW/J,EAAM,OAAO,SAEnC,gBAAApF,EAAA,cAAAA,EAAA,UAAA,MAAA4Q,KAAgB,gBAAA5Q,EAAA,cAAAqR,IAAA,EAAe,OAAO,EAAE,aAAa,aAAa,GACnE,gBAAArR,EAAA;AAAA,MAACmH;AAAA,MAAA;AAAA,QACC,UAAU;AAAA,UACR,aAAagI,EAAe;AAAA,UAC5B,QAAQ/J,EAAM,OAAO;AAAA,QACvB;AAAA,QACA,MAAM;AAAA,MAAA;AAAA,IAAA,GAER,gBAAApF,EAAA,cAAC0G,GAAgB,IAAA,CACnB,IAGE,gBAAA1G,EAAA,cAAAA,EAAA,UAAA,MAAA,gBAAAA,EAAA,cAAC0F,IAAgB,EAAA,OAAO,EAAE,aAAa,YAAa,CAAA,GACnD,gBAAA1F,EAAA,cAAA0G,GAAA,MAAgB,WAAc,CACjC,CAEJ,GACC,gBAAA1G,EAAA,cAAAuG,IAAA,EAA2B,WAAU,+BAA+B,CAAA,CACvE,CACF,mCAEDtE,IAAY,EAAA,UAAUkN,GAAgB,CACzC,CACF,CACF;AAAA,EAAA;AAGN;AC/KO,SAASmC,GAAqBrR,GAAkC;AACrE,QAAMwG,IAAezC,EAAW/D,GAAO,EAAE,WAAW,mBAAmB;AAEvE,SAAQ,gBAAAD,EAAA,cAAA,OAAA,EAAK,GAAGyG,EAAA,GAAexG,EAAM,QAAS;AAChD;AAcO,SAASsR,GAAY,EAAE,UAAArP,GAAU,GAAGsF,KAA+B;AACxE,SAAQ,gBAAAxH,EAAA,cAAAuQ,IAAA,EAAgB,UAAArO,GAAqB,GAAGsF,GAAW;AAC7D;ACPO,SAASgK,GAAU,EAAE,QAAAC,GAAQ,GAAGxR,KAAyB;AAC9D,SAEK,gBAAAD,EAAA,cAAAA,EAAA,UAAA,MAAAyR,EAAO,IAAI,CAACtC,MAET,gBAAAnP,EAAA;AAAA,IAACsQ,EAAgB;AAAA,IAAhB;AAAA,MACC,OAAOnB;AAAA,MACP,KAAKuC,GAAoBvC,CAAc;AAAA,IAAA;AAAA,IAEtCwC,EAAiB1R,EAAM,QAAQ;AAAA,EAClC,CAEH,CACH;AAEJ;AC7BO,SAAS2R,GAAkB;AAAA,EAChC,gBAAAC;AAAA,EACA,UAAAC;AAAA,EACA,UAAAC;AAAA,EACA,aAAAC;AAAA,EACA,gBAAgBC;AAClB,GAA2B;AACzB,QAAM,CAACC,GAAaC,CAAc,IAAInS,EAAM,SAAS,EAAK;AAC1D,SAAAA,EAAM,UAAU,MAAM;AAChB,QAAA0I;AAGJ,WAAIuJ,MACFvJ,IAAe0J,GAA4BH,EAAiB,SAAS,GAAI,EAAE;AAAA,MACzEE;AAAA,IACF,IAEK,MAAM;AACX,MAAIzJ,KACFA,EAAa,YAAY;AAAA,IAE7B;AAAA,EAAA,GACC,CAACuJ,CAAgB,CAAC,GAGlB,gBAAAjS,EAAA,cAAA,OAAA,EAAI,WAAU,yBAAwB,4BAA0BkS,KAC/D,gBAAAlS,EAAA,cAAC,UAAO,EAAA,WAAU,aAAY,SAAS+R,EAAA,GACpC,gBAAA/R,EAAA,cAAAkB,GAAA,IAAW,CACd,GACA,gBAAAlB,EAAA,cAAC,QAAK,EAAA,WAAU,yBAAuB,GAAGgS,CAAW,OAAOH,CAAc,EAAG,GAC7E,gBAAA7R,EAAA,cAAC,UAAO,EAAA,WAAU,aAAY,SAAS8R,EAAA,GACpC,gBAAA9R,EAAA,cAAAkB,GAAA,IAAW,CACd,CACF;AAEJ;AC3CO,MAAMmR,KAEgC,gBAAArS,EAAA;AAAA,EAC3C,SAA6B,EAAE,gBAAA6R,GAAgB,aAAAG,EAAA,GAAyC9R,GAAK;AACrF,UAAAoS,IAAU,IAAI,MAAMT,CAAc,EAAE,KAAK,EAAE,EAAE,IAAI,CAACU,GAAGnO,MACrDA,IAAQ,MAAM4N,IACR,gBAAAhS,EAAA,cAAA,QAAA,EAAK,kBAAc,IAAC,KAAKoE,GAAO,IAEjC,gBAAApE,EAAA,cAAC,QAAK,EAAA,KAAKoE,EAAO,CAAA,CAE5B;AAED,WACG,gBAAApE,EAAA,cAAA,OAAA,EAAI,KAAAE,GAAU,WAAU,6BACtBoS,CACH;AAAA,EAAA;AAGN;ACQO,SAASE,GAAW,EAAE,QAAAf,GAAQ,GAAGxR,KAA0B;AAC1D,QAAAwS,IAASzS,EAAM,UAA0B,GAEzCyG,IAAezG,EAAM;AAAA,IACzB,MAAMgE,EAAW/D,GAAO,EAAE,WAAW,kBAAkB;AAAA,IACvD,CAACA,CAAK;AAAA,EACR,GACM,EAAE,QAAAyS,EAAO,IAAIC,GAAcF,GAAQhB,EAAO,MAAM,GAChDmB,IAAaC,GAAcH,EAAO,UAAUjB,CAAM;AAExD,SAAAqB,GAASL,GAAQ;AAAA,IACf,aAAaG,EAAW;AAAA,IACxB,cAAcA,EAAW;AAAA,EAAA,CAC1B,GAGC,gBAAA5S,EAAA,cAAC,SAAI,KAAKyS,GAAQ,sBAAoBG,EAAW,iBAAiB,GAAI,GAAGnM,EACvE,GAAA,gBAAAzG,EAAA,cAACwR,MAAU,QAAQoB,EAAW,UAAS3S,EAAM,QAAS,GACrDwR,EAAO,SAASiB,EAAO,YAEpB,gBAAA1S,EAAA,cAAAA,EAAA,UAAA,MAAA,gBAAAA,EAAA;AAAA,IAACqS;AAAA,IAAA;AAAA,MACC,gBAAgBO,EAAW;AAAA,MAC3B,aAAaA,EAAW;AAAA,IAAA;AAAA,EAAA,mCAEzBhB,IAAkB,EAAA,gBAAgBa,GAAS,GAAGG,EAAY,CAAA,CAC7D,CAEJ;AAEJ;ACvDA,MAAMG,KAAa,KACbC,KAAY,KACZC,IAAoB,GACpBC,KAAe,KAAK,IACpBC,MAAuB,IAAID,MAAgB;AA0B1C,SAASE,GAAe,EAAE,QAAA3B,GAAQ,aAAArJ,GAAa,GAAGnI,KAA8B;AAC/E,QAAAoT,IAAUrT,EAAM,OAAuB,IAAI,GAC3C,CAACsT,GAAWC,CAAY,IAAIvT,EAAM,SAAS,CAAC,GAC5C,EAAE,OAAAwT,GAAO,QAAAC,MAAWC,GAAQL,CAAO,GACnCM,IAAsBvL,MAExBqL,KAAUD,IACR,aACA,eAEAI,IACJD,MAAwB,aACpB,KAAK,IAAIH,IAAQL,IAAqBJ,EAAU,IAChD,KAAK,IAAIU,IAASP,IAAcF,EAAS,GACzCa,IAAiBC,GAAkB,GAEnCC,IAEA,KAAK,IADTJ,MAAwB,cACVF,IAASI,KAAkBD,KAC3BJ,IAAQK,KAAkBD,GADWX,CAAiB;AAGlE,MAAAe,IAAkB,KAAK,MAAMD,CAAY;AAC7C,EAAI,KAAK,IAAIA,IAAeT,CAAS,IAAI,MACrBU,IAAA,KAAK,MAAMV,CAAS,IAC7BA,MAAcS,KACvBR,EAAaQ,CAAY;AAGrB,QAAAE,IAAcC,GAAsBzC,GAAQuC,CAAe;AAEjE,SAAAhU,EAAM,gBAAgB,MAAM;AAC1B,IAAIqT,EAAQ,YACFA,EAAA,QAAQ,QAAQ,gBAAgBM,GACxCN,EAAQ,QAAQ,MAAM,YAAY,0BAA0BW,EAAgB,UAAU;AAAA,EACxF,GACC,CAACA,GAAiBL,CAAmB,CAAC,mCAGtC,SAAM,EAAA,KAAKA,GAAqB,WAAU,eAAc,KAAKN,GAAU,GAAGpT,qCACxEuR,IAAU,EAAA,QAAQyC,EAAc,GAAAhU,EAAM,QAAS,CAClD;AAEJ;ACjEO,SAASkU,GAAsB;AAAA,EACpC,OAAArI;AAAA,EACA,aAAAsI;AAAA,EACA,gBAAAC;AAAA,EACA,UAAA7D;AACF,GAAwD;AAChD,QAAA8D,IAAqBC,GAA6BzI,CAAK;AAE7D,SAAA9L,EAAM,UAAU,MAAM;AACpB,IAAA2I,EAAI,MAAM,oBAAoB,EAAE,OAAO2L,EAAmB,IAAI,OAAO,GACjEF,KAAeE,EAAmB,IAAI,SAAmBF,EAAAE,EAAmB,IAAI,KAAK;AAAA,KACxF,CAACA,EAAmB,IAAI,OAAOF,CAAW,CAAC,GAE9CpU,EAAM,UAAU,MAAM;AACpB,IAAA2I,EAAI,MAAM,kBAAkB,EAAE,aAAa2L,EAAmB,OAAO,OAAO,GACxED,KAAkBC,EAAmB,OAAO,SAC/BD,EAAAC,EAAmB,OAAO,KAAK;AAAA,KAE/C,CAACD,GAAgBC,EAAmB,OAAO,KAAK,CAAC,mCAE5C/R,GAAc,UAAd,EAAuB,OAAO+R,KAAqB9D,CAAS;AACtE;ACXO,MAAMgE,KAEgC,gBAAAxU,EAAA;AAAA,EAC3C,SAAyB,EAAE,UAAAkC,GAAU,GAAGjC,EAAA,GAA+BC,GAAK;AAOpE,UAAAiP,IAAiBC,EAAkBlN,CAAQ,GAE3CuS,IAAUC,GAAwBvF,GAAgB,EAAE,OAAO,GAAG,QAAQ,KAAK;AAG/E,WAAA,gBAAAnP,EAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACC,KAAAE;AAAA,QACA,OAAM;AAAA,QACN,QAAO;AAAA,QACP,SAAS;AAAA,QACR,GAAGD;AAAA,QACJ,WAAU;AAAA,MAAA;AAAA,MAEV,gBAAAD,EAAA,cAAC,UAAK,GAAE,KAAI,GAAE,KAAI,OAAM,QAAO,QAAO,OAAO,CAAA;AAAA,MAC7C,gBAAAA,EAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACC,OAAO;AAAA,YACL,WAAW,cAAc,MAAW,IAAY,MAA0B,CAAC;AAAA,UAAA;AAAA,QAC7E;AAAA,QAECyU,EAAQ,IAAI,CAACE,GAAKC,MACjB,gBAAA5U,EAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACC,KAAK4U;AAAA,YACL,GAAGA,IAAO;AAAA,YACV,GAAG,KAAY,IAAKD,IAAM,KAAiB;AAAA,YAC3C,OAAO;AAAA,YACP,QAAQA,IAAM;AAAA,UAAA;AAAA,QAEjB,CAAA;AAAA,MAAA;AAAA,IAEL;AAAA,EAAA;AAGN;ACpCO,SAASE,GAAgB,EAAE,cAAAC,GAAc,GAAG7U,KAA+B;AAChF,2DAEK6U,EAAa,IAAI,CAACnO,MACjB,gBAAA3G,EAAA,cAACoQ,GAAmB,UAAnB,EAA4B,OAAOzJ,GAAa,KAAKA,EAAY,SAC/D,GAAAgL,EAAiB1R,EAAM,QAAQ,CAClC,CACD,CACH;AAEJ;ACTO,SAAS8U,GAAkB,EAAE,QAAAhF,GAAQ,OAAAiF,KAAiC;AAC3E,QAAMvD,IAASwD;AAAA,IACb,CAAC7P,EAAM,OAAO,YAAYA,EAAM,OAAO,kBAAkBA,EAAM,OAAO,OAAO;AAAA,IAC7E;AAAA,MACE,cAAc,CAAC;AAAA,MACf,gBAAgB;AAAA,IAAA;AAAA,EAElB,EAAA,OAAO,CAAClF,MAAQ,CAACA,EAAI,YAAY,WAAWA,EAAI,YAAY,SAASkF,EAAM,KAAK,KAAK;AAGrF,SAAA,gBAAApF,EAAA,cAAC,OAAI,EAAA,OAAO,EAAE,SAAS,OACpB,EAAA,GAAAyR,EAAO,IAAI,CAACvP,MACX,gBAAAlC,EAAA;AAAA,IAAC8P;AAAA,IAAA;AAAA,MACC,KAAK4B,GAAoBxP,CAAQ;AAAA,MACjC,UAAAA;AAAA,MACA,QAAA6N;AAAA,MACA,OAAAiF;AAAA,IAAA;AAAA,EAAA,CAEH,CACH;AAEJ;AC/BO,MAAME,KACK,gBAAAlV,EAAM,WAA2C,SAC/D,EAAE,kBAAAmV,IAAmB,UAAU,UAAA3E,GAAU,GAAG4E,EAAe,GAC3DlV,GACA;AACM,QAAA,EAAE,MAAA+G,EAAK,IAAIoO,GAAY;AAG3B,SAAA,gBAAArV,EAAA,cAAC,QAAK,EAAA,KAAAE,GAAW,GAAGkV,EAAA,GACjBD,MAAqB,YAAY3E,GACjCvJ,GACAkO,MAAqB,WAAW3E,CACnC;AAEJ,CAAC;ACrBI,SAAS8E,GAAMrV,GAA6C;AACjE,QAAMuH,IAAYxH,EAAM,QAAQ,MAAMgE,EAAW/D,GAAO,EAAE,WAAW,WAAW,CAAC,GAAG,CAACA,CAAK,CAAC;AAC3F,SAAQ,gBAAAD,EAAA,cAAA,OAAA,EAAK,GAAGwH,EAAA,GAAYvH,EAAM,QAAS;AAC7C;ACjBa,MAAAsV,KAAgC,CAACC,MAAgC;AAC5E,QAAMC,IAAM,CAAC;AAEb,WAASC,IAAI,GAAGA,IAAIF,GAASE;AAC3B,IAAAD,EAAI,KAAK,CAACC,GAAGF,IAAU,IAAIE,CAAC,CAAC;AAGxB,SAAAD;AACT,GCRaE,IAA+B,CAACH,MAIpC,CAAC,CAHO,KAAK,MAAMA,IAAU,CAAC,CAGtB,GAAG,CAFF,EAEU,CAAC,GCChBI,KAAiB,CAC5B3H,GACAuH,GACAK,MACa;AACb,QAAM,CAACzR,GAAO0R,CAAQ,IAAIvI,EAAS,CAAC,GAC9B,CAACwI,GAAUC,CAAW,IAAIzI,EAAqB,CAAC,CAAE,CAAA,CAAC;AAEzD,EAAAf,EAAU,MAAM;AACd,QAAIyB,MAAU;AACA,MAAA+H,EAAAL,EAA6BH,CAAO,CAAC;AAAA,aACxCvH,MAAU,gBAAgBA,MAAU,gBAAgB;AAC7D,YAAM8H,IAAW,CAAC,GAAGR,GAA8BC,CAAO,CAAC;AAC3D,MAAAQ,EAAYD,CAAQ;AAAA,IAAA,MACtB,CACcC,EADH/H,MAAU,cACP0H,EAA6BH,CAAO,IACvCvH,MAAU,SACP,CAAC,IAAI,MAAMuH,CAAO,EAAE,KAAK,CAAC,EAAE,IAAI,CAACjD,GAAGqC,MAAQA,CAAG,CAAC,IAEhD,CAAC,CAAA,CAAE,CAJkC;AAMnD,IAAAkB,EAAS,CAAC;AAAA,EAAA,GACT,CAAC7H,GAAOuH,CAAO,CAAC;AAEb,QAAAS,IAAmB1J,EAAsB,IAAI;AACnD,SAAAC,EAAU,MAAM;AACV,QAAA0J,IAAY,YAAY,IAAI;AAE1B,UAAAC,IAAU,CAACnL,MAA8B;AAG7C,MAFoBA,IAAOkL,KAERL,MACRC,EAAA,CAACM,MAASA,IAAO,CAAC,GACfF,IAAAlL,IAGGiL,EAAA,UAAU,sBAAsBE,CAAO;AAAA,IAC1D;AAEiB,WAAAF,EAAA,UAAU,sBAAsBE,CAAO,GAEjD,MAAM;AACP,MAAAF,EAAiB,YAAY,QAC/B,qBAAqBA,EAAiB,OAAO;AAAA,IAEjD;AAAA,EAAA,GACC,CAACJ,GAAUL,GAASvH,GAAO8H,EAAS,MAAM,CAAC,GAEvCA,EAAS3R,IAAQ2R,EAAS,MAAM;AACzC,GCvBMM,yBAAyB,IAAwB;AAAA,EACrD,CAAC,cAAc,GAAI;AAAA,EACnB,CAAC,gBAAgB,GAAI;AAAA,EACrB,CAAC,aAAa,GAAG;AAAA,EACjB,CAAC,YAAY,GAAG;AAClB,CAAC,GAEKC,KAAuB,CAC3BrI,GACAsI,MACuB;AACvB,MAAItI,MAAU;AACL,WAAA;AAEL,MAAA4H,IAAWQ,GAAmB,IAAIpI,CAAK;AAC3C,MAAI4H;AACF,YAAQ5H,GAAO;AAAA,MACb,KAAK;AAES,QAAA4H,KAAAU;AACZ;AAAA,IAGA;AAGC,SAAAV;AACT,GAqBaW,KAAsC,gBAAAxW,EAAA;AAAA,EACjD,SACE,EAAE,OAAAiO,GAAO,SAAArG,GAAS,UAAA2O,IAAW,IAAI,UAAArU,GAAU,UAAAsO,GAAU,GAAGvQ,EAAM,GAC9DC,GACA;AACA,UAAMuG,IAAezC,EAAW/D,GAAO,EAAE,WAAW,2BAA2B;AAC/E,QAAIkP,IAAiB/M,EAAwB;AAE7C,IAAIF,MACeiN,IAAAjN;AAGb,UAAAuU,IAAc/B,GAAwBvF,GAAgB;AAAA,MAC1D,OAAOoH;AAAA,MACP,QAAQ;AAAA,MACR,QAAQ;AAAA,IAAA,CACT,GACKG,KAAY9O,KAAA,gBAAAA,EAAS,cAAa,IAClC+O,KAAY/O,KAAA,gBAAAA,EAAS,cAAa,KAElCgP,IAAqBhB;AAAA,MACzB3H;AAAA,MACAsI;AAAA,MACAD,GAAqBrI,GAAOsI,CAAQ,KAAK;AAAA,IAC3C;AAEA,2CACG,OAAI,EAAA,KAAArW,GAAW,GAAGuG,GAAc,oBAAkBwH,KAChDwI,EAAY;AAAA,MAAI,CAAC1G,GAAQ6E,MACxBpE,IACEmB,EAAiBnB,GAAU;AAAA,QACzB,uBAAuBoG,EAAmB,SAAShC,CAAG;AAAA,QACtD,qBAAqBA;AAAA,QACrB,WAAW;AAAA,QACX,OAAO,EAAE,QAAQ,GAAG,KAAK,IAAI+B,GAAW,KAAK,IAAID,GAAW3G,IAAS,MAAM,CAAC,CAAC,CAAC,IAAI;AAAA,MAAA,CACnF,IAED,gBAAA/P,EAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACC,KAAK4U;AAAA,UACL,uBAAqBgC,EAAmB,SAAShC,CAAG;AAAA,UACpD,qBAAmBA;AAAA,UACnB,WAAW,gBAAgBgC,EAAmB,SAAShC,CAAG,KAAK,gBAAgB;AAAA,UAC/E,OAAO;AAAA;AAAA;AAAA,YAGL,QAAQ,GAAG,KAAK,IAAI+B,GAAW,KAAK,IAAID,GAAW3G,IAAS,MAAM,CAAC,CAAC,CAAC;AAAA,UAAA;AAAA,QACvE;AAAA,MAAA;AAAA,IACD,CAGP;AAAA,EAAA;AAGN,GC7Ga8G,KAEgC,gBAAA7W,EAAA;AAAA,EAC3C,SACE;AAAA,IACE,UAAAwQ;AAAA,IACA,0BAAAE;AAAA,IACA,oBAAAD;AAAA,IACA,UAAAvO;AAAA,IACA,GAAGsF;AAAA,KAELtH,GACA;AACM,UAAAiP,IAAiBC,EAAkBlN,CAAQ,GAC3C,EAAE,cAAAuE,EAAa,IAAIkK,GAAmB;AAAA,MAC1C,UAAUxB;AAAA,MACV,WAAA3H;AAAA,MACA,0BAAAkJ;AAAA,MACA,oBAAAD;AAAA,IAAA,CACD;AAED,WACG,gBAAAzQ,EAAA,cAAA,OAAA,EAAI,KAAAE,GAAU,OAAO,EAAE,UAAU,YAAY,WAAW,QAAQ,GAAI,GAAGuG,KACrE,gBAAAzG,EAAA,cAAAsQ,EAAgB,UAAhB,EAAyB,OAAOnB,EAAA,GAC9BqB,KACC,gBAAAxQ,EAAA,cAAAA,EAAA,UAAA,MACGyI,EAAiB0G,CAAc,KAC9B,gBAAAnP,EAAA,cAAC8P,GAAW,EAAA,UAAUX,EAAgB,CAAA,mCAEvCqH,IAAc,EAAA,UAAU,GAAG,SAAS,EAAE,WAAW,IAAK,CAAA,GACvD,gBAAAxW,EAAA,cAAC,OAAI,EAAA,WAAU,0BACb,GAAA,gBAAAA,EAAA,cAAC,OAAI,EAAA,WAAU,+BACb,GAAA,gBAAAA,EAAA,cAACmH,IAAoB,EAAA,UAAUgI,EAAgB,CAAA,GAC9C,gBAAAnP,EAAA,cAAA0G,GAAA,IAAgB,CACnB,GACC,gBAAA1G,EAAA,cAAAuG,IAAA,EAA2B,WAAU,+BAA+B,CAAA,CACvE,CACF,CAEJ,CACF;AAAA,EAAA;AAGN;ACjDO,SAASuQ,GAAqB7W,GAAkC;AACrE,QAAM,CAAC8W,GAAcC,CAAe,IAAIhX,EAAM,SAAyC,MAAS,GAC1FiO,IAAQzN,EAAmBP,EAAM,IAAI;AAE3C,SAAAD,EAAM,UAAU,MAAM;AACpB,YAAQiO,GAAO;AAAA,MACb,KAAK5N,EAAgB;AACnB,QAAA2W;AAAA,4DAEK,gBAAAhX,EAAA,cAAAiX,GAAA,EAAY,WAAU,aAAA,CAAa,GAAE,eACxC;AAAA,QACF;AACA;AAAA,MACF,KAAK5W,EAAgB;AACnB,QAAA2W;AAAA,4DAEK,gBAAAhX,EAAA,cAAAiX,GAAA,EAAY,WAAU,aAAA,CAAa,GAAE,aACxC;AAAA,QACF;AACA;AAAA,MACF,KAAK5W,EAAgB;AACH,QAAA2W,EAAA,gBAAAhX,EAAA,cAAAA,EAAA,UAAA,MAAE,cAAY,CAAG;AACjC;AAAA,MACF;AACE,QAAAgX,EAAgB,MAAS;AACzB;AAAA,IAAA;AAAA,EACJ,GACC,CAAC/I,CAAK,CAAC,GACH8I,IAAgB,gBAAA/W,EAAA,cAAAsV,IAAA,EAAM,WAAU,4BAA6B,GAAAyB,CAAa,IAAa,gBAAA/W,EAAA,cAAAA,EAAA,UAAA,IAAA;AAChG;ACXO,MAAMkX,KAEgC,gBAAAlX,EAAA;AAAA,EAC3C,SACE,EAAE,OAAAyO,GAAO,UAAA0I,IAAW,IAAO,eAAAC,IAAgB,IAAO,kBAAAC,GAAkB,GAAGpX,EAAM,GAC7EC,GACA;;AACM,UAAAoX,IAAmBtX,EAAM,QAAQ,MAC9BqX,IAAmBA,EAAiB5I,EAAM,OAAO,IAAIA,EAAM,SACjE,CAACA,EAAM,SAAS4I,CAAgB,CAAC,GAC9BE,IAAgB,CAAC,CAAC9I,EAAM,eACxBzD,IAAO,IAAI,KAAKyD,EAAM,SAAS,GAC/B+I,IAAS,YAAY,UAAU,WAAW;AAG9C,WAAA,gBAAAxX,EAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACC,KAAAE;AAAA,QACA,WAAU;AAAA,QACV,OAAO8K,EAAK,mBAAmBwM,GAAQ,EAAE,WAAW,QAAQ;AAAA,QAC5D,2BAAwB5O,IAAA6F,EAAM,SAAN,QAAA7F,EAAY,UAAU,UAAU;AAAA,QACvD,GAAG3I;AAAA,MAAA;AAAA,OAEF,CAACmX,KAAiB,CAACD,KAAYI,sCAC9B,QAAK,EAAA,WAAU,eACb,GAAA,CAACJ,KACC,gBAAAnX,EAAA,cAAA,UAAA,EAAO,WAAU,sBACf,KAAA6I,IAAA4F,EAAM,SAAN,gBAAA5F,EAAY,WAAQ4O,IAAAhJ,EAAM,SAAN,gBAAAgJ,EAAY,SACnC,IAGA,CAACL,KAAiBG,MACjB,gBAAAvX,EAAA,cAAA,QAAA,EAAK,WAAU,eACb,GAAAuX,KAAiB,WACjBvM,EAAK,mBAAmBwM,GAAQ,EAAE,WAAW,QAAS,CAAA,CACzD,CAEJ;AAAA,MAGD,gBAAAxX,EAAA,cAAA,QAAA,EAAK,WAAU,kBAAA,GAAmBsX,CAAiB;AAAA,IACtD;AAAA,EAAA;AAGN;AAGO,SAASI,GAAuBC,GAAkC;AAChE,SAAAC,GAASD,GAASE,GAAqB,CAAC,EAAE,IAAI,CAACC,GAAKC,MAAM;AAC3D,QAAA,OAAOD,KAAQ;AACV,aAAAA;AACF;AACC,YAAAE,IAAUF,EAAI,QAAQ,SAAS,GAC/BG,IACJH,EAAI,SAAS,QACT,iBAAiB,KAAKE,CAAO,IAC3BA,IACA,WAAWA,CAAO,KACpB,UAAUA,CAAO;AAErB,aAAA,gBAAAhY,EAAA,cAAC,KAAE,EAAA,WAAU,gBAAe,KAAK+X,GAAG,MAAAE,GAAY,QAAO,UAAS,KAAI,aAAA,GACjED,CACH;AAAA,IAAA;AAAA,EAEJ,CACD;AACH;", "x_google_ignoreList": [34, 35]}