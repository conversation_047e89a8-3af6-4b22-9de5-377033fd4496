exports.id=310,exports.ids=[310],exports.modules={18768:()=>{},33207:(e,a,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},35159:(e,a,r)=>{Promise.resolve().then(r.bind(r,53996))},37264:()=>{},53996:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>n});var s=r(60687);r(43210);var t=r(85814),d=r.n(t),i=r(16189);let l=()=>{let e=(0,i.usePathname)(),a=a=>e===a||e?.startsWith(a+"/");return(0,s.jsxs)("div",{className:"h-full w-64 bg-white dark:bg-[#1A1A1A] border-r border-gray-200 dark:border-gray-800 flex flex-col",children:[(0,s.jsx)("div",{className:"p-4 border-b border-gray-200 dark:border-gray-800",children:(0,s.jsx)("h1",{className:"text-xl font-bold text-indigo-600 dark:text-indigo-400",children:"QuickCall"})}),(0,s.jsxs)("nav",{className:"flex-1 p-4 overflow-y-auto",children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("p",{className:"px-2 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-2",children:"Main"}),(0,s.jsxs)("ul",{className:"space-y-1",children:[(0,s.jsx)("li",{children:(0,s.jsxs)(d(),{href:"/dashboard",className:`flex items-center p-2 rounded-md ${a("/dashboard")&&!a("/dashboard/prompt")&&!a("/dashboard/agents")&&!a("/dashboard/history")&&!a("/dashboard/knowledge")&&!a("/dashboard/numbers")&&!a("/dashboard/settings")?"bg-indigo-50 dark:bg-indigo-900/20 text-indigo-600 dark:text-indigo-400":"text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"}`,children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-3",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{d:"M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"})}),(0,s.jsx)("span",{className:"text-sm font-medium",children:"Dashboard"})]})}),(0,s.jsx)("li",{children:(0,s.jsxs)(d(),{href:"/dashboard/agents",className:`flex items-center p-2 rounded-md ${a("/dashboard/agents")?"bg-indigo-50 dark:bg-indigo-900/20 text-indigo-600 dark:text-indigo-400":"text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"}`,children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-3",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{d:"M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z"})}),(0,s.jsx)("span",{className:"text-sm font-medium",children:"Agents"})]})}),(0,s.jsx)("li",{children:(0,s.jsxs)(d(),{href:"/dashboard/history",className:`flex items-center p-2 rounded-md ${a("/dashboard/history")?"bg-indigo-50 dark:bg-indigo-900/20 text-indigo-600 dark:text-indigo-400":"text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"}`,children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-3",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{d:"M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"})}),(0,s.jsx)("span",{className:"text-sm font-medium",children:"Call History"})]})}),(0,s.jsx)("li",{children:(0,s.jsxs)(d(),{href:"/dashboard/processes",className:`flex items-center p-2 rounded-md ${a("/dashboard/processes")?"bg-indigo-50 dark:bg-indigo-900/20 text-indigo-600 dark:text-indigo-400":"text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"}`,children:[(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-3",viewBox:"0 0 20 20",fill:"currentColor",children:[(0,s.jsx)("path",{d:"M13 7H7v6h6V7z"}),(0,s.jsx)("path",{fillRule:"evenodd",d:"M7 2a1 1 0 012 0v1h2V2a1 1 0 112 0v1h2a2 2 0 012 2v2h1a1 1 0 110 2h-1v2h1a1 1 0 110 2h-1v2a2 2 0 01-2 2h-2v1a1 1 0 11-2 0v-1H9v1a1 1 0 11-2 0v-1H5a2 2 0 01-2-2v-2H2a1 1 0 110-2h1V9H2a1 1 0 010-2h1V5a2 2 0 012-2h2V2zM5 5h10v10H5V5z",clipRule:"evenodd"})]}),(0,s.jsx)("span",{className:"text-sm font-medium",children:"Call Processes"})]})})]})]}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("p",{className:"px-2 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-2",children:"Resources"}),(0,s.jsxs)("ul",{className:"space-y-1",children:[(0,s.jsx)("li",{children:(0,s.jsxs)("div",{className:"flex items-center p-2 rounded-md text-gray-400 dark:text-gray-600 cursor-not-allowed",children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-3",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{d:"M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z"})}),(0,s.jsx)("span",{className:"text-sm font-medium",children:"Knowledge Base"}),(0,s.jsx)("span",{className:"ml-2 text-xs bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-400 px-1.5 py-0.5 rounded",children:"Coming Soon"})]})}),(0,s.jsx)("li",{children:(0,s.jsxs)("div",{className:"flex items-center p-2 rounded-md text-gray-400 dark:text-gray-600 cursor-not-allowed",children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-3",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{d:"M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"})}),(0,s.jsx)("span",{className:"text-sm font-medium",children:"Phone Numbers"}),(0,s.jsx)("span",{className:"ml-2 text-xs bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-400 px-1.5 py-0.5 rounded",children:"Coming Soon"})]})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"px-2 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-2",children:"Configuration"}),(0,s.jsx)("ul",{className:"space-y-1",children:(0,s.jsx)("li",{children:(0,s.jsxs)(d(),{href:"/dashboard/settings",className:`flex items-center p-2 rounded-md ${a("/dashboard/settings")?"bg-indigo-50 dark:bg-indigo-900/20 text-indigo-600 dark:text-indigo-400":"text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"}`,children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-3",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z",clipRule:"evenodd"})}),(0,s.jsx)("span",{className:"text-sm font-medium",children:"Settings"})]})})})]})]}),(0,s.jsx)("div",{className:"p-4 border-t border-gray-200 dark:border-gray-800",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"w-8 h-8 rounded-full bg-indigo-100 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400 flex items-center justify-center",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z",clipRule:"evenodd"})})}),(0,s.jsxs)("div",{className:"ml-3",children:[(0,s.jsx)("p",{className:"text-sm font-medium",children:"User Name"}),(0,s.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"<EMAIL>"})]})]})})]})};function n({children:e}){return(0,s.jsxs)("div",{className:"flex h-screen",children:[(0,s.jsx)(l,{}),(0,s.jsx)("main",{className:"flex-1 overflow-auto",children:e})]})}},61135:()=>{},63144:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\quickcall\\frontend\\quickcall-frontend\\src\\app\\dashboard\\layout.tsx","default")},70440:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>t});var s=r(31658);let t=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},86351:(e,a,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},94431:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>l,metadata:()=>i});var s=r(37413),t=r(25091),d=r.n(t);r(61135);let i={title:"QuickCall - Voice AI Calling Platform",description:"Make AI-powered calls with LiveKit and Groq"};function l({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:`${d().variable} font-sans antialiased bg-white dark:bg-[#0F0F0F] text-black dark:text-white`,children:e})})}},98207:(e,a,r)=>{Promise.resolve().then(r.bind(r,63144))}};