{"version": 3, "file": "index.mjs", "sources": ["../src/index.ts"], "sourcesContent": ["export class Mutex {\n  private _locking: Promise<void>;\n\n  private _locks: number;\n\n  constructor() {\n    this._locking = Promise.resolve();\n    this._locks = 0;\n  }\n\n  isLocked() {\n    return this._locks > 0;\n  }\n\n  lock() {\n    this._locks += 1;\n\n    let unlockNext: () => void;\n\n    const willLock = new Promise<void>(\n      (resolve) =>\n        (unlockNext = () => {\n          this._locks -= 1;\n          resolve();\n        }),\n    );\n\n    const willUnlock = this._locking.then(() => unlockNext);\n\n    this._locking = this._locking.then(() => willLock);\n\n    return willUnlock;\n  }\n}\n\nexport class MultiMutex {\n  private _queue: (() => void)[];\n  private _limit: number;\n  private _locks: number;\n\n  constructor(limit: number) {\n    this._queue = [];\n    this._limit = limit;\n    this._locks = 0;\n  }\n\n  isLocked() {\n    return this._locks >= this._limit;\n  }\n\n  async lock(): Promise<() => void> {\n    if (!this.isLocked()) {\n      this._locks++;\n      return this._unlock.bind(this);\n    }\n\n    return new Promise((resolve) => {\n      this._queue.push(() => {\n        this._locks++;\n        resolve(this._unlock.bind(this));\n      });\n    });\n  }\n\n  private _unlock() {\n    this._locks--;\n    if (this._queue.length && !this.isLocked()) {\n      const nextUnlock = this._queue.shift();\n      nextUnlock?.();\n    }\n  }\n}\n"], "names": ["Mutex", "__publicField", "unlockNext", "will<PERSON>ock", "resolve", "<PERSON><PERSON><PERSON><PERSON>", "MultiMutex", "limit", "nextUnlock"], "mappings": ";;;AAAO,MAAMA,EAAM;AAAA,EAKjB,cAAc;AAJN,IAAAC,EAAA;AAEA,IAAAA,EAAA;AAGD,SAAA,WAAW,QAAQ,WACxB,KAAK,SAAS;AAAA,EAChB;AAAA,EAEA,WAAW;AACT,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EAEA,OAAO;AACL,SAAK,UAAU;AAEX,QAAAC;AAEJ,UAAMC,IAAW,IAAI;AAAA,MACnB,CAACC,MACEF,IAAa,MAAM;AAClB,aAAK,UAAU,GACPE;MACV;AAAA,IAAA,GAGEC,IAAa,KAAK,SAAS,KAAK,MAAMH,CAAU;AAEtD,gBAAK,WAAW,KAAK,SAAS,KAAK,MAAMC,CAAQ,GAE1CE;AAAA,EACT;AACF;AAEO,MAAMC,EAAW;AAAA,EAKtB,YAAYC,GAAe;AAJnB,IAAAN,EAAA;AACA,IAAAA,EAAA;AACA,IAAAA,EAAA;AAGN,SAAK,SAAS,IACd,KAAK,SAASM,GACd,KAAK,SAAS;AAAA,EAChB;AAAA,EAEA,WAAW;AACF,WAAA,KAAK,UAAU,KAAK;AAAA,EAC7B;AAAA,EAEA,MAAM,OAA4B;AAC5B,WAAC,KAAK,aAKH,IAAI,QAAQ,CAACH,MAAY;AACzB,WAAA,OAAO,KAAK,MAAM;AAChB,aAAA,UACLA,EAAQ,KAAK,QAAQ,KAAK,IAAI,CAAC;AAAA,MAAA,CAChC;AAAA,IAAA,CACF,KATM,KAAA,UACE,KAAK,QAAQ,KAAK,IAAI;AAAA,EASjC;AAAA,EAEQ,UAAU;AAEhB,QADK,KAAA,UACD,KAAK,OAAO,UAAU,CAAC,KAAK,YAAY;AACpC,YAAAI,IAAa,KAAK,OAAO,MAAM;AACxB,MAAAA,KAAA,QAAAA;AAAA,IACf;AAAA,EACF;AACF;"}