# LiveKit Components **Core**

This package is a wrapper around the [livekit/client-sdk-js](https://github.com/livekit/client-sdk-js) package. It transforms the event based logic into simple to use observable state and component level APIs. This package is the core for all framework specific implementations.

> **Warning** this is a internal package and not intended to be used directly.

<!--NAV_START-->

## Monorepo Navigation

- [Home](../../README.md)
- **Framework Implementations**:
  - [React](../../packages/react/README.md)
- **Examples**
  - [Next.js](../../examples/nextjs/README.md)
- **Internal Packages**
  - [Core 👈](../../packages/core/README.md)
  - [Styles](../../packages/styles/README.md)

<!--NAV_END-->
