(()=>{var $;function U(C){return U=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(G){return typeof G}:function(G){return G&&typeof Symbol=="function"&&G.constructor===Symbol&&G!==Symbol.prototype?"symbol":typeof G},U(C)}function N(C,G){var H=Object.keys(C);if(Object.getOwnPropertySymbols){var J=Object.getOwnPropertySymbols(C);G&&(J=J.filter(function(Y){return Object.getOwnPropertyDescriptor(C,Y).enumerable})),H.push.apply(H,J)}return H}function Q(C){for(var G=1;G<arguments.length;G++){var H=arguments[G]!=null?arguments[G]:{};G%2?N(Object(H),!0).forEach(function(J){z(C,J,H[J])}):Object.getOwnPropertyDescriptors?Object.defineProperties(C,Object.getOwnPropertyDescriptors(H)):N(Object(H)).forEach(function(J){Object.defineProperty(C,J,Object.getOwnPropertyDescriptor(H,J))})}return C}function z(C,G,H){if(G=A(G),G in C)Object.defineProperty(C,G,{value:H,enumerable:!0,configurable:!0,writable:!0});else C[G]=H;return C}function A(C){var G=E(C,"string");return U(G)=="symbol"?G:String(G)}function E(C,G){if(U(C)!="object"||!C)return C;var H=C[Symbol.toPrimitive];if(H!==void 0){var J=H.call(C,G||"default");if(U(J)!="object")return J;throw new TypeError("@@toPrimitive must return a primitive value.")}return(G==="string"?String:Number)(C)}var W=Object.defineProperty,HC=function C(G,H){for(var J in H)W(G,J,{get:H[J],enumerable:!0,configurable:!0,set:function Y(X){return H[J]=function(){return X}}})},D={lessThanXSeconds:{one:"menos de um segundo",other:"menos de {{count}} segundos"},xSeconds:{one:"1 segundo",other:"{{count}} segundos"},halfAMinute:"meio minuto",lessThanXMinutes:{one:"menos de um minuto",other:"menos de {{count}} minutos"},xMinutes:{one:"1 minuto",other:"{{count}} minutos"},aboutXHours:{one:"aproximadamente 1 hora",other:"aproximadamente {{count}} horas"},xHours:{one:"1 hora",other:"{{count}} horas"},xDays:{one:"1 dia",other:"{{count}} dias"},aboutXWeeks:{one:"aproximadamente 1 semana",other:"aproximadamente {{count}} semanas"},xWeeks:{one:"1 semana",other:"{{count}} semanas"},aboutXMonths:{one:"aproximadamente 1 m\xEAs",other:"aproximadamente {{count}} meses"},xMonths:{one:"1 m\xEAs",other:"{{count}} meses"},aboutXYears:{one:"aproximadamente 1 ano",other:"aproximadamente {{count}} anos"},xYears:{one:"1 ano",other:"{{count}} anos"},overXYears:{one:"mais de 1 ano",other:"mais de {{count}} anos"},almostXYears:{one:"quase 1 ano",other:"quase {{count}} anos"}},S=function C(G,H,J){var Y,X=D[G];if(typeof X==="string")Y=X;else if(H===1)Y=X.one;else Y=X.other.replace("{{count}}",String(H));if(J!==null&&J!==void 0&&J.addSuffix)if(J.comparison&&J.comparison>0)return"daqui a "+Y;else return"h\xE1 "+Y;return Y};function K(C){return function(){var G=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},H=G.width?String(G.width):C.defaultWidth,J=C.formats[H]||C.formats[C.defaultWidth];return J}}var M={full:"EEEE, d 'de' MMMM 'de' y",long:"d 'de' MMMM 'de' y",medium:"d 'de' MMM 'de' y",short:"dd/MM/y"},R={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},L={full:"{{date}} '\xE0s' {{time}}",long:"{{date}} '\xE0s' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},V={date:K({formats:M,defaultWidth:"full"}),time:K({formats:R,defaultWidth:"full"}),dateTime:K({formats:L,defaultWidth:"full"})},j={lastWeek:function C(G){var H=G.getDay(),J=H===0||H===6?"\xFAltimo":"\xFAltima";return"'"+J+"' eeee '\xE0s' p"},yesterday:"'ontem \xE0s' p",today:"'hoje \xE0s' p",tomorrow:"'amanh\xE3 \xE0s' p",nextWeek:"eeee '\xE0s' p",other:"P"},w=function C(G,H,J,Y){var X=j[G];if(typeof X==="function")return X(H);return X};function I(C){return function(G,H){var J=H!==null&&H!==void 0&&H.context?String(H.context):"standalone",Y;if(J==="formatting"&&C.formattingValues){var X=C.defaultFormattingWidth||C.defaultWidth,Z=H!==null&&H!==void 0&&H.width?String(H.width):X;Y=C.formattingValues[Z]||C.formattingValues[X]}else{var B=C.defaultWidth,q=H!==null&&H!==void 0&&H.width?String(H.width):C.defaultWidth;Y=C.values[q]||C.values[B]}var T=C.argumentCallback?C.argumentCallback(G):G;return Y[T]}}var _={narrow:["aC","dC"],abbreviated:["a.C.","d.C."],wide:["antes de Cristo","depois de Cristo"]},F={narrow:["1","2","3","4"],abbreviated:["T1","T2","T3","T4"],wide:["1\xBA trimestre","2\xBA trimestre","3\xBA trimestre","4\xBA trimestre"]},f={narrow:["j","f","m","a","m","j","j","a","s","o","n","d"],abbreviated:["jan","fev","mar","abr","mai","jun","jul","ago","set","out","nov","dez"],wide:["janeiro","fevereiro","mar\xE7o","abril","maio","junho","julho","agosto","setembro","outubro","novembro","dezembro"]},v={narrow:["d","s","t","q","q","s","s"],short:["dom","seg","ter","qua","qui","sex","s\xE1b"],abbreviated:["dom","seg","ter","qua","qui","sex","s\xE1b"],wide:["domingo","segunda-feira","ter\xE7a-feira","quarta-feira","quinta-feira","sexta-feira","s\xE1bado"]},P={narrow:{am:"AM",pm:"PM",midnight:"meia-noite",noon:"meio-dia",morning:"manh\xE3",afternoon:"tarde",evening:"noite",night:"madrugada"},abbreviated:{am:"AM",pm:"PM",midnight:"meia-noite",noon:"meio-dia",morning:"manh\xE3",afternoon:"tarde",evening:"noite",night:"madrugada"},wide:{am:"AM",pm:"PM",midnight:"meia-noite",noon:"meio-dia",morning:"manh\xE3",afternoon:"tarde",evening:"noite",night:"madrugada"}},k={narrow:{am:"AM",pm:"PM",midnight:"meia-noite",noon:"meio-dia",morning:"da manh\xE3",afternoon:"da tarde",evening:"da noite",night:"da madrugada"},abbreviated:{am:"AM",pm:"PM",midnight:"meia-noite",noon:"meio-dia",morning:"da manh\xE3",afternoon:"da tarde",evening:"da noite",night:"da madrugada"},wide:{am:"AM",pm:"PM",midnight:"meia-noite",noon:"meio-dia",morning:"da manh\xE3",afternoon:"da tarde",evening:"da noite",night:"da madrugada"}},b=function C(G,H){var J=Number(G);return J+"\xBA"},h={ordinalNumber:b,era:I({values:_,defaultWidth:"wide"}),quarter:I({values:F,defaultWidth:"wide",argumentCallback:function C(G){return G-1}}),month:I({values:f,defaultWidth:"wide"}),day:I({values:v,defaultWidth:"wide"}),dayPeriod:I({values:P,defaultWidth:"wide",formattingValues:k,defaultFormattingWidth:"wide"})};function O(C){return function(G){var H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},J=H.width,Y=J&&C.matchPatterns[J]||C.matchPatterns[C.defaultMatchWidth],X=G.match(Y);if(!X)return null;var Z=X[0],B=J&&C.parsePatterns[J]||C.parsePatterns[C.defaultParseWidth],q=Array.isArray(B)?c(B,function(x){return x.test(Z)}):m(B,function(x){return x.test(Z)}),T;T=C.valueCallback?C.valueCallback(q):q,T=H.valueCallback?H.valueCallback(T):T;var GC=G.slice(Z.length);return{value:T,rest:GC}}}function m(C,G){for(var H in C)if(Object.prototype.hasOwnProperty.call(C,H)&&G(C[H]))return H;return}function c(C,G){for(var H=0;H<C.length;H++)if(G(C[H]))return H;return}function y(C){return function(G){var H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},J=G.match(C.matchPattern);if(!J)return null;var Y=J[0],X=G.match(C.parsePattern);if(!X)return null;var Z=C.valueCallback?C.valueCallback(X[0]):X[0];Z=H.valueCallback?H.valueCallback(Z):Z;var B=G.slice(Y.length);return{value:Z,rest:B}}}var g=/^(\d+)(º|ª)?/i,p=/\d+/i,d={narrow:/^(ac|dc|a|d)/i,abbreviated:/^(a\.?\s?c\.?|a\.?\s?e\.?\s?c\.?|d\.?\s?c\.?|e\.?\s?c\.?)/i,wide:/^(antes de cristo|antes da era comum|depois de cristo|era comum)/i},u={any:[/^ac/i,/^dc/i],wide:[/^(antes de cristo|antes da era comum)/i,/^(depois de cristo|era comum)/i]},l={narrow:/^[1234]/i,abbreviated:/^T[1234]/i,wide:/^[1234](º|ª)? trimestre/i},i={any:[/1/i,/2/i,/3/i,/4/i]},n={narrow:/^[jfmasond]/i,abbreviated:/^(jan|fev|mar|abr|mai|jun|jul|ago|set|out|nov|dez)/i,wide:/^(janeiro|fevereiro|março|abril|maio|junho|julho|agosto|setembro|outubro|novembro|dezembro)/i},s={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ab/i,/^mai/i,/^jun/i,/^jul/i,/^ag/i,/^s/i,/^o/i,/^n/i,/^d/i]},o={narrow:/^[dstq]/i,short:/^(dom|seg|ter|qua|qui|sex|s[áa]b)/i,abbreviated:/^(dom|seg|ter|qua|qui|sex|s[áa]b)/i,wide:/^(domingo|segunda-?\s?feira|terça-?\s?feira|quarta-?\s?feira|quinta-?\s?feira|sexta-?\s?feira|s[áa]bado)/i},r={narrow:[/^d/i,/^s/i,/^t/i,/^q/i,/^q/i,/^s/i,/^s/i],any:[/^d/i,/^seg/i,/^t/i,/^qua/i,/^qui/i,/^sex/i,/^s[áa]/i]},e={narrow:/^(a|p|meia-?\s?noite|meio-?\s?dia|(da) (manh[ãa]|tarde|noite|madrugada))/i,any:/^([ap]\.?\s?m\.?|meia-?\s?noite|meio-?\s?dia|(da) (manh[ãa]|tarde|noite|madrugada))/i},a={any:{am:/^a/i,pm:/^p/i,midnight:/^meia/i,noon:/^meio/i,morning:/manh[ãa]/i,afternoon:/tarde/i,evening:/noite/i,night:/madrugada/i}},t={ordinalNumber:y({matchPattern:g,parsePattern:p,valueCallback:function C(G){return parseInt(G,10)}}),era:O({matchPatterns:d,defaultMatchWidth:"wide",parsePatterns:u,defaultParseWidth:"any"}),quarter:O({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any",valueCallback:function C(G){return G+1}}),month:O({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),day:O({matchPatterns:o,defaultMatchWidth:"wide",parsePatterns:r,defaultParseWidth:"any"}),dayPeriod:O({matchPatterns:e,defaultMatchWidth:"any",parsePatterns:a,defaultParseWidth:"any"})},CC={code:"pt",formatDistance:S,formatLong:V,formatRelative:w,localize:h,match:t,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=Q(Q({},window.dateFns),{},{locale:Q(Q({},($=window.dateFns)===null||$===void 0?void 0:$.locale),{},{pt:CC})})})();

//# debugId=BE724D9A6EA40E2264756E2164756E21
