"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Voice, Model } from "@/hooks/useVoiceAssistants";

interface AssistantFormProps {
  assistantName: string;
  systemPrompt: string;
  selectedVoice: string;
  selectedModel: string;
  voices: Voice[];
  models: Model[];
  loading: boolean;
  onNameChange: (name: string) => void;
  onPromptChange: (prompt: string) => void;
  onVoiceChange: (voice: string) => void;
  onModelChange: (model: string) => void;
  onSubmit: () => void;
}

const AssistantForm: React.FC<AssistantFormProps> = ({
  assistant<PERSON><PERSON>,
  systemPrompt,
  selectedVoice,
  selectedModel,
  voices,
  models,
  loading,
  onNameChange,
  onPromptChange,
  onVoiceChange,
  onModelChange,
  onSubmit,
}) => {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit();
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>
          Create New Assistant
        </CardTitle>
        <CardDescription>
          Configure your voice assistant with custom settings
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">
              Assistant Name
            </Label>
            <Input
              id="name"
              type="text"
              placeholder="Enter assistant name"
              value={assistantName}
              onChange={(e) => onNameChange(e.target.value)}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="prompt">
              System Prompt
            </Label>
            <Textarea
              id="prompt"
              placeholder="Enter the system prompt for your assistant..."
              value={systemPrompt}
              onChange={(e) => onPromptChange(e.target.value)}
              className="min-h-[100px]"
              required
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="voice">
                Voice
              </Label>
              <Select value={selectedVoice} onValueChange={onVoiceChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a voice" />
                </SelectTrigger>
                <SelectContent>
                  {voices.map((voice) => (
                    <SelectItem key={voice.id} value={voice.id}>
                      {voice.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="model">
                Model
              </Label>
              <Select value={selectedModel} onValueChange={onModelChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a model" />
                </SelectTrigger>
                <SelectContent>
                  {models.map((model) => (
                    <SelectItem key={model.id} value={model.id}>
                      {model.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <Button 
            type="submit" 
            disabled={loading || !assistantName || !systemPrompt}
            className="w-full"
          >
            {loading ? "Creating..." : "Create Assistant"}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};

export default AssistantForm;
