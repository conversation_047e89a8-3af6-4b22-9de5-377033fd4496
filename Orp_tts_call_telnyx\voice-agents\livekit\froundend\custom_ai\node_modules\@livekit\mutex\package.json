{"name": "@livekit/mutex", "private": false, "access": "public", "license": "Apache-2.0", "version": "1.1.1", "description": "Tiny mutex helper", "repository": "**************:livekit/ts-mutex.git", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "main": "dist/index.js", "module": "dist/index.mjs", "typings": "dist/index.d.ts", "sideEffects": false, "files": ["dist", "src"], "devDependencies": {"@edge-runtime/vm": "^4.0.3", "@eslint/js": "^9.12.0", "eslint": "^9.12.0", "eslint-config-prettier": "^9.1.0", "globals": "^15.11.0", "happy-dom": "^15.7.4", "prettier": "^3.3.3", "typescript": "~5.0.0", "typescript-eslint": "^8.9.0", "vite": "^5.4.8", "vite-plugin-dts": "^4.2.4", "vitest": "^2.1.3"}, "scripts": {"build": "vite build", "lint": "eslint src", "format:check": "prettier --check .", "test": "vitest --environment node run", "test:browser": "vitest --environment happy-dom run", "test:edge": "vitest --environment edge-runtime run"}}