(()=>{var I;function z(B,C){var G=Object.keys(B);if(Object.getOwnPropertySymbols){var J=Object.getOwnPropertySymbols(B);C&&(J=J.filter(function(X){return Object.getOwnPropertyDescriptor(B,X).enumerable})),G.push.apply(G,J)}return G}function A(B){for(var C=1;C<arguments.length;C++){var G=arguments[C]!=null?arguments[C]:{};C%2?z(Object(G),!0).forEach(function(J){P(B,J,G[J])}):Object.getOwnPropertyDescriptors?Object.defineProperties(B,Object.getOwnPropertyDescriptors(G)):z(Object(G)).forEach(function(J){Object.defineProperty(B,J,Object.getOwnPropertyDescriptor(G,J))})}return B}function P(B,C,G){if(C=F(C),C in B)Object.defineProperty(B,C,{value:G,enumerable:!0,configurable:!0,writable:!0});else B[C]=G;return B}function F(B){var C=D(B,"string");return E(C)=="symbol"?C:String(C)}function D(B,C){if(E(B)!="object"||!B)return B;var G=B[Symbol.toPrimitive];if(G!==void 0){var J=G.call(B,C||"default");if(E(J)!="object")return J;throw new TypeError("@@toPrimitive must return a primitive value.")}return(C==="string"?String:Number)(B)}function w(B,C){return f(B)||b(B,C)||h(B,C)||v()}function v(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function h(B,C){if(!B)return;if(typeof B==="string")return R(B,C);var G=Object.prototype.toString.call(B).slice(8,-1);if(G==="Object"&&B.constructor)G=B.constructor.name;if(G==="Map"||G==="Set")return Array.from(B);if(G==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(G))return R(B,C)}function R(B,C){if(C==null||C>B.length)C=B.length;for(var G=0,J=new Array(C);G<C;G++)J[G]=B[G];return J}function b(B,C){var G=B==null?null:typeof Symbol!="undefined"&&B[Symbol.iterator]||B["@@iterator"];if(G!=null){var J,X,Z,U,H=[],Y=!0,Q=!1;try{if(Z=(G=G.call(B)).next,C===0){if(Object(G)!==G)return;Y=!1}else for(;!(Y=(J=Z.call(G)).done)&&(H.push(J.value),H.length!==C);Y=!0);}catch(q){Q=!0,X=q}finally{try{if(!Y&&G.return!=null&&(U=G.return(),Object(U)!==U))return}finally{if(Q)throw X}}return H}}function f(B){if(Array.isArray(B))return B}function E(B){return E=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(C){return typeof C}:function(C){return C&&typeof Symbol=="function"&&C.constructor===Symbol&&C!==Symbol.prototype?"symbol":typeof C},E(B)}var k=Object.defineProperty,OB=function B(C,G){for(var J in G)k(C,J,{get:G[J],enumerable:!0,configurable:!0,set:function X(Z){return G[J]=function(){return Z}}})},m={lessThanXSeconds:{one:"meno di un secondo",other:"meno di {{count}} secondi"},xSeconds:{one:"un secondo",other:"{{count}} secondi"},halfAMinute:"alcuni secondi",lessThanXMinutes:{one:"meno di un minuto",other:"meno di {{count}} minuti"},xMinutes:{one:"un minuto",other:"{{count}} minuti"},aboutXHours:{one:"circa un'ora",other:"circa {{count}} ore"},xHours:{one:"un'ora",other:"{{count}} ore"},xDays:{one:"un giorno",other:"{{count}} giorni"},aboutXWeeks:{one:"circa una settimana",other:"circa {{count}} settimane"},xWeeks:{one:"una settimana",other:"{{count}} settimane"},aboutXMonths:{one:"circa un mese",other:"circa {{count}} mesi"},xMonths:{one:"un mese",other:"{{count}} mesi"},aboutXYears:{one:"circa un anno",other:"circa {{count}} anni"},xYears:{one:"un anno",other:"{{count}} anni"},overXYears:{one:"pi\xF9 di un anno",other:"pi\xF9 di {{count}} anni"},almostXYears:{one:"quasi un anno",other:"quasi {{count}} anni"}},_=function B(C,G,J){var X,Z=m[C];if(typeof Z==="string")X=Z;else if(G===1)X=Z.one;else X=Z.other.replace("{{count}}",G.toString());if(J!==null&&J!==void 0&&J.addSuffix)if(J.comparison&&J.comparison>0)return"tra "+X;else return X+" fa";return X};function M(B){return function(){var C=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},G=C.width?String(C.width):B.defaultWidth,J=B.formats[G]||B.formats[B.defaultWidth];return J}}var g={full:"EEEE d MMMM y",long:"d MMMM y",medium:"d MMM y",short:"dd/MM/y"},c={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},y={full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},u={date:M({formats:g,defaultWidth:"full"}),time:M({formats:c,defaultWidth:"full"}),dateTime:M({formats:y,defaultWidth:"full"})},PB=7,p=365.2425,d=Math.pow(10,8)*24*60*60*1000,FB=-d,DB=604800000,wB=86400000,vB=60000,hB=3600000,bB=1000,fB=525600,kB=43200,mB=1440,_B=60,gB=3,cB=12,yB=4,l=3600,uB=60,V=l*24,pB=V*7,i=V*p,n=i/12,dB=n*3,W=Symbol.for("constructDateFrom");function S(B,C){if(typeof B==="function")return B(C);if(B&&E(B)==="object"&&W in B)return B[W](C);if(B instanceof Date)return new B.constructor(C);return new Date(C)}function s(B){for(var C=arguments.length,G=new Array(C>1?C-1:0),J=1;J<C;J++)G[J-1]=arguments[J];var X=S.bind(null,B||G.find(function(Z){return E(Z)==="object"}));return G.map(X)}function r(){return $}function lB(B){$=B}var $={};function o(B,C){return S(C||B,B)}function j(B,C){var G,J,X,Z,U,H,Y=r(),Q=(G=(J=(X=(Z=C===null||C===void 0?void 0:C.weekStartsOn)!==null&&Z!==void 0?Z:C===null||C===void 0||(U=C.locale)===null||U===void 0||(U=U.options)===null||U===void 0?void 0:U.weekStartsOn)!==null&&X!==void 0?X:Y.weekStartsOn)!==null&&J!==void 0?J:(H=Y.locale)===null||H===void 0||(H=H.options)===null||H===void 0?void 0:H.weekStartsOn)!==null&&G!==void 0?G:0,q=o(B,C===null||C===void 0?void 0:C.in),K=q.getDay(),LB=(K<Q?7:0)+K-Q;return q.setDate(q.getDate()-LB),q.setHours(0,0,0,0),q}function L(B,C,G){var J=s(G===null||G===void 0?void 0:G.in,B,C),X=w(J,2),Z=X[0],U=X[1];return+j(Z,G)===+j(U,G)}function a(B){switch(B){case 0:return"'domenica scorsa alle' p";default:return"'"+x[B]+" scorso alle' p"}}function O(B){return"'"+x[B]+" alle' p"}function e(B){switch(B){case 0:return"'domenica prossima alle' p";default:return"'"+x[B]+" prossimo alle' p"}}var x=["domenica","luned\xEC","marted\xEC","mercoled\xEC","gioved\xEC","venerd\xEC","sabato"],t={lastWeek:function B(C,G,J){var X=C.getDay();if(L(C,G,J))return O(X);else return a(X)},yesterday:"'ieri alle' p",today:"'oggi alle' p",tomorrow:"'domani alle' p",nextWeek:function B(C,G,J){var X=C.getDay();if(L(C,G,J))return O(X);else return e(X)},other:"P"},BB=function B(C,G,J,X){var Z=t[C];if(typeof Z==="function")return Z(G,J,X);return Z};function N(B){return function(C,G){var J=G!==null&&G!==void 0&&G.context?String(G.context):"standalone",X;if(J==="formatting"&&B.formattingValues){var Z=B.defaultFormattingWidth||B.defaultWidth,U=G!==null&&G!==void 0&&G.width?String(G.width):Z;X=B.formattingValues[U]||B.formattingValues[Z]}else{var H=B.defaultWidth,Y=G!==null&&G!==void 0&&G.width?String(G.width):B.defaultWidth;X=B.values[Y]||B.values[H]}var Q=B.argumentCallback?B.argumentCallback(C):C;return X[Q]}}var CB={narrow:["aC","dC"],abbreviated:["a.C.","d.C."],wide:["avanti Cristo","dopo Cristo"]},GB={narrow:["1","2","3","4"],abbreviated:["T1","T2","T3","T4"],wide:["1\xBA trimestre","2\xBA trimestre","3\xBA trimestre","4\xBA trimestre"]},JB={narrow:["G","F","M","A","M","G","L","A","S","O","N","D"],abbreviated:["gen","feb","mar","apr","mag","giu","lug","ago","set","ott","nov","dic"],wide:["gennaio","febbraio","marzo","aprile","maggio","giugno","luglio","agosto","settembre","ottobre","novembre","dicembre"]},XB={narrow:["D","L","M","M","G","V","S"],short:["dom","lun","mar","mer","gio","ven","sab"],abbreviated:["dom","lun","mar","mer","gio","ven","sab"],wide:["domenica","luned\xEC","marted\xEC","mercoled\xEC","gioved\xEC","venerd\xEC","sabato"]},ZB={narrow:{am:"m.",pm:"p.",midnight:"mezzanotte",noon:"mezzogiorno",morning:"mattina",afternoon:"pomeriggio",evening:"sera",night:"notte"},abbreviated:{am:"AM",pm:"PM",midnight:"mezzanotte",noon:"mezzogiorno",morning:"mattina",afternoon:"pomeriggio",evening:"sera",night:"notte"},wide:{am:"AM",pm:"PM",midnight:"mezzanotte",noon:"mezzogiorno",morning:"mattina",afternoon:"pomeriggio",evening:"sera",night:"notte"}},UB={narrow:{am:"m.",pm:"p.",midnight:"mezzanotte",noon:"mezzogiorno",morning:"di mattina",afternoon:"del pomeriggio",evening:"di sera",night:"di notte"},abbreviated:{am:"AM",pm:"PM",midnight:"mezzanotte",noon:"mezzogiorno",morning:"di mattina",afternoon:"del pomeriggio",evening:"di sera",night:"di notte"},wide:{am:"AM",pm:"PM",midnight:"mezzanotte",noon:"mezzogiorno",morning:"di mattina",afternoon:"del pomeriggio",evening:"di sera",night:"di notte"}},HB=function B(C,G){var J=Number(C);return String(J)},QB={ordinalNumber:HB,era:N({values:CB,defaultWidth:"wide"}),quarter:N({values:GB,defaultWidth:"wide",argumentCallback:function B(C){return C-1}}),month:N({values:JB,defaultWidth:"wide"}),day:N({values:XB,defaultWidth:"wide"}),dayPeriod:N({values:ZB,defaultWidth:"wide",formattingValues:UB,defaultFormattingWidth:"wide"})};function T(B){return function(C){var G=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},J=G.width,X=J&&B.matchPatterns[J]||B.matchPatterns[B.defaultMatchWidth],Z=C.match(X);if(!Z)return null;var U=Z[0],H=J&&B.parsePatterns[J]||B.parsePatterns[B.defaultParseWidth],Y=Array.isArray(H)?qB(H,function(K){return K.test(U)}):YB(H,function(K){return K.test(U)}),Q;Q=B.valueCallback?B.valueCallback(Y):Y,Q=G.valueCallback?G.valueCallback(Q):Q;var q=C.slice(U.length);return{value:Q,rest:q}}}function YB(B,C){for(var G in B)if(Object.prototype.hasOwnProperty.call(B,G)&&C(B[G]))return G;return}function qB(B,C){for(var G=0;G<B.length;G++)if(C(B[G]))return G;return}function EB(B){return function(C){var G=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},J=C.match(B.matchPattern);if(!J)return null;var X=J[0],Z=C.match(B.parsePattern);if(!Z)return null;var U=B.valueCallback?B.valueCallback(Z[0]):Z[0];U=G.valueCallback?G.valueCallback(U):U;var H=C.slice(X.length);return{value:U,rest:H}}}var KB=/^(\d+)(º)?/i,NB=/\d+/i,TB={narrow:/^(aC|dC)/i,abbreviated:/^(a\.?\s?C\.?|a\.?\s?e\.?\s?v\.?|d\.?\s?C\.?|e\.?\s?v\.?)/i,wide:/^(avanti Cristo|avanti Era Volgare|dopo Cristo|Era Volgare)/i},AB={any:[/^a/i,/^(d|e)/i]},IB={narrow:/^[1234]/i,abbreviated:/^t[1234]/i,wide:/^[1234](º)? trimestre/i},MB={any:[/1/i,/2/i,/3/i,/4/i]},xB={narrow:/^[gfmalsond]/i,abbreviated:/^(gen|feb|mar|apr|mag|giu|lug|ago|set|ott|nov|dic)/i,wide:/^(gennaio|febbraio|marzo|aprile|maggio|giugno|luglio|agosto|settembre|ottobre|novembre|dicembre)/i},zB={narrow:[/^g/i,/^f/i,/^m/i,/^a/i,/^m/i,/^g/i,/^l/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ge/i,/^f/i,/^mar/i,/^ap/i,/^mag/i,/^gi/i,/^l/i,/^ag/i,/^s/i,/^o/i,/^n/i,/^d/i]},RB={narrow:/^[dlmgvs]/i,short:/^(do|lu|ma|me|gi|ve|sa)/i,abbreviated:/^(dom|lun|mar|mer|gio|ven|sab)/i,wide:/^(domenica|luned[i|ì]|marted[i|ì]|mercoled[i|ì]|gioved[i|ì]|venerd[i|ì]|sabato)/i},VB={narrow:[/^d/i,/^l/i,/^m/i,/^m/i,/^g/i,/^v/i,/^s/i],any:[/^d/i,/^l/i,/^ma/i,/^me/i,/^g/i,/^v/i,/^s/i]},WB={narrow:/^(a|m\.|p|mezzanotte|mezzogiorno|(di|del) (mattina|pomeriggio|sera|notte))/i,any:/^([ap]\.?\s?m\.?|mezzanotte|mezzogiorno|(di|del) (mattina|pomeriggio|sera|notte))/i},SB={any:{am:/^a/i,pm:/^p/i,midnight:/^mezza/i,noon:/^mezzo/i,morning:/mattina/i,afternoon:/pomeriggio/i,evening:/sera/i,night:/notte/i}},$B={ordinalNumber:EB({matchPattern:KB,parsePattern:NB,valueCallback:function B(C){return parseInt(C,10)}}),era:T({matchPatterns:TB,defaultMatchWidth:"wide",parsePatterns:AB,defaultParseWidth:"any"}),quarter:T({matchPatterns:IB,defaultMatchWidth:"wide",parsePatterns:MB,defaultParseWidth:"any",valueCallback:function B(C){return C+1}}),month:T({matchPatterns:xB,defaultMatchWidth:"wide",parsePatterns:zB,defaultParseWidth:"any"}),day:T({matchPatterns:RB,defaultMatchWidth:"wide",parsePatterns:VB,defaultParseWidth:"any"}),dayPeriod:T({matchPatterns:WB,defaultMatchWidth:"any",parsePatterns:SB,defaultParseWidth:"any"})},jB={code:"it",formatDistance:_,formatLong:u,formatRelative:BB,localize:QB,match:$B,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=A(A({},window.dateFns),{},{locale:A(A({},(I=window.dateFns)===null||I===void 0?void 0:I.locale),{},{it:jB})})})();

//# debugId=43EBC4D66D160DAB64756E2164756E21
