{"version": 3, "sources": ["../src/constants.ts", "../src/track-reference/track-reference.types.ts", "../src/track-reference/track-reference.utils.ts", "../src/utils.ts", "../src/helper/detectMobileBrowser.ts", "../src/helper/url-regex.ts", "../src/helper/emailRegex.ts", "../src/helper/floating-menu.ts", "../src/helper/tokenizer.ts", "../src/helper/eventGroups.ts", "../src/logger.ts", "../src/helper/grid-layouts.ts", "../src/helper/set-helper.ts", "../src/helper/featureDetection.ts", "../src/helper/transcriptions.ts", "../src/types.ts", "../src/sorting/sort-track-bundles.ts", "../src/sorting/base-sort-functions.ts", "../src/sorting/sort-participants.ts", "../src/helper/array-helper.ts", "../src/track-reference/test-utils.ts", "../src/sorting/tile-array-update.ts", "../src/components/mediaToggle.ts", "../src/observables/participant.ts", "../src/components/mediaTrack.ts", "../src/styles-interface/class-prefixer.ts", "../src/observables/room.ts", "../src/components/mediaDeviceSelect.ts", "../src/components/disconnectButton.ts", "../src/components/connectionQualityIndicator.ts", "../src/components/trackMutedIndicator.ts", "../src/components/participantName.ts", "../src/components/participantTile.ts", "../src/components/chat.ts", "../src/observables/dataChannel.ts", "../src/components/startAudio.ts", "../src/components/startVideo.ts", "../src/components/chatToggle.ts", "../src/components/focusToggle.ts", "../src/components/clearPinButton.ts", "../src/components/room.ts", "../src/observables/track.ts", "../src/observables/dom-event.ts", "../src/persistent-storage/local-storage-helpers.ts", "../src/persistent-storage/user-choices.ts"], "sourcesContent": ["export const cssPrefix = 'lk';\n", "/**\n * The TrackReference type is a logical grouping of participant publication and/or subscribed track.\n *\n */\n\nimport type { Participant, Track, TrackPublication } from 'livekit-client';\n// ## TrackReference Types\n\n/** @public */\nexport type TrackReferencePlaceholder = {\n  participant: Participant;\n  publication?: never;\n  source: Track.Source;\n};\n\n/** @public */\nexport type TrackReference = {\n  participant: Participant;\n  publication: TrackPublication;\n  source: Track.Source;\n};\n\n/** @public */\nexport type TrackReferenceOrPlaceholder = TrackReference | TrackReferencePlaceholder;\n\n// ### TrackReference Type Predicates\n/** @internal */\nexport function isTrackReference(trackReference: unknown): trackReference is TrackReference {\n  if (typeof trackReference === 'undefined') {\n    return false;\n  }\n  return (\n    isTrackReferenceSubscribed(trackReference as TrackReference) ||\n    isTrackReferencePublished(trackReference as TrackReference)\n  );\n}\n\nfunction isTrackReferenceSubscribed(trackReference?: TrackReferenceOrPlaceholder): boolean {\n  if (!trackReference) {\n    return false;\n  }\n  return (\n    trackReference.hasOwnProperty('participant') &&\n    trackReference.hasOwnProperty('source') &&\n    trackReference.hasOwnProperty('track') &&\n    typeof trackReference.publication?.track !== 'undefined'\n  );\n}\n\nfunction isTrackReferencePublished(trackReference?: TrackReferenceOrPlaceholder): boolean {\n  if (!trackReference) {\n    return false;\n  }\n  return (\n    trackReference.hasOwnProperty('participant') &&\n    trackReference.hasOwnProperty('source') &&\n    trackReference.hasOwnProperty('publication') &&\n    typeof trackReference.publication !== 'undefined'\n  );\n}\n\nexport function isTrackReferencePlaceholder(\n  trackReference?: TrackReferenceOrPlaceholder,\n): trackReference is TrackReferencePlaceholder {\n  if (!trackReference) {\n    return false;\n  }\n  return (\n    trackReference.hasOwnProperty('participant') &&\n    trackReference.hasOwnProperty('source') &&\n    typeof trackReference.publication === 'undefined'\n  );\n}\n", "import type { Track } from 'livekit-client';\nimport type { PinState } from '../types';\nimport type { TrackReferenceOrPlaceholder } from './track-reference.types';\nimport { isTrackReference, isTrackReferencePlaceholder } from './track-reference.types';\n\n/**\n * Returns a id to identify the `TrackReference` or `TrackReferencePlaceholder` based on\n * participant, track source and trackSid.\n * @remarks\n * The id pattern is: `${participantIdentity}_${trackSource}_${trackSid}` for `TrackReference`\n * and `${participantIdentity}_${trackSource}_placeholder` for `TrackReferencePlaceholder`.\n */\nexport function getTrackReferenceId(trackReference: TrackReferenceOrPlaceholder | number) {\n  if (typeof trackReference === 'string' || typeof trackReference === 'number') {\n    return `${trackReference}`;\n  } else if (isTrackReferencePlaceholder(trackReference)) {\n    return `${trackReference.participant.identity}_${trackReference.source}_placeholder`;\n  } else if (isTrackReference(trackReference)) {\n    return `${trackReference.participant.identity}_${trackReference.publication.source}_${trackReference.publication.trackSid}`;\n  } else {\n    throw new Error(`Can't generate a id for the given track reference: ${trackReference}`);\n  }\n}\n\nexport type TrackReferenceId = ReturnType<typeof getTrackReferenceId>;\n\n/** Returns the Source of the TrackReference. */\nexport function getTrackReferenceSource(trackReference: TrackReferenceOrPlaceholder): Track.Source {\n  if (isTrackReference(trackReference)) {\n    return trackReference.publication.source;\n  } else {\n    return trackReference.source;\n  }\n}\n\nexport function isEqualTrackRef(\n  a?: TrackReferenceOrPlaceholder,\n  b?: TrackReferenceOrPlaceholder,\n): boolean {\n  if (a === undefined || b === undefined) {\n    return false;\n  }\n  if (isTrackReference(a) && isTrackReference(b)) {\n    return a.publication.trackSid === b.publication.trackSid;\n  } else {\n    return getTrackReferenceId(a) === getTrackReferenceId(b);\n  }\n}\n\n/**\n * Check if the `TrackReference` is pinned.\n */\nexport function isTrackReferencePinned(\n  trackReference: TrackReferenceOrPlaceholder,\n  pinState: PinState | undefined,\n): boolean {\n  if (typeof pinState === 'undefined') {\n    return false;\n  }\n  if (isTrackReference(trackReference)) {\n    return pinState.some(\n      (pinnedTrackReference) =>\n        pinnedTrackReference.participant.identity === trackReference.participant.identity &&\n        isTrackReference(pinnedTrackReference) &&\n        pinnedTrackReference.publication.trackSid === trackReference.publication.trackSid,\n    );\n  } else if (isTrackReferencePlaceholder(trackReference)) {\n    return pinState.some(\n      (pinnedTrackReference) =>\n        pinnedTrackReference.participant.identity === trackReference.participant.identity &&\n        isTrackReferencePlaceholder(pinnedTrackReference) &&\n        pinnedTrackReference.source === trackReference.source,\n    );\n  } else {\n    return false;\n  }\n}\n\n/**\n * Check if the current `currentTrackRef` is the placeholder for next `nextTrackRef`.\n * Based on the participant identity and the source.\n * @internal\n */\nexport function isPlaceholderReplacement(\n  currentTrackRef: TrackReferenceOrPlaceholder,\n  nextTrackRef: TrackReferenceOrPlaceholder,\n) {\n  // if (typeof nextTrackRef === 'number' || typeof currentTrackRef === 'number') {\n  //   return false;\n  // }\n  return (\n    isTrackReferencePlaceholder(currentTrackRef) &&\n    isTrackReference(nextTrackRef) &&\n    nextTrackRef.participant.identity === currentTrackRef.participant.identity &&\n    nextTrackRef.source === currentTrackRef.source\n  );\n}\n", "import type { Participant, TrackPublication } from 'livekit-client';\n\nimport type { TrackReference } from './track-reference';\nimport { isEqualTrackRef } from './track-reference';\nimport type { PinState } from './types';\n\nexport function isLocal(p: Participant) {\n  return p.isLocal;\n}\n\nexport function isRemote(p: Participant) {\n  return !p.isLocal;\n}\n\nexport const attachIfSubscribed = (\n  publication: TrackPublication | undefined,\n  element: HTMLMediaElement | null | undefined,\n) => {\n  if (!publication) return;\n  const { isSubscribed, track } = publication;\n  if (element && track) {\n    if (isSubscribed) {\n      track.attach(element);\n    } else {\n      track.detach(element);\n    }\n  }\n};\n\n/**\n * Check if the participant track reference is pinned.\n */\nexport function isParticipantTrackReferencePinned(\n  trackRef: TrackReference,\n  pinState: PinState | undefined,\n): boolean {\n  if (pinState === undefined) {\n    return false;\n  }\n\n  return pinState.some((pinnedTrackRef) => isEqualTrackRef(pinnedTrackRef, trackRef));\n}\n\n/**\n * Calculates the scrollbar width by creating two HTML elements\n * and messaging the difference.\n * @internal\n */\nexport function getScrollBarWidth() {\n  const inner = document.createElement('p');\n  inner.style.width = '100%';\n  inner.style.height = '200px';\n\n  const outer = document.createElement('div');\n  outer.style.position = 'absolute';\n  outer.style.top = '0px';\n  outer.style.left = '0px';\n  outer.style.visibility = 'hidden';\n  outer.style.width = '200px';\n  outer.style.height = '150px';\n  outer.style.overflow = 'hidden';\n  outer.appendChild(inner);\n\n  document.body.appendChild(outer);\n  const w1 = inner.offsetWidth;\n  outer.style.overflow = 'scroll';\n  let w2 = inner.offsetWidth;\n  if (w1 === w2) {\n    w2 = outer.clientWidth;\n  }\n  document.body.removeChild(outer);\n  const scrollBarWidth = w1 - w2;\n  return scrollBarWidth;\n}\n", "/**\n * @internal\n */\nexport function isWeb(): boolean {\n  return typeof document !== 'undefined';\n}\n\n/**\n * Mobile browser detection based on `navigator.userAgent` string.\n * Defaults to returning `false` if not in a browser.\n *\n * @remarks\n * This should only be used if feature detection or other methods do not work!\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/HTTP/Browser_detection_using_the_user_agent#mobile_device_detection\n */\nexport function isMobileBrowser(): boolean {\n  return isWeb() ? /Mobi/i.test(window.navigator.userAgent) : false;\n}\n", "// The MIT License (MIT)\n\n// Copyright (c) <PERSON> <k<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com> and <PERSON>\n\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n\ninterface RegExOptions {\n  /**\n\t\tOnly match an exact string. Useful with `RegExp#test` to check if a string is a URL.\n\t\t@defaultValue false\n\t\t*/\n  readonly exact?: boolean;\n}\n\nexport function createUrlRegExp(options: RegExOptions) {\n  options = {\n    ...options,\n  };\n\n  const protocol = `(?:(?:[a-z]+:)?//)?`;\n  const auth = '(?:\\\\S+(?::\\\\S*)?@)?';\n  const ip = new RegExp(\n    '(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)(?:\\\\.(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)){3}',\n    'g',\n  ).source;\n  const host = '(?:(?:[a-z\\\\u00a1-\\\\uffff0-9][-_]*)*[a-z\\\\u00a1-\\\\uffff0-9]+)';\n  const domain = '(?:\\\\.(?:[a-z\\\\u00a1-\\\\uffff0-9]-*)*[a-z\\\\u00a1-\\\\uffff0-9]+)*';\n  const tld = `(?:\\\\.(?:[a-z\\\\u00a1-\\\\uffff]{2,}))\\\\.?`;\n  const port = '(?::\\\\d{2,5})?';\n  const path = '(?:[/?#][^\\\\s\"]*)?';\n  const regex = `(?:${protocol}|www\\\\.)${auth}(?:localhost|${ip}|${host}${domain}${tld})${port}${path}`;\n\n  return options.exact ? new RegExp(`(?:^${regex}$)`, 'i') : new RegExp(regex, 'ig');\n}\n", "// source code adapted from https://github.com/sindresorhus/email-regex due to ESM import incompatibilities when trying to serve a CJS version of components\n\nconst regex = '[^\\\\.\\\\s@:](?:[^\\\\s@:]*[^\\\\s@:\\\\.])?@[^\\\\.\\\\s@]+(?:\\\\.[^\\\\.\\\\s@]+)*';\n\nfunction createEmailRegExp({ exact }: { exact?: boolean } = {}) {\n  return exact ? new RegExp(`^${regex}$`) : new RegExp(regex, 'g');\n}\nexport { createEmailRegExp };\n", "import { computePosition, flip, offset, shift } from '@floating-ui/dom';\n\nexport async function computeMenuPosition(\n  button: HTMLElement,\n  menu: HTMLElement,\n): Promise<{ x: number; y: number }> {\n  const { x, y } = await computePosition(button, menu, {\n    placement: 'top',\n    middleware: [offset(6), flip(), shift({ padding: 5 })],\n  });\n  return { x, y };\n}\n\nexport function wasClickOutside(insideElement: HTMLElement, event: MouseEvent): boolean {\n  const isOutside = !insideElement.contains(event.target as Node);\n  return isOutside;\n}\n", "import { createEmailRegExp } from './emailRegex';\nimport { createUrlRegExp } from './url-regex';\n\nexport type TokenizeGrammar = { [type: string]: RegExp };\n\nexport const createDefaultGrammar = () => {\n  return {\n    email: createEmailRegExp(),\n    url: createUrlRegExp({}),\n  } satisfies TokenizeGrammar;\n};\n\nexport function tokenize<T extends TokenizeGrammar>(input: string, grammar: T) {\n  const matches = Object.entries(grammar)\n    .map(([type, rx], weight) =>\n      Array.from(input.matchAll(rx)).map(({ index, 0: content }) => ({\n        type: type as keyof T,\n        weight,\n        content,\n        index: index ?? 0,\n      })),\n    )\n    .flat()\n    .sort((a, b) => {\n      const d = a.index - b.index;\n      return d !== 0 ? d : a.weight - b.weight;\n    })\n    .filter(({ index }, i, arr) => {\n      if (i === 0) return true;\n      const prev = arr[i - 1];\n      return prev.index + prev.content.length <= index;\n    });\n\n  const tokens = [];\n  let pos = 0;\n  for (const { type, content, index } of matches) {\n    if (index > pos) tokens.push(input.substring(pos, index));\n    tokens.push({ type, content });\n    pos = index + content.length;\n  }\n  if (input.length > pos) tokens.push(input.substring(pos));\n  return tokens;\n}\n", "import { ParticipantEvent, RoomEvent } from 'livekit-client';\n\nexport const allRemoteParticipantRoomEvents = [\n  RoomEvent.ConnectionStateChanged,\n  RoomEvent.RoomMetadataChanged,\n\n  RoomEvent.ActiveSpeakersChanged,\n  RoomEvent.ConnectionQualityChanged,\n\n  RoomEvent.ParticipantConnected,\n  RoomEvent.ParticipantDisconnected,\n  RoomEvent.ParticipantPermissionsChanged,\n  RoomEvent.ParticipantMetadataChanged,\n  RoomEvent.ParticipantNameChanged,\n  RoomEvent.ParticipantAttributesChanged,\n\n  RoomEvent.TrackMuted,\n  RoomEvent.TrackUnmuted,\n  RoomEvent.TrackPublished,\n  RoomEvent.TrackUnpublished,\n  RoomEvent.TrackStreamStateChanged,\n  RoomEvent.TrackSubscriptionFailed,\n  RoomEvent.TrackSubscriptionPermissionChanged,\n  RoomEvent.TrackSubscriptionStatusChanged,\n];\n\nexport const allParticipantRoomEvents = [\n  ...allRemoteParticipantRoomEvents,\n  RoomEvent.LocalTrackPublished,\n  RoomEvent.LocalTrackUnpublished,\n];\n\nexport const participantTrackEvents = [\n  ParticipantEvent.TrackPublished,\n  ParticipantEvent.TrackUnpublished,\n  ParticipantEvent.TrackMuted,\n  ParticipantEvent.TrackUnmuted,\n  ParticipantEvent.TrackStreamStateChanged,\n  ParticipantEvent.TrackSubscribed,\n  ParticipantEvent.TrackUnsubscribed,\n  ParticipantEvent.TrackSubscriptionPermissionChanged,\n  ParticipantEvent.TrackSubscriptionFailed,\n  ParticipantEvent.LocalTrackPublished,\n  ParticipantEvent.LocalTrackUnpublished,\n];\n\nexport const allRemoteParticipantEvents = [\n  ParticipantEvent.ConnectionQualityChanged,\n  ParticipantEvent.IsSpeakingChanged,\n  ParticipantEvent.ParticipantMetadataChanged,\n  ParticipantEvent.ParticipantPermissionsChanged,\n\n  ParticipantEvent.TrackMuted,\n  ParticipantEvent.TrackUnmuted,\n  ParticipantEvent.TrackPublished,\n  ParticipantEvent.TrackUnpublished,\n  ParticipantEvent.TrackStreamStateChanged,\n  ParticipantEvent.TrackSubscriptionFailed,\n  ParticipantEvent.TrackSubscriptionPermissionChanged,\n  ParticipantEvent.TrackSubscriptionStatusChanged,\n];\n\nexport const allParticipantEvents = [\n  ...allRemoteParticipantEvents,\n  ParticipantEvent.LocalTrackPublished,\n  ParticipantEvent.LocalTrackUnpublished,\n];\n", "import {\n  setLogLevel as setClientSdkLogLevel,\n  setLogExtension as setClientSdkLogExtension,\n  LogLevel as LogLevelEnum,\n} from 'livekit-client';\nimport loglevel from 'loglevel';\n\nexport const log = loglevel.getLogger('lk-components-js');\nlog.setDefaultLevel('WARN');\n\ntype LogLevel = Parameters<typeof setClientSdkLogLevel>[0];\ntype SetLogLevelOptions = {\n  liveKitClientLogLevel?: LogLevel;\n};\n\n/**\n * Set the log level for both the `@livekit/components-react` package and the `@livekit-client` package.\n * To set the `@livekit-client` log independently, use the `liveKitClientLogLevel` prop on the `options` object.\n * @public\n */\nexport function setLogLevel(level: LogLevel, options: SetLogLevelOptions = {}): void {\n  log.setLevel(level);\n  setClientSdkLogLevel(options.liveKitClientLogLevel ?? level);\n}\n\ntype LogExtension = (level: LogLevel, msg: string, context?: object) => void;\ntype SetLogExtensionOptions = {\n  liveKitClientLogExtension?: LogExtension;\n};\n\n/**\n * Set the log extension for both the `@livekit/components-react` package and the `@livekit-client` package.\n * To set the `@livekit-client` log extension, use the `liveKitClientLogExtension` prop on the `options` object.\n * @public\n */\nexport function setLogExtension(extension: LogExtension, options: SetLogExtensionOptions = {}) {\n  const originalFactory = log.methodFactory;\n\n  log.methodFactory = (methodName, configLevel, loggerName) => {\n    const rawMethod = originalFactory(methodName, configLevel, loggerName);\n\n    const logLevel = LogLevelEnum[methodName];\n    const needLog = logLevel >= configLevel && logLevel < LogLevelEnum.silent;\n\n    return (msg, context?: [msg: string, context: object]) => {\n      if (context) rawMethod(msg, context);\n      else rawMethod(msg);\n      if (needLog) {\n        extension(logLevel, msg, context);\n      }\n    };\n  };\n  log.setLevel(log.getLevel()); // Be sure to call setLevel method in order to apply plugin\n  setClientSdkLogExtension(options.liveKitClientLogExtension ?? extension);\n}\n", "import { log } from '../logger';\n\n/**\n * @public\n */\nexport type GridLayoutDefinition = {\n  /** Column count of the grid layout. */\n  columns: number;\n  /** Row count of the grid layout. */\n  rows: number;\n  // # Constraints that have to be meet to use this layout.\n  /**\n   * Minimum grid container width required to use this layout.\n   * @remarks\n   * If this constraint is not met, we try to select a layout with fewer tiles\n   * (`tiles=columns*rows`) that is within the constraint.\n   */\n  minWidth?: number;\n  /**\n   * Minimum grid container height required to use this layout.\n   * @remarks\n   * If this constraint is not met, we try to select a layout with fewer tiles\n   * (`tiles=columns*rows`) that is within the constraint.\n   */\n  minHeight?: number;\n  /**\n   * For which orientation the layout definition should be applied.\n   * Will be used for both landscape and portrait if no value is specified.\n   */\n  orientation?: 'landscape' | 'portrait';\n};\n\nexport type GridLayoutInfo = {\n  /** Layout name (convention `<column_count>x<row_count>`). */\n  name: string;\n  /** Column count of the layout. */\n  columns: number;\n  /** Row count of the layout. */\n  rows: number;\n  // # Constraints that have to be meet to use this layout.\n  // ## 1. Participant range:\n  /** Maximum tiles that fit into this layout. */\n  maxTiles: number;\n  // ## 2. Screen size limits:\n  /** Minimum width required to use this layout. */\n  minWidth: number;\n  /** Minimum height required to use this layout. */\n  minHeight: number;\n  orientation?: 'landscape' | 'portrait';\n};\n\nexport const GRID_LAYOUTS: GridLayoutDefinition[] = [\n  {\n    columns: 1,\n    rows: 1,\n  },\n  {\n    columns: 1,\n    rows: 2,\n    orientation: 'portrait',\n  },\n  {\n    columns: 2,\n    rows: 1,\n    orientation: 'landscape',\n  },\n  {\n    columns: 2,\n    rows: 2,\n    minWidth: 560,\n  },\n  {\n    columns: 3,\n    rows: 3,\n    minWidth: 700,\n  },\n  {\n    columns: 4,\n    rows: 4,\n    minWidth: 960,\n  },\n  {\n    columns: 5,\n    rows: 5,\n    minWidth: 1100,\n  },\n] as const;\n\nexport function selectGridLayout(\n  layoutDefinitions: GridLayoutDefinition[],\n  participantCount: number,\n  width: number,\n  height: number,\n): GridLayoutInfo {\n  if (layoutDefinitions.length < 1) {\n    throw new Error('At least one grid layout definition must be provided.');\n  }\n  const layouts = expandAndSortLayoutDefinitions(layoutDefinitions);\n  if (width <= 0 || height <= 0) {\n    return layouts[0];\n  }\n  // Find the best layout to fit all participants.\n  let currentLayoutIndex = 0;\n  const containerOrientation = width / height > 1 ? 'landscape' : 'portrait';\n  let layout = layouts.find((layout_, index, allLayouts) => {\n    currentLayoutIndex = index;\n    const isBiggerLayoutAvailable =\n      allLayouts.findIndex((l, i) => {\n        const fitsOrientation = !l.orientation || l.orientation === containerOrientation;\n        const layoutIsBiggerThanCurrent = i > index;\n        const layoutFitsSameAmountOfParticipants = l.maxTiles === layout_.maxTiles;\n        return layoutIsBiggerThanCurrent && layoutFitsSameAmountOfParticipants && fitsOrientation;\n      }) !== -1;\n    return layout_.maxTiles >= participantCount && !isBiggerLayoutAvailable;\n  });\n  if (layout === undefined) {\n    layout = layouts[layouts.length - 1];\n    if (layout) {\n      log.warn(\n        `No layout found for: participantCount: ${participantCount}, width/height: ${width}/${height} fallback to biggest available layout (${layout}).`,\n      );\n    } else {\n      throw new Error(`No layout or fallback layout found.`);\n    }\n  }\n\n  // Check if the layout fits into the screen constraints. If not, recursively check the next smaller layout.\n  if (width < layout.minWidth || height < layout.minHeight) {\n    // const currentLayoutIndex = layouts.indexOf(layout);\n    if (currentLayoutIndex > 0) {\n      const smallerLayout = layouts[currentLayoutIndex - 1];\n      layout = selectGridLayout(\n        layouts.slice(0, currentLayoutIndex),\n        smallerLayout.maxTiles,\n        width,\n        height,\n      );\n    }\n  }\n  return layout;\n}\n\n/**\n * @internal\n */\nexport function expandAndSortLayoutDefinitions(layouts: GridLayoutDefinition[]): GridLayoutInfo[] {\n  return [...layouts]\n    .map((layout) => {\n      return {\n        name: `${layout.columns}x${layout.rows}`,\n        columns: layout.columns,\n        rows: layout.rows,\n        maxTiles: layout.columns * layout.rows,\n        minWidth: layout.minWidth ?? 0,\n        minHeight: layout.minHeight ?? 0,\n        orientation: layout.orientation,\n      } satisfies GridLayoutInfo;\n    })\n    .sort((a, b) => {\n      if (a.maxTiles !== b.maxTiles) {\n        return a.maxTiles - b.maxTiles;\n      } else if (a.minWidth !== 0 || b.minWidth !== 0) {\n        return a.minWidth - b.minWidth;\n      } else if (a.minHeight !== 0 || b.minHeight !== 0) {\n        return a.minHeight - b.minHeight;\n      } else {\n        return 0;\n      }\n    });\n}\n", "export function setDifference<T>(setA: Set<T>, setB: Set<T>): Set<T> {\n  const _difference = new Set(setA);\n  for (const elem of setB) {\n    _difference.delete(elem);\n  }\n  return _difference;\n}\n", "/**\n * Returns `true` if the browser supports screen sharing.\n */\nexport function supportsScreenSharing(): boolean {\n  return (\n    typeof navigator !== 'undefined' &&\n    navigator.mediaDevices &&\n    !!navigator.mediaDevices.getDisplayMedia\n  );\n}\n", "import type { TranscriptionSegment } from 'livekit-client';\n\nexport type ReceivedTranscriptionSegment = TranscriptionSegment & {\n  receivedAtMediaTimestamp: number;\n  receivedAt: number;\n};\n\nexport function getActiveTranscriptionSegments(\n  segments: ReceivedTranscriptionSegment[],\n  syncTimes: { timestamp: number; rtpTimestamp?: number },\n  maxAge = 0,\n) {\n  return segments.filter((segment) => {\n    const hasTrackSync = !!syncTimes.rtpTimestamp;\n    const currentTrackTime = syncTimes.rtpTimestamp ?? performance.timeOrigin + performance.now();\n    // if a segment arrives late, consider startTime to be the media timestamp from when the segment was received client side\n    const displayStartTime = hasTrackSync\n      ? Math.max(segment.receivedAtMediaTimestamp, segment.startTime)\n      : segment.receivedAt;\n    // \"active\" duration is computed by the diff between start and end time, so we don't rely on displayStartTime to always be the same as the segment's startTime\n    const segmentDuration = maxAge + segment.endTime - segment.startTime;\n    return (\n      currentTrackTime >= displayStartTime && currentTrackTime <= displayStartTime + segmentDuration\n    );\n  });\n}\n\nexport function addMediaTimestampToTranscription(\n  segment: TranscriptionSegment,\n  timestamps: { timestamp: number; rtpTimestamp?: number },\n): ReceivedTranscriptionSegment {\n  return {\n    ...segment,\n    receivedAtMediaTimestamp: timestamps.rtpTimestamp ?? 0,\n    receivedAt: timestamps.timestamp,\n  };\n}\n\n/**\n * @returns An array of unique (by id) `TranscriptionSegment`s. Latest wins. If the resulting array would be longer than `windowSize`, the array will be reduced to `windowSize` length\n */\nexport function dedupeSegments<T extends TranscriptionSegment>(\n  prevSegments: T[],\n  newSegments: T[],\n  windowSize: number,\n) {\n  return [...prevSegments, ...newSegments]\n    .reduceRight((acc, segment) => {\n      if (!acc.find((val) => val.id === segment.id)) {\n        acc.unshift(segment);\n      }\n      return acc;\n    }, [] as Array<T>)\n    .slice(0 - windowSize);\n}\n\nexport function didActiveSegmentsChange<T extends TranscriptionSegment>(\n  prevActive: T[],\n  newActive: T[],\n) {\n  if (newActive.length !== prevActive.length) {\n    return true;\n  }\n  return !newActive.every((newSegment) => {\n    return prevActive.find(\n      (prevSegment) =>\n        prevSegment.id === newSegment.id &&\n        prevSegment.text === newSegment.text &&\n        prevSegment.final === newSegment.final &&\n        prevSegment.language === newSegment.language &&\n        prevSegment.startTime === newSegment.startTime &&\n        prevSegment.endTime === newSegment.endTime,\n    );\n  });\n}\n", "import type { Participant, ParticipantKind, Track, TrackPublication } from 'livekit-client';\nimport type { TrackReference, TrackReferenceOrPlaceholder } from './track-reference';\n\n// ## PinState Type\n/** @public */\nexport type PinState = TrackReferenceOrPlaceholder[];\nexport const PIN_DEFAULT_STATE: PinState = [];\n\n// ## WidgetState Types\n/** @public */\nexport type WidgetState = {\n  showChat: boolean;\n  unreadMessages: number;\n  showSettings?: boolean;\n};\nexport const WIDGET_DEFAULT_STATE: WidgetState = {\n  showChat: false,\n  unreadMessages: 0,\n  showSettings: false,\n};\n\n// ## Track Source Types\nexport type TrackSourceWithOptions = { source: Track.Source; withPlaceholder: boolean };\n\nexport type SourcesArray = Track.Source[] | TrackSourceWithOptions[];\n\n// ### Track Source Type Predicates\nexport function isSourceWitOptions(source: SourcesArray[number]): source is TrackSourceWithOptions {\n  return typeof source === 'object';\n}\n\nexport function isSourcesWithOptions(sources: SourcesArray): sources is TrackSourceWithOptions[] {\n  return (\n    Array.isArray(sources) &&\n    (sources as TrackSourceWithOptions[]).filter(isSourceWitOptions).length > 0\n  );\n}\n\n// ## Loop Filter Types\nexport type TrackReferenceFilter = Parameters<TrackReferenceOrPlaceholder[]['filter']>['0'];\nexport type ParticipantFilter = Parameters<Participant[]['filter']>['0'];\n\n// ## Other Types\n/** @internal */\nexport interface ParticipantClickEvent {\n  participant: Participant;\n  track?: TrackPublication;\n}\n\nexport type TrackSource<T extends Track.Source> = RequireAtLeastOne<\n  { source: T; name: string; participant: Participant },\n  'name' | 'source'\n>;\n\nexport type ParticipantTrackIdentifier = RequireAtLeastOne<\n  { sources: Track.Source[]; name: string; kind: Track.Kind },\n  'sources' | 'name' | 'kind'\n>;\n\n/**\n * @beta\n */\nexport type ParticipantIdentifier = RequireAtLeastOne<\n  { kind: ParticipantKind; identity: string },\n  'identity' | 'kind'\n>;\n\n/**\n * The TrackIdentifier type is used to select Tracks either based on\n * - Track.Source and/or name of the track, e.g. `{source: Track.Source.Camera}` or `{name: \"my-track\"}`\n * - TrackReference (participant and publication)\n * @internal\n */\nexport type TrackIdentifier<T extends Track.Source = Track.Source> =\n  | TrackSource<T>\n  | TrackReference;\n\n// ## Util Types\ntype RequireAtLeastOne<T, Keys extends keyof T = keyof T> = Pick<T, Exclude<keyof T, Keys>> &\n  {\n    [K in Keys]-?: Required<Pick<T, K>> & Partial<Pick<T, Exclude<Keys, K>>>;\n  }[Keys];\n\nexport type RequireOnlyOne<T, Keys extends keyof T = keyof T> = Pick<T, Exclude<keyof T, Keys>> &\n  {\n    [K in Keys]-?: Required<Pick<T, K>> & Partial<Record<Exclude<Keys, K>, undefined>>;\n  }[Keys];\n\nexport type AudioSource = Track.Source.Microphone | Track.Source.ScreenShareAudio;\nexport type VideoSource = Track.Source.Camera | Track.Source.ScreenShare;\n", "import { Track } from 'livekit-client';\nimport type { TrackReferenceOrPlaceholder } from '../track-reference';\nimport { isTrackReference } from '../track-reference';\nimport {\n  sortParticipantsByAudioLevel,\n  sortParticipantsByIsSpeaking,\n  sortParticipantsByJoinedAt,\n  sortParticipantsByLastSpokenAT,\n  sortTrackReferencesByType,\n  sortTrackRefsByIsCameraEnabled,\n} from './base-sort-functions';\n\n/**\n * Default sort for `TrackReferenceOrPlaceholder`, it'll order participants by:\n * 1. local camera track (publication.isLocal)\n * 2. remote screen_share track\n * 3. local screen_share track\n * 4. remote dominant speaker camera track (sorted by speaker with the loudest audio level)\n * 5. other remote speakers that are recently active\n * 6. remote unmuted camera tracks\n * 7. remote tracks sorted by joinedAt\n */\nexport function sortTrackReferences(\n  tracks: TrackReferenceOrPlaceholder[],\n): TrackReferenceOrPlaceholder[] {\n  const localTracks: TrackReferenceOrPlaceholder[] = [];\n  const screenShareTracks: TrackReferenceOrPlaceholder[] = [];\n  const cameraTracks: TrackReferenceOrPlaceholder[] = [];\n  const undefinedTracks: TrackReferenceOrPlaceholder[] = [];\n\n  tracks.forEach((trackRef) => {\n    if (trackRef.participant.isLocal && trackRef.source === Track.Source.Camera) {\n      localTracks.push(trackRef);\n    } else if (trackRef.source === Track.Source.ScreenShare) {\n      screenShareTracks.push(trackRef);\n    } else if (trackRef.source === Track.Source.Camera) {\n      cameraTracks.push(trackRef);\n    } else {\n      undefinedTracks.push(trackRef);\n    }\n  });\n\n  const sortedScreenShareTracks = sortScreenShareTracks(screenShareTracks);\n  const sortedCameraTracks = sortCameraTracks(cameraTracks);\n\n  return [...localTracks, ...sortedScreenShareTracks, ...sortedCameraTracks, ...undefinedTracks];\n}\n\n/**\n * Sort an array of `TrackReference` screen shares.\n * Main sorting order:\n * 1. remote screen shares\n * 2. local screen shares\n * Secondary sorting by participant's joining time.\n */\nfunction sortScreenShareTracks(\n  screenShareTracks: TrackReferenceOrPlaceholder[],\n): TrackReferenceOrPlaceholder[] {\n  const localScreenShares: TrackReferenceOrPlaceholder[] = [];\n  const remoteScreenShares: TrackReferenceOrPlaceholder[] = [];\n\n  screenShareTracks.forEach((trackRef) => {\n    if (trackRef.participant.isLocal) {\n      localScreenShares.push(trackRef);\n    } else {\n      remoteScreenShares.push(trackRef);\n    }\n  });\n\n  localScreenShares.sort((a, b) => sortParticipantsByJoinedAt(a.participant, b.participant));\n  remoteScreenShares.sort((a, b) => sortParticipantsByJoinedAt(a.participant, b.participant));\n\n  const sortedScreenShareTrackRefs = [...remoteScreenShares, ...localScreenShares];\n  return sortedScreenShareTrackRefs;\n}\n\nfunction sortCameraTracks(\n  cameraTrackReferences: TrackReferenceOrPlaceholder[],\n): TrackReferenceOrPlaceholder[] {\n  const localCameraTracks: TrackReferenceOrPlaceholder[] = [];\n  const remoteCameraTracks: TrackReferenceOrPlaceholder[] = [];\n\n  cameraTrackReferences.forEach((trackRef) => {\n    if (trackRef.participant.isLocal) {\n      localCameraTracks.push(trackRef);\n    } else {\n      remoteCameraTracks.push(trackRef);\n    }\n  });\n\n  remoteCameraTracks.sort((a, b) => {\n    // Participant with higher audio level goes first.\n    if (a.participant.isSpeaking && b.participant.isSpeaking) {\n      return sortParticipantsByAudioLevel(a.participant, b.participant);\n    }\n\n    // A speaking participant goes before one that is not speaking.\n    if (a.participant.isSpeaking !== b.participant.isSpeaking) {\n      return sortParticipantsByIsSpeaking(a.participant, b.participant);\n    }\n\n    // A participant that spoke recently goes before a participant that spoke a while back.\n    if (a.participant.lastSpokeAt !== b.participant.lastSpokeAt) {\n      return sortParticipantsByLastSpokenAT(a.participant, b.participant);\n    }\n\n    // TrackReference before TrackReferencePlaceholder\n    if (isTrackReference(a) !== isTrackReference(b)) {\n      return sortTrackReferencesByType(a, b);\n    }\n\n    // Tiles with video on before tiles with muted video track.\n    if (a.participant.isCameraEnabled !== b.participant.isCameraEnabled) {\n      return sortTrackRefsByIsCameraEnabled(a, b);\n    }\n\n    // A participant that joined a long time ago goes before one that joined recently.\n    return sortParticipantsByJoinedAt(a.participant, b.participant);\n  });\n\n  return [...localCameraTracks, ...remoteCameraTracks];\n}\n", "import type { Participant } from 'livekit-client';\nimport { Track } from 'livekit-client';\nimport type { TrackReferenceOrPlaceholder } from '../track-reference';\nimport { getTrackReferenceSource, isTrackReference } from '../track-reference';\n\nexport function sortParticipantsByAudioLevel(\n  a: Pick<Participant, 'audioLevel'>,\n  b: Pick<Participant, 'audioLevel'>,\n): number {\n  return b.audioLevel - a.audioLevel;\n}\n\nexport function sortParticipantsByIsSpeaking(\n  a: Pick<Participant, 'isSpeaking'>,\n  b: Pick<Participant, 'isSpeaking'>,\n): number {\n  if (a.isSpeaking === b.isSpeaking) {\n    return 0;\n  } else {\n    return a.isSpeaking ? -1 : 1;\n  }\n}\n\nexport function sortParticipantsByLastSpokenAT(\n  a: Pick<Participant, 'lastSpokeAt'>,\n  b: Pick<Participant, 'lastSpokeAt'>,\n): number {\n  if (a.lastSpokeAt !== undefined || b.lastSpokeAt !== undefined) {\n    return (b.lastSpokeAt?.getTime() ?? 0) - (a.lastSpokeAt?.getTime() ?? 0);\n  } else {\n    return 0;\n  }\n}\n\nexport function sortParticipantsByJoinedAt(\n  a: Pick<Participant, 'joinedAt'>,\n  b: Pick<Participant, 'joinedAt'>,\n) {\n  return (a.joinedAt?.getTime() ?? 0) - (b.joinedAt?.getTime() ?? 0);\n}\n\nexport function sortTrackReferencesByType(\n  a: TrackReferenceOrPlaceholder,\n  b: TrackReferenceOrPlaceholder,\n) {\n  if (isTrackReference(a)) {\n    if (isTrackReference(b)) {\n      return 0;\n    } else {\n      return -1;\n    }\n  } else if (isTrackReference(b)) {\n    return 1;\n  } else {\n    return 0;\n  }\n}\n\n/** TrackReference with screen share source goes first. */\nexport function sortTrackReferencesByScreenShare(\n  a: TrackReferenceOrPlaceholder,\n  b: TrackReferenceOrPlaceholder,\n): number {\n  const sourceA = getTrackReferenceSource(a);\n  const sourceB = getTrackReferenceSource(b);\n\n  if (sourceA === sourceB) {\n    if (sourceA === Track.Source.ScreenShare) {\n      if (a.participant.isLocal === b.participant.isLocal) {\n        return 0;\n      } else {\n        return a.participant.isLocal ? 1 : -1;\n      }\n    }\n    return 0;\n  } else if (sourceA === Track.Source.ScreenShare) {\n    return -1;\n  } else if (sourceB === Track.Source.ScreenShare) {\n    return 1;\n  } else {\n    return 0;\n  }\n}\n\nexport function sortTrackRefsByIsCameraEnabled(\n  a: { participant: { isCameraEnabled: boolean } },\n  b: { participant: { isCameraEnabled: boolean } },\n) {\n  const aVideo = a.participant.isCameraEnabled;\n  const bVideo = b.participant.isCameraEnabled;\n\n  if (aVideo !== bVideo) {\n    if (aVideo) {\n      return -1;\n    } else {\n      return 1;\n    }\n  } else {\n    return 0;\n  }\n}\n", "import type { Participant } from 'livekit-client';\nimport { LocalParticipant } from 'livekit-client';\nimport {\n  sortParticipantsByAudioLevel,\n  sortParticipantsByIsSpeaking,\n  sortParticipantsByJoinedAt,\n  sortParticipantsByLastSpokenAT,\n} from './base-sort-functions';\n\n/**\n * Default sort for participants, it'll order participants by:\n * 1. local participant\n * 2. dominant speaker (speaker with the loudest audio level)\n * 3. other speakers that are recently active\n * 4. participants with video on\n * 5. by joinedAt\n */\nexport function sortParticipants(participants: Participant[]): Participant[] {\n  const sortedParticipants = [...participants];\n  sortedParticipants.sort((a, b) => {\n    // loudest speaker first\n    if (a.isSpeaking && b.isSpeaking) {\n      return sortParticipantsByAudioLevel(a, b);\n    }\n\n    // speaker goes first\n    if (a.isSpeaking !== b.isSpeaking) {\n      return sortParticipantsByIsSpeaking(a, b);\n    }\n\n    // last active speaker first\n    if (a.lastSpokeAt !== b.lastSpokeAt) {\n      return sortParticipantsByLastSpokenAT(a, b);\n    }\n\n    // video on\n    const aVideo = a.videoTrackPublications.size > 0;\n    const bVideo = b.videoTrackPublications.size > 0;\n    if (aVideo !== bVideo) {\n      if (aVideo) {\n        return -1;\n      } else {\n        return 1;\n      }\n    }\n\n    // joinedAt\n    return sortParticipantsByJoinedAt(a, b);\n  });\n  const localParticipant = sortedParticipants.find((p) => p.isLocal) as LocalParticipant;\n  if (localParticipant) {\n    const localIdx = sortedParticipants.indexOf(localParticipant);\n    if (localIdx >= 0) {\n      sortedParticipants.splice(localIdx, 1);\n      if (sortedParticipants.length > 0) {\n        sortedParticipants.splice(0, 0, localParticipant);\n      } else {\n        sortedParticipants.push(localParticipant);\n      }\n    }\n  }\n  return sortedParticipants;\n}\n", "export function chunk<T>(input: Array<T>, size: number) {\n  return input.reduce(\n    (arr, item, idx) => {\n      return idx % size === 0\n        ? [...arr, [item]]\n        : [...arr.slice(0, -1), [...arr.slice(-1)[0], item]];\n    },\n    [] as Array<Array<T>>,\n  );\n}\n\nexport function zip<T, U>(a1: Array<T>, a2: Array<U>) {\n  const resultLength = Math.max(a1.length, a2.length);\n  return new Array(resultLength).fill([]).map((_val, idx) => [a1[idx], a2[idx]]);\n}\n\nexport function differenceBy<T>(a1: Array<T>, a2: Array<T>, by: (arg: T) => string) {\n  return a1.filter((c) => !a2.map((v) => by(v)).includes(by(c)));\n}\n", "/**\n * Internal test function.\n *\n * @internal\n */\n\nimport { Participant, Track, TrackPublication } from 'livekit-client';\nimport type { UpdatableItem } from '../sorting/tile-array-update';\nimport type { TrackReference, TrackReferencePlaceholder } from './track-reference.types';\nimport { getTrackReferenceId } from './track-reference.utils';\n\n// Test function:\nexport const mockTrackReferencePlaceholder = (\n  id: string,\n  source: Track.Source,\n): TrackReferencePlaceholder => {\n  return { participant: new Participant(`${id}`, `${id}`), source };\n};\n\nexport const mockTrackReferencePublished = (id: string, source: Track.Source): TrackReference => {\n  const kind = [Track.Source.Camera, Track.Source.ScreenShare].includes(source)\n    ? Track.Kind.Video\n    : Track.Kind.Audio;\n  return {\n    participant: new Participant(`${id}`, `${id}`),\n    publication: new TrackPublication(kind, `${id}`, `${id}`),\n    source: source,\n  };\n};\n\ntype mockTrackReferenceSubscribedOptions = {\n  mockPublication?: boolean;\n  mockParticipant?: boolean;\n  mockIsLocal?: boolean;\n};\n\nexport const mockTrackReferenceSubscribed = (\n  id: string,\n  source: Track.Source,\n  options: mockTrackReferenceSubscribedOptions = {},\n): TrackReference => {\n  const kind = [Track.Source.Camera, Track.Source.ScreenShare].includes(source)\n    ? Track.Kind.Video\n    : Track.Kind.Audio;\n\n  const publication = new TrackPublication(kind, `${id}`, `${id}`);\n  // @ts-expect-error\n  publication.track = {};\n  return {\n    participant: options.mockParticipant\n      ? (mockParticipant(id, options.mockIsLocal ?? false) as Participant)\n      : new Participant(`${id}`, `${id}`),\n    publication: options.mockPublication\n      ? (mockTrackPublication(`publicationId(${id})`, kind, source) as TrackPublication)\n      : publication,\n    source,\n  };\n};\n\nconst mockTrackPublication = (\n  id: string,\n  kind: Track.Kind,\n  source: Track.Source,\n): Pick<TrackPublication, 'kind' | 'trackSid' | 'trackName' | 'source'> => {\n  return {\n    kind,\n    trackSid: id,\n    trackName: `name_${id}`,\n    source: source,\n  };\n};\n\nfunction mockParticipant(\n  id: string,\n  isLocal: boolean,\n): Pick<Participant, 'sid' | 'identity' | 'isLocal'> {\n  return {\n    sid: `${id}_sid`,\n    identity: `${id}`,\n    isLocal: isLocal,\n  };\n}\n\nexport function flatTrackReferenceArray<T extends UpdatableItem>(list: T[]): string[] {\n  return list.map((item) => {\n    if (typeof item === 'string' || typeof item === 'number') {\n      return `${item}`;\n    } else {\n      return getTrackReferenceId(item);\n    }\n  });\n}\n", "import { differenceBy, chunk, zip } from '../helper/array-helper';\nimport { log } from '../logger';\nimport type { TrackReferenceOrPlaceholder } from '../track-reference';\nimport {\n  getTrackReferenceId,\n  isPlaceholderReplacement,\n  isTrackReference,\n  isTrackReferencePlaceholder,\n} from '../track-reference';\nimport { flatTrackReferenceArray } from '../track-reference/test-utils';\n\ntype VisualChanges<T> = {\n  dropped: T[];\n  added: T[];\n};\n\nexport type UpdatableItem = TrackReferenceOrPlaceholder | number;\n\n/** Check to see if anything visually changes on the page. */\nexport function visualPageChange<T extends UpdatableItem>(state: T[], next: T[]): VisualChanges<T> {\n  return {\n    dropped: differenceBy(state, next, getTrackReferenceId),\n    added: differenceBy(next, state, getTrackReferenceId),\n  };\n}\n\nfunction listNeedsUpdating<T>(changes: VisualChanges<T>): boolean {\n  return changes.added.length !== 0 || changes.dropped.length !== 0;\n}\n\nexport function findIndex<T extends UpdatableItem>(\n  trackReference: T,\n  trackReferences: T[],\n): number {\n  const indexToReplace = trackReferences.findIndex(\n    (trackReference_) =>\n      getTrackReferenceId(trackReference_) === getTrackReferenceId(trackReference),\n  );\n  if (indexToReplace === -1) {\n    throw new Error(\n      `Element not part of the array: ${getTrackReferenceId(\n        trackReference,\n      )} not in ${flatTrackReferenceArray(trackReferences)}`,\n    );\n  }\n  return indexToReplace;\n}\n\n/** Swap items in the complete list of all elements */\nexport function swapItems<T extends UpdatableItem>(\n  moveForward: T,\n  moveBack: T,\n  trackReferences: T[],\n): T[] {\n  const indexToReplace = findIndex(moveForward, trackReferences);\n  const indexReplaceWith = findIndex(moveBack, trackReferences);\n\n  trackReferences.splice(indexToReplace, 1, moveBack);\n  trackReferences.splice(indexReplaceWith, 1, moveForward);\n\n  return trackReferences;\n}\n\nexport function dropItem<T extends UpdatableItem>(itemToDrop: T, list: T[]): T[] {\n  const indexOfElementToDrop = findIndex(itemToDrop, list);\n  // const indexOfElementToDrop = list.findIndex((item) => item === itemToDrop, list);\n  list.splice(indexOfElementToDrop, 1);\n  return list;\n}\n\nfunction addItem<T extends UpdatableItem>(itemToAdd: T, list: T[]): T[] {\n  return [...list, itemToAdd];\n}\n\nexport function divideIntoPages<T>(list: T[], maxElementsOnPage: number): Array<T[]> {\n  const pages = chunk(list, maxElementsOnPage);\n  return pages;\n}\n\n/** Divide the list of elements into pages and and check if pages need updating. */\nexport function updatePages<T extends UpdatableItem>(\n  currentList: T[],\n  nextList: T[],\n  maxItemsOnPage: number,\n): T[] {\n  let updatedList: T[] = refreshList(currentList, nextList);\n\n  if (updatedList.length < nextList.length) {\n    // Items got added: Find newly added items and add them to the end of the list.\n    const addedItems = differenceBy(nextList, updatedList, getTrackReferenceId);\n    updatedList = [...updatedList, ...addedItems];\n  }\n  const currentPages = divideIntoPages(updatedList, maxItemsOnPage);\n  const nextPages = divideIntoPages(nextList, maxItemsOnPage);\n\n  zip(currentPages, nextPages).forEach(([currentPage, nextPage], pageIndex) => {\n    if (currentPage && nextPage) {\n      // 1) Identify  missing tile.\n      const updatedPage = divideIntoPages(updatedList, maxItemsOnPage)[pageIndex];\n      const changes = visualPageChange(updatedPage, nextPage);\n\n      if (listNeedsUpdating(changes)) {\n        log.debug(\n          `Detected visual changes on page: ${pageIndex}, current: ${flatTrackReferenceArray(\n            currentPage,\n          )}, next: ${flatTrackReferenceArray(nextPage)}`,\n          { changes },\n        );\n        // ## Swap Items\n        if (changes.added.length === changes.dropped.length) {\n          zip(changes.added, changes.dropped).forEach(([added, dropped]) => {\n            if (added && dropped) {\n              updatedList = swapItems<T>(added, dropped, updatedList);\n            } else {\n              throw new Error(\n                `For a swap action we need a addition and a removal one is missing: ${added}, ${dropped}`,\n              );\n            }\n          });\n        }\n        // ## Handle Drop Items\n        if (changes.added.length === 0 && changes.dropped.length > 0) {\n          changes.dropped.forEach((item) => {\n            updatedList = dropItem<T>(item, updatedList);\n          });\n        }\n        // ## Handle Item added\n        if (changes.added.length > 0 && changes.dropped.length === 0) {\n          changes.added.forEach((item) => {\n            updatedList = addItem<T>(item, updatedList);\n          });\n        }\n      }\n    }\n  });\n\n  if (updatedList.length > nextList.length) {\n    // Items got removed: Find items that got completely removed from the list.\n    const missingItems = differenceBy(updatedList, nextList, getTrackReferenceId);\n    updatedList = updatedList.filter(\n      (item) => !missingItems.map(getTrackReferenceId).includes(getTrackReferenceId(item)),\n    );\n  }\n\n  return updatedList;\n}\n\n/**\n * Update the current list with the items from the next list whenever the item ids are the same\n * or the current item is a placeholder and we find a track reference in the next list\n * to replace the placeholder with.\n * @remarks\n * This is needed because `TrackReference`s can change their internal state while keeping the same id.\n */\nfunction refreshList<T extends UpdatableItem>(currentList: T[], nextList: T[]): T[] {\n  return currentList.map((currentItem) => {\n    const updateForCurrentItem = nextList.find(\n      (newItem_) =>\n        // If the IDs match or ..\n        getTrackReferenceId(currentItem) === getTrackReferenceId(newItem_) ||\n        // ... if the current item is a placeholder and the new item is the track reference can replace it.\n        (typeof currentItem !== 'number' &&\n          isTrackReferencePlaceholder(currentItem) &&\n          isTrackReference(newItem_) &&\n          isPlaceholderReplacement(currentItem, newItem_)),\n    );\n    return updateForCurrentItem ?? currentItem;\n  });\n}\n", "import type {\n  AudioCaptureOptions,\n  LocalParticipant,\n  Room,\n  ScreenShareCaptureOptions,\n  TrackPublishOptions,\n  VideoCaptureOptions,\n} from 'livekit-client';\nimport { Track } from 'livekit-client';\nimport type { Observable } from 'rxjs';\nimport { Subject, map, startWith } from 'rxjs';\nimport { observeParticipantMedia } from '../observables/participant';\nimport { prefixClass } from '../styles-interface';\n\nexport type CaptureOptionsBySource<T extends ToggleSource> = T extends Track.Source.Camera\n  ? VideoCaptureOptions\n  : T extends Track.Source.Microphone\n    ? AudioCaptureOptions\n    : T extends Track.Source.ScreenShare\n      ? ScreenShareCaptureOptions\n      : never;\n\nexport type MediaToggleType<T extends ToggleSource> = {\n  pendingObserver: Observable<boolean>;\n  toggle: (\n    forceState?: boolean,\n    captureOptions?: CaptureOptionsBySource<T>,\n  ) => Promise<boolean | undefined>;\n  className: string;\n  enabledObserver: Observable<boolean>;\n};\n\nexport type ToggleSource = Exclude<\n  Track.Source,\n  Track.Source.ScreenShareAudio | Track.Source.Unknown\n>;\n\nexport function setupMediaToggle<T extends ToggleSource>(\n  source: T,\n  room: Room,\n  options?: CaptureOptionsBySource<T>,\n  publishOptions?: TrackPublishOptions,\n  onError?: (error: Error) => void,\n): MediaToggleType<T> {\n  const { localParticipant } = room;\n\n  const getSourceEnabled = (source: ToggleSource, localParticipant: LocalParticipant) => {\n    let isEnabled = false;\n    switch (source) {\n      case Track.Source.Camera:\n        isEnabled = localParticipant.isCameraEnabled;\n        break;\n      case Track.Source.Microphone:\n        isEnabled = localParticipant.isMicrophoneEnabled;\n        break;\n      case Track.Source.ScreenShare:\n        isEnabled = localParticipant.isScreenShareEnabled;\n        break;\n      default:\n        break;\n    }\n    return isEnabled;\n  };\n\n  const enabledObserver = observeParticipantMedia(localParticipant).pipe(\n    map((media) => {\n      return getSourceEnabled(source, media.participant as LocalParticipant);\n    }),\n    startWith(getSourceEnabled(source, localParticipant)),\n  );\n\n  const pendingSubject = new Subject<boolean>();\n  const toggle = async (forceState?: boolean, captureOptions?: CaptureOptionsBySource<T>) => {\n    try {\n      captureOptions ??= options;\n      // trigger observable update\n      pendingSubject.next(true);\n      switch (source) {\n        case Track.Source.Camera:\n          await localParticipant.setCameraEnabled(\n            forceState ?? !localParticipant.isCameraEnabled,\n            captureOptions as VideoCaptureOptions,\n            publishOptions,\n          );\n          return localParticipant.isCameraEnabled;\n        case Track.Source.Microphone:\n          await localParticipant.setMicrophoneEnabled(\n            forceState ?? !localParticipant.isMicrophoneEnabled,\n            captureOptions as AudioCaptureOptions,\n            publishOptions,\n          );\n          return localParticipant.isMicrophoneEnabled;\n        case Track.Source.ScreenShare:\n          await localParticipant.setScreenShareEnabled(\n            forceState ?? !localParticipant.isScreenShareEnabled,\n            captureOptions as ScreenShareCaptureOptions,\n            publishOptions,\n          );\n          return localParticipant.isScreenShareEnabled;\n        default:\n          throw new TypeError('Tried to toggle unsupported source');\n      }\n    } catch (e) {\n      if (onError && e instanceof Error) {\n        onError?.(e);\n        return undefined;\n      } else {\n        throw e;\n      }\n    } finally {\n      pendingSubject.next(false);\n      // trigger observable update\n    }\n  };\n\n  const className: string = prefixClass('button');\n  return {\n    className,\n    toggle,\n    enabledObserver,\n    pendingObserver: pendingSubject.asObservable(),\n  };\n}\n\nexport function setupManualToggle() {\n  let state = false;\n\n  const enabledSubject = new Subject<boolean>();\n\n  const pendingSubject = new Subject<boolean>();\n\n  const toggle = async (forceState?: boolean) => {\n    pendingSubject.next(true);\n    state = forceState ?? !state;\n    enabledSubject.next(state);\n    pendingSubject.next(false);\n  };\n  const className: string = prefixClass('button');\n  return {\n    className,\n    toggle,\n    enabledObserver: enabledSubject.asObservable(),\n    pendingObserver: pendingSubject.asObservable(),\n  };\n}\n", "import type { ParticipantPermission } from '@livekit/protocol';\nimport { Participant, RemoteParticipant, Room, TrackPublication } from 'livekit-client';\nimport { ParticipantEvent, RoomEvent, Track } from 'livekit-client';\n// @ts-ignore some module resolutions (other than 'node') choke on this\nimport type { ParticipantEventCallbacks } from 'livekit-client/dist/src/room/participant/Participant';\nimport type { Subscriber } from 'rxjs';\nimport { Observable, map, startWith, switchMap } from 'rxjs';\nimport { getTrackByIdentifier } from '../components/mediaTrack';\nimport { allParticipantEvents, allParticipantRoomEvents } from '../helper/eventGroups';\nimport type { TrackReferenceOrPlaceholder } from '../track-reference';\nimport type { ParticipantIdentifier, TrackIdentifier } from '../types';\nimport { observeRoomEvents } from './room';\n\nexport function observeParticipantEvents<T extends Participant>(\n  participant: T,\n  ...events: ParticipantEvent[]\n) {\n  const observable = new Observable<T>((subscribe) => {\n    const onParticipantUpdate = () => {\n      subscribe.next(participant);\n    };\n\n    events.forEach((evt) => {\n      participant.on(evt as keyof ParticipantEventCallbacks, onParticipantUpdate);\n    });\n\n    const unsubscribe = () => {\n      events.forEach((evt) => {\n        participant.off(evt as keyof ParticipantEventCallbacks, onParticipantUpdate);\n      });\n    };\n    return unsubscribe;\n  }).pipe(startWith(participant));\n\n  return observable;\n}\n\nexport interface ParticipantMedia<T extends Participant = Participant> {\n  isCameraEnabled: boolean;\n  isMicrophoneEnabled: boolean;\n  isScreenShareEnabled: boolean;\n  microphoneTrack?: TrackPublication;\n  cameraTrack?: TrackPublication;\n  participant: T;\n}\n\nexport function observeParticipantMedia<T extends Participant>(participant: T) {\n  const participantObserver = observeParticipantEvents(\n    participant,\n    ParticipantEvent.TrackMuted,\n    ParticipantEvent.TrackUnmuted,\n    ParticipantEvent.ParticipantPermissionsChanged,\n    // ParticipantEvent.IsSpeakingChanged,\n    ParticipantEvent.TrackPublished,\n    ParticipantEvent.TrackUnpublished,\n    ParticipantEvent.LocalTrackPublished,\n    ParticipantEvent.LocalTrackUnpublished,\n    ParticipantEvent.MediaDevicesError,\n    ParticipantEvent.TrackSubscriptionStatusChanged,\n    // ParticipantEvent.ConnectionQualityChanged,\n  ).pipe(\n    map((p) => {\n      const { isMicrophoneEnabled, isCameraEnabled, isScreenShareEnabled } = p;\n      const microphoneTrack = p.getTrackPublication(Track.Source.Microphone);\n      const cameraTrack = p.getTrackPublication(Track.Source.Camera);\n      const participantMedia: ParticipantMedia<T> = {\n        isCameraEnabled,\n        isMicrophoneEnabled,\n        isScreenShareEnabled,\n        cameraTrack,\n        microphoneTrack,\n        participant: p,\n      };\n      return participantMedia;\n    }),\n  );\n\n  return participantObserver;\n}\n\nexport function createTrackObserver(participant: Participant, options: TrackIdentifier) {\n  return observeParticipantMedia(participant).pipe(\n    map(() => {\n      return { publication: getTrackByIdentifier(options) };\n    }),\n  );\n}\n\nexport function participantInfoObserver(participant?: Participant) {\n  if (!participant) {\n    return undefined;\n  }\n  const observer = observeParticipantEvents(\n    participant,\n    ParticipantEvent.ParticipantMetadataChanged,\n    ParticipantEvent.ParticipantNameChanged,\n  ).pipe(\n    map(({ name, identity, metadata }) => {\n      return {\n        name,\n        identity,\n        metadata,\n      };\n    }),\n    startWith({\n      name: participant.name,\n      identity: participant.identity,\n      metadata: participant.metadata,\n    }),\n  );\n  return observer;\n}\n\nexport function createConnectionQualityObserver(participant: Participant) {\n  const observer = participantEventSelector(\n    participant,\n    ParticipantEvent.ConnectionQualityChanged,\n  ).pipe(\n    map(([quality]) => quality),\n    startWith(participant.connectionQuality),\n  );\n  return observer;\n}\n\nexport function participantEventSelector<T extends ParticipantEvent>(\n  participant: Participant,\n  event: T,\n) {\n  const observable = new Observable<\n    Parameters<ParticipantEventCallbacks[Extract<T, keyof ParticipantEventCallbacks>]>\n  >((subscribe) => {\n    const update = (\n      ...params: Parameters<ParticipantEventCallbacks[Extract<T, keyof ParticipantEventCallbacks>]>\n    ) => {\n      subscribe.next(params);\n    };\n    // @ts-expect-error not a perfect overlap between ParticipantEvent and keyof ParticipantEventCallbacks\n    participant.on(event, update);\n\n    const unsubscribe = () => {\n      // @ts-expect-error not a perfect overlap between ParticipantEvent and keyof ParticipantEventCallbacks\n      participant.off(event, update);\n    };\n    return unsubscribe;\n  });\n\n  return observable;\n}\n\nexport function mutedObserver(trackRef: TrackReferenceOrPlaceholder) {\n  return observeParticipantEvents(\n    trackRef.participant,\n    ParticipantEvent.TrackMuted,\n    ParticipantEvent.TrackUnmuted,\n    ParticipantEvent.TrackSubscribed,\n    ParticipantEvent.TrackUnsubscribed,\n    ParticipantEvent.LocalTrackPublished,\n    ParticipantEvent.LocalTrackUnpublished,\n  ).pipe(\n    map((participant) => {\n      const pub = trackRef.publication ?? participant.getTrackPublication(trackRef.source);\n      return pub?.isMuted ?? true;\n    }),\n    startWith(\n      trackRef.publication?.isMuted ??\n        trackRef.participant.getTrackPublication(trackRef.source)?.isMuted ??\n        true,\n    ),\n  );\n}\n\nexport function createIsSpeakingObserver(participant: Participant) {\n  return participantEventSelector(participant, ParticipantEvent.IsSpeakingChanged).pipe(\n    map(([isSpeaking]) => isSpeaking),\n  );\n}\n\ntype ConnectedParticipantsObserverOptions = {\n  additionalRoomEvents?: RoomEvent[];\n};\n\nexport function connectedParticipantsObserver(\n  room: Room,\n  options: ConnectedParticipantsObserverOptions = {},\n) {\n  let subscriber: Subscriber<RemoteParticipant[]> | undefined;\n\n  const observable = new Observable<RemoteParticipant[]>((sub) => {\n    subscriber = sub;\n    return () => listener.unsubscribe();\n  }).pipe(startWith(Array.from(room.remoteParticipants.values())));\n\n  const additionalRoomEvents = options.additionalRoomEvents ?? allParticipantRoomEvents;\n\n  const roomEvents = Array.from(\n    new Set([\n      RoomEvent.ParticipantConnected,\n      RoomEvent.ParticipantDisconnected,\n      RoomEvent.ConnectionStateChanged,\n      ...additionalRoomEvents,\n    ]),\n  );\n\n  const listener = observeRoomEvents(room, ...roomEvents).subscribe((r) =>\n    subscriber?.next(Array.from(r.remoteParticipants.values())),\n  );\n  if (room.remoteParticipants.size > 0) {\n    subscriber?.next(Array.from(room.remoteParticipants.values()));\n  }\n  return observable;\n}\n\nexport type ConnectedParticipantObserverOptions = {\n  additionalEvents?: ParticipantEvent[];\n};\n\nexport function connectedParticipantObserver(\n  room: Room,\n  identity: string,\n  options: ConnectedParticipantObserverOptions = {},\n) {\n  const additionalEvents = options.additionalEvents ?? allParticipantEvents;\n  const observable = observeRoomEvents(\n    room,\n    RoomEvent.ParticipantConnected,\n    RoomEvent.ParticipantDisconnected,\n    RoomEvent.ConnectionStateChanged,\n  ).pipe(\n    switchMap((r) => {\n      const participant = r.getParticipantByIdentity(identity) as RemoteParticipant | undefined;\n      if (participant) {\n        return observeParticipantEvents(participant, ...additionalEvents);\n      } else {\n        return new Observable<undefined>((subscribe) => subscribe.next(undefined));\n      }\n    }),\n    startWith(room.getParticipantByIdentity(identity) as RemoteParticipant | undefined),\n  );\n\n  return observable;\n}\n\nexport function participantPermissionObserver(\n  participant: Participant,\n): Observable<ParticipantPermission | undefined> {\n  const observer = participantEventSelector(\n    participant,\n    ParticipantEvent.ParticipantPermissionsChanged,\n  ).pipe(\n    map(() => participant.permissions),\n    startWith(participant.permissions),\n  );\n  return observer;\n}\n\nexport function participantByIdentifierObserver(\n  room: Room,\n  { kind, identity }: ParticipantIdentifier,\n  options: ConnectedParticipantObserverOptions = {},\n): Observable<RemoteParticipant | undefined> {\n  const additionalEvents = options.additionalEvents ?? allParticipantEvents;\n  const matchesIdentifier = (participant: RemoteParticipant) => {\n    let isMatch = true;\n    if (kind) {\n      isMatch = isMatch && participant.kind === kind;\n    }\n    if (identity) {\n      isMatch = isMatch && participant.identity === identity;\n    }\n    return isMatch;\n  };\n  const observable = observeRoomEvents(\n    room,\n    RoomEvent.ParticipantConnected,\n    RoomEvent.ParticipantDisconnected,\n    RoomEvent.ConnectionStateChanged,\n  ).pipe(\n    switchMap((r) => {\n      const participant = Array.from(r.remoteParticipants.values()).find((p) =>\n        matchesIdentifier(p),\n      );\n      if (participant) {\n        return observeParticipantEvents(participant, ...additionalEvents);\n      } else {\n        return new Observable<undefined>((subscribe) => subscribe.next(undefined));\n      }\n    }),\n    startWith(Array.from(room.remoteParticipants.values()).find((p) => matchesIdentifier(p))),\n  );\n\n  return observable;\n}\n\nexport function participantAttributesObserver(participant: Participant): Observable<{\n  changed: Readonly<Record<string, string>>;\n  attributes: Readonly<Record<string, string>>;\n}>;\nexport function participantAttributesObserver(participant: undefined): Observable<{\n  changed: undefined;\n  attributes: undefined;\n}>;\nexport function participantAttributesObserver(participant: Participant | undefined) {\n  if (typeof participant === 'undefined') {\n    return new Observable<{ changed: undefined; attributes: undefined }>();\n  }\n  return participantEventSelector(participant, ParticipantEvent.AttributesChanged).pipe(\n    map(([changedAttributes]) => {\n      return {\n        changed: changedAttributes as Readonly<Record<string, string>>,\n        attributes: participant.attributes,\n      };\n    }),\n    startWith({ changed: participant.attributes, attributes: participant.attributes }),\n  );\n}\n", "import { Track } from 'livekit-client';\nimport { map, startWith } from 'rxjs';\nimport { observeParticipantMedia } from '../observables/participant';\nimport { prefixClass } from '../styles-interface';\nimport { isTrackReference } from '../track-reference/track-reference.types';\nimport type { TrackIdentifier } from '../types';\n\nexport function setupMediaTrack(trackIdentifier: TrackIdentifier) {\n  const initialPub = getTrackByIdentifier(trackIdentifier);\n  const trackObserver = observeParticipantMedia(trackIdentifier.participant).pipe(\n    map(() => {\n      return getTrackByIdentifier(trackIdentifier);\n    }),\n    startWith(initialPub),\n  );\n  const className: string = prefixClass(\n    trackIdentifier.source === Track.Source.Camera ||\n      trackIdentifier.source === Track.Source.ScreenShare\n      ? 'participant-media-video'\n      : 'participant-media-audio',\n  );\n  return { className, trackObserver };\n}\n\nexport function getTrackByIdentifier(options: TrackIdentifier) {\n  if (isTrackReference(options)) {\n    return options.publication;\n  } else {\n    const { source, name, participant } = options;\n    if (source && name) {\n      return participant\n        .getTrackPublications()\n        .find((pub) => pub.source === source && pub.trackName === name);\n    } else if (name) {\n      return participant.getTrackPublicationByName(name);\n    } else if (source) {\n      return participant.getTrackPublication(source);\n    } else {\n      throw new Error('At least one of source and name needs to be defined');\n    }\n  }\n}\n", "import type { UnprefixedClassNames as ComponentNoPrefixClasses } from '@livekit/components-styles/dist/types_unprefixed/index.scss';\nimport type { UnprefixedClassNames as PrefabNoPrefixClasses } from '@livekit/components-styles/dist/types_unprefixed/prefabs/index.scss';\nimport { cssPrefix } from './../constants';\n\ntype UnprefixedClassNames = ComponentNoPrefixClasses | PrefabNoPrefixClasses;\n\n/**\n * This function is a type safe way to add a prefix to a HTML class attribute.\n * Only classes defined in the styles module are valid, any other class produces a ts error.\n * @internal\n */\nexport function prefixClass<T extends UnprefixedClassNames>(unprefixedClassName: T) {\n  return `${cssPrefix}-${unprefixedClassName}` as const;\n}\n", "import type { Subscriber, Subscription } from 'rxjs';\nimport { Subject, map, Observable, startWith, finalize, filter, concat } from 'rxjs';\nimport type { Participant, TrackPublication } from 'livekit-client';\nimport { LocalParticipant, Room, RoomEvent, Track } from 'livekit-client';\n// @ts-ignore some module resolutions (other than 'node') choke on this\nimport type { RoomEventCallbacks } from 'livekit-client/dist/src/room/Room';\nimport { log } from '../logger';\nexport function observeRoomEvents(room: Room, ...events: RoomEvent[]): Observable<Room> {\n  const observable = new Observable<Room>((subscribe) => {\n    const onRoomUpdate = () => {\n      subscribe.next(room);\n    };\n\n    events.forEach((evt) => {\n      room.on(evt, onRoomUpdate);\n    });\n\n    const unsubscribe = () => {\n      events.forEach((evt) => {\n        room.off(evt, onRoomUpdate);\n      });\n    };\n    return unsubscribe;\n  }).pipe(startWith(room));\n\n  return observable;\n}\n\nexport function roomEventSelector<T extends RoomEvent>(room: Room, event: T) {\n  const observable = new Observable<Parameters<RoomEventCallbacks[T]>>((subscribe) => {\n    const update = (...params: Parameters<RoomEventCallbacks[T]>) => {\n      subscribe.next(params);\n    };\n    room.on(event as keyof RoomEventCallbacks, update);\n\n    const unsubscribe = () => {\n      room.off(event as keyof RoomEventCallbacks, update);\n    };\n    return unsubscribe;\n  });\n\n  return observable;\n}\n\nexport function roomObserver(room: Room) {\n  const observable = observeRoomEvents(\n    room,\n    RoomEvent.ParticipantConnected,\n    RoomEvent.ParticipantDisconnected,\n    RoomEvent.ActiveSpeakersChanged,\n    RoomEvent.TrackSubscribed,\n    RoomEvent.TrackUnsubscribed,\n    RoomEvent.LocalTrackPublished,\n    RoomEvent.LocalTrackUnpublished,\n    RoomEvent.AudioPlaybackStatusChanged,\n    RoomEvent.ConnectionStateChanged,\n  ).pipe(startWith(room));\n\n  return observable;\n}\n\nexport function connectionStateObserver(room: Room) {\n  return roomEventSelector(room, RoomEvent.ConnectionStateChanged).pipe(\n    map(([connectionState]) => connectionState),\n    startWith(room.state),\n  );\n}\nexport type ScreenShareTrackMap = Array<{\n  participant: Participant;\n  tracks: Array<TrackPublication>;\n}>;\n\nexport function screenShareObserver(room: Room) {\n  let screenShareSubscriber: Subscriber<ScreenShareTrackMap>;\n  const observers: Array<Subscription> = [];\n\n  const observable = new Observable<ScreenShareTrackMap>((subscriber) => {\n    screenShareSubscriber = subscriber;\n    return () => {\n      observers.forEach((observer) => {\n        observer.unsubscribe();\n      });\n    };\n  });\n  const screenShareTracks: ScreenShareTrackMap = [];\n\n  const handleSub = (publication: TrackPublication, participant: Participant) => {\n    if (\n      publication.source !== Track.Source.ScreenShare &&\n      publication.source !== Track.Source.ScreenShareAudio\n    ) {\n      return;\n    }\n    let trackMap = screenShareTracks.find((tr) => tr.participant.identity === participant.identity);\n    const getScreenShareTracks = (participant: Participant) => {\n      return participant\n        .getTrackPublications()\n        .filter(\n          (track) =>\n            (track.source === Track.Source.ScreenShare ||\n              track.source === Track.Source.ScreenShareAudio) &&\n            track.track,\n        );\n    };\n    if (!trackMap) {\n      trackMap = {\n        participant,\n        tracks: getScreenShareTracks(participant),\n      };\n    } else {\n      const index = screenShareTracks.indexOf(trackMap);\n      screenShareTracks.splice(index, 1);\n      trackMap.tracks = getScreenShareTracks(participant);\n    }\n    if (trackMap.tracks.length > 0) {\n      screenShareTracks.push(trackMap);\n    }\n\n    screenShareSubscriber.next(screenShareTracks);\n  };\n  observers.push(\n    roomEventSelector(room, RoomEvent.TrackSubscribed).subscribe(([, ...args]) =>\n      handleSub(...args),\n    ),\n  );\n  observers.push(\n    roomEventSelector(room, RoomEvent.TrackUnsubscribed).subscribe(([, ...args]) =>\n      handleSub(...args),\n    ),\n  );\n  observers.push(\n    roomEventSelector(room, RoomEvent.LocalTrackPublished).subscribe((args) => handleSub(...args)),\n  );\n  observers.push(\n    roomEventSelector(room, RoomEvent.LocalTrackUnpublished).subscribe((args) => {\n      handleSub(...args);\n    }),\n  );\n  observers.push(\n    roomEventSelector(room, RoomEvent.TrackMuted).subscribe((args) => {\n      handleSub(...args);\n    }),\n  );\n  observers.push(\n    roomEventSelector(room, RoomEvent.TrackUnmuted).subscribe((args) => {\n      handleSub(...args);\n    }),\n  );\n  setTimeout(() => {\n    // TODO find way to avoid this timeout\n    for (const p of room.remoteParticipants.values()) {\n      p.getTrackPublications().forEach((track) => {\n        handleSub(track, p);\n      });\n    }\n  }, 1);\n\n  return observable;\n}\n\nexport function roomInfoObserver(room: Room) {\n  const observer = observeRoomEvents(\n    room,\n    RoomEvent.RoomMetadataChanged,\n    RoomEvent.ConnectionStateChanged,\n  ).pipe(\n    map((r) => {\n      return { name: r.name, metadata: r.metadata };\n    }),\n  );\n  return observer;\n}\n\nexport function activeSpeakerObserver(room: Room) {\n  return roomEventSelector(room, RoomEvent.ActiveSpeakersChanged).pipe(\n    map(([speakers]) => speakers),\n  );\n}\n\nexport function createMediaDeviceObserver(\n  kind?: MediaDeviceKind,\n  onError?: (e: Error) => void,\n  requestPermissions = true,\n) {\n  const onDeviceChange = async () => {\n    try {\n      const newDevices = await Room.getLocalDevices(kind, requestPermissions);\n      deviceSubject.next(newDevices);\n    } catch (e: any) {\n      onError?.(e);\n    }\n  };\n  const deviceSubject = new Subject<MediaDeviceInfo[]>();\n\n  const observable = deviceSubject.pipe(\n    finalize(() => {\n      navigator?.mediaDevices?.removeEventListener('devicechange', onDeviceChange);\n    }),\n  );\n\n  if (typeof window !== 'undefined') {\n    if (!window.isSecureContext) {\n      throw new Error(\n        `Accessing media devices is available only in secure contexts (HTTPS and localhost), in some or all supporting browsers. See: https://developer.mozilla.org/en-US/docs/Web/API/Navigator/mediaDevices`,\n      );\n    }\n    navigator?.mediaDevices?.addEventListener('devicechange', onDeviceChange);\n  }\n  // because we rely on an async function, concat the promise to retrieve the initial values with the observable\n  return concat(\n    Room.getLocalDevices(kind, requestPermissions).catch((e) => {\n      onError?.(e);\n      return [] as MediaDeviceInfo[];\n    }),\n    observable,\n  );\n}\n\nexport function createDataObserver(room: Room) {\n  return roomEventSelector(room, RoomEvent.DataReceived);\n}\n\nexport function createChatObserver(room: Room) {\n  return roomEventSelector(room, RoomEvent.ChatMessage);\n}\n\nexport function roomAudioPlaybackAllowedObservable(room: Room) {\n  const observable = observeRoomEvents(room, RoomEvent.AudioPlaybackStatusChanged).pipe(\n    map((room) => {\n      return { canPlayAudio: room.canPlaybackAudio };\n    }),\n  );\n  return observable;\n}\n\nexport function roomVideoPlaybackAllowedObservable(room: Room) {\n  const observable = observeRoomEvents(room, RoomEvent.VideoPlaybackStatusChanged).pipe(\n    map((room) => {\n      return { canPlayVideo: room.canPlaybackVideo };\n    }),\n  );\n  return observable;\n}\n\nexport function createActiveDeviceObservable(room: Room, kind: MediaDeviceKind) {\n  return roomEventSelector(room, RoomEvent.ActiveDeviceChanged).pipe(\n    filter(([kindOfDevice]) => kindOfDevice === kind),\n    map(([kind, deviceId]) => {\n      log.debug('activeDeviceObservable | RoomEvent.ActiveDeviceChanged', { kind, deviceId });\n      return deviceId;\n    }),\n  );\n}\n\nexport function encryptionStatusObservable(room: Room, participant: Participant | undefined) {\n  return roomEventSelector(room, RoomEvent.ParticipantEncryptionStatusChanged).pipe(\n    filter(\n      ([, p]) =>\n        participant?.identity === p?.identity ||\n        (!p && participant?.identity === room.localParticipant.identity),\n    ),\n    map(([encrypted]) => encrypted),\n    startWith(\n      participant?.isLocal\n        ? (participant as LocalParticipant).isE2EEEnabled\n        : !!participant?.isEncrypted,\n    ),\n  );\n}\n\nexport function recordingStatusObservable(room: Room) {\n  return roomEventSelector(room, RoomEvent.RecordingStatusChanged).pipe(\n    map(([recording]) => recording),\n    startWith(room.isRecording),\n  );\n}\n", "import {\n  Track,\n  type LocalAudioTrack,\n  type LocalVideoTrack,\n  type Room,\n  type LocalTrack,\n} from 'livekit-client';\nimport { BehaviorSubject } from 'rxjs';\nimport { log } from '../logger';\nimport { prefixClass } from '../styles-interface';\nimport { createActiveDeviceObservable } from '../observables/room';\n\nexport type SetMediaDeviceOptions = {\n  /**\n   *  If true, adds an `exact` constraint to the getUserMedia request.\n   *  The request will fail if this option is true and the device specified is not actually available\n   */\n  exact?: boolean;\n};\n\nexport function setupDeviceSelector(\n  kind: MediaDeviceKind,\n  room: Room,\n  localTrack?: LocalAudioTrack | LocalVideoTrack,\n) {\n  const activeDeviceSubject = new BehaviorSubject<string | undefined>(undefined);\n\n  const activeDeviceObservable = createActiveDeviceObservable(room, kind);\n\n  const setActiveMediaDevice = async (id: string, options: SetMediaDeviceOptions = {}) => {\n    if (localTrack) {\n      await localTrack.setDeviceId(options.exact ? { exact: id } : id);\n      const actualId = await localTrack.getDeviceId(false);\n      activeDeviceSubject.next(\n        id === 'default' && localTrack.mediaStreamTrack.label.startsWith('Default') ? id : actualId,\n      );\n    } else if (room) {\n      log.debug(`Switching active device of kind \"${kind}\" with id ${id}.`);\n      await room.switchActiveDevice(kind, id, options.exact);\n      const actualDeviceId: string | undefined = room.getActiveDevice(kind) ?? id;\n      if (actualDeviceId !== id && id !== 'default') {\n        log.info(\n          `We tried to select the device with id (${id}), but the browser decided to select the device with id (${actualDeviceId}) instead.`,\n        );\n      }\n      let targetTrack: LocalTrack | undefined = undefined;\n      if (kind === 'audioinput')\n        targetTrack = room.localParticipant.getTrackPublication(Track.Source.Microphone)?.track;\n      else if (kind === 'videoinput') {\n        targetTrack = room.localParticipant.getTrackPublication(Track.Source.Camera)?.track;\n      }\n      const useDefault =\n        (id === 'default' && !targetTrack) ||\n        (id === 'default' && targetTrack?.mediaStreamTrack.label.startsWith('Default'));\n      activeDeviceSubject.next(useDefault ? id : actualDeviceId);\n    }\n  };\n  const className: string = prefixClass('media-device-select');\n  return {\n    className,\n    activeDeviceObservable,\n    setActiveMediaDevice,\n  };\n}\n", "import type { Room } from 'livekit-client';\nimport { prefixClass } from '../styles-interface';\n\nexport function setupDisconnectButton(room: Room) {\n  const disconnect = (stopTracks?: boolean) => {\n    room.disconnect(stopTracks);\n  };\n  const className: string = prefixClass('disconnect-button');\n  return { className, disconnect };\n}\n", "import type { Participant } from 'livekit-client';\nimport { createConnectionQualityObserver } from '../observables/participant';\nimport { prefixClass } from '../styles-interface';\n\nexport function setupConnectionQualityIndicator(participant: Participant) {\n  const className = prefixClass('connection-quality');\n  const connectionQualityObserver = createConnectionQualityObserver(participant);\n  return { className, connectionQualityObserver };\n}\n", "// @ts-ignore some module resolutions (other than 'node') choke on this\nimport type { Styles } from '@livekit/components-styles/dist/types_unprefixed/index.scss';\nimport { Track } from 'livekit-client';\nimport { mutedObserver } from '../observables/participant';\nimport { prefixClass } from '../styles-interface';\nimport type { TrackReferenceOrPlaceholder } from '../track-reference';\n\nexport function setupTrackMutedIndicator(trackRef: TrackReferenceOrPlaceholder) {\n  let classForSource: keyof Styles = 'track-muted-indicator-camera';\n  switch (trackRef.source) {\n    case Track.Source.Camera:\n      classForSource = 'track-muted-indicator-camera';\n      break;\n    case Track.Source.Microphone:\n      classForSource = 'track-muted-indicator-microphone';\n      break;\n\n    default:\n      break;\n  }\n  const className: string = prefixClass(classForSource);\n  const mediaMutedObserver = mutedObserver(trackRef);\n\n  return { className, mediaMutedObserver };\n}\n", "import type { Participant } from 'livekit-client';\nimport { participantInfoObserver } from '../observables/participant';\n\nexport function setupParticipantName(participant: Participant) {\n  const infoObserver = participantInfoObserver(participant);\n  return { className: 'lk-participant-name', infoObserver };\n}\n", "import { prefixClass } from '../styles-interface';\n\nexport function setupParticipantTile() {\n  const className: string = prefixClass('participant-tile');\n  return {\n    className,\n  };\n}\n", "/* eslint-disable camelcase */\nimport type { Participant, Room, ChatMessage } from 'livekit-client';\nimport { compareVersions, RoomEvent } from 'livekit-client';\nimport { BehaviorSubject, Subject, scan, map, takeUntil, merge } from 'rxjs';\nimport {\n  DataTopic,\n  sendMessage,\n  setupChatMessageHandler,\n  setupDataMessageHandler,\n} from '../observables/dataChannel';\n\n/** @public */\nexport type { ChatMessage };\n\n/** @public */\nexport interface ReceivedChatMessage extends ChatMessage {\n  from?: Participant;\n}\n\nexport interface LegacyChatMessage extends ChatMessage {\n  ignore?: boolean;\n}\n\nexport interface LegacyReceivedChatMessage extends ReceivedChatMessage {\n  ignore?: boolean;\n}\n\n/**\n * @public\n * @deprecated the new chat API doesn't rely on encoders and decoders anymore and uses a dedicated chat API instead\n */\nexport type MessageEncoder = (message: LegacyChatMessage) => Uint8Array;\n/**\n * @public\n * @deprecated the new chat API doesn't rely on encoders and decoders anymore and uses a dedicated chat API instead\n */\nexport type MessageDecoder = (message: Uint8Array) => LegacyReceivedChatMessage;\n/** @public */\nexport type ChatOptions = {\n  /** @deprecated the new chat API doesn't rely on encoders and decoders anymore and uses a dedicated chat API instead */\n  messageEncoder?: (message: LegacyChatMessage) => Uint8Array;\n  /** @deprecated the new chat API doesn't rely on encoders and decoders anymore and uses a dedicated chat API instead */\n  messageDecoder?: (message: Uint8Array) => LegacyReceivedChatMessage;\n  /** @deprecated the new chat API doesn't rely on topics anymore and uses a dedicated chat API instead */\n  channelTopic?: string;\n  /** @deprecated the new chat API doesn't rely on topics anymore and uses a dedicated chat API instead */\n  updateChannelTopic?: string;\n};\n\ntype RawMessage = {\n  payload: Uint8Array;\n  topic: string | undefined;\n  from: Participant | undefined;\n};\n\nconst encoder = new TextEncoder();\nconst decoder = new TextDecoder();\n\nconst topicSubjectMap: Map<Room, Map<string, Subject<RawMessage>>> = new Map();\n\nconst encode = (message: LegacyReceivedChatMessage) => encoder.encode(JSON.stringify(message));\n\nconst decode = (message: Uint8Array) =>\n  JSON.parse(decoder.decode(message)) as LegacyReceivedChatMessage | ReceivedChatMessage;\n\nexport function setupChat(room: Room, options?: ChatOptions) {\n  const onDestroyObservable = new Subject<void>();\n\n  const serverSupportsChatApi = () =>\n    room.serverInfo?.edition === 1 ||\n    (!!room.serverInfo?.version && compareVersions(room.serverInfo?.version, '1.17.2') > 0);\n\n  const { messageDecoder, messageEncoder, channelTopic, updateChannelTopic } = options ?? {};\n\n  const topic = channelTopic ?? DataTopic.CHAT;\n\n  const updateTopic = updateChannelTopic ?? DataTopic.CHAT_UPDATE;\n\n  let needsSetup = false;\n  if (!topicSubjectMap.has(room)) {\n    needsSetup = true;\n  }\n  const topicMap = topicSubjectMap.get(room) ?? new Map<string, Subject<RawMessage>>();\n  const messageSubject = topicMap.get(topic) ?? new Subject<RawMessage>();\n  topicMap.set(topic, messageSubject);\n  topicSubjectMap.set(room, topicMap);\n\n  if (needsSetup) {\n    /** Subscribe to all appropriate messages sent over the wire. */\n    const { messageObservable } = setupDataMessageHandler(room, [topic, updateTopic]);\n    messageObservable.pipe(takeUntil(onDestroyObservable)).subscribe(messageSubject);\n  }\n  const { chatObservable, send: sendChatMessage } = setupChatMessageHandler(room);\n\n  const finalMessageDecoder = messageDecoder ?? decode;\n\n  /** Build up the message array over time. */\n  const messagesObservable = merge(\n    messageSubject.pipe(\n      map((msg) => {\n        const parsedMessage = finalMessageDecoder(msg.payload);\n        const newMessage = { ...parsedMessage, from: msg.from };\n        if (isIgnorableChatMessage(newMessage)) {\n          return undefined;\n        }\n        return newMessage;\n      }),\n    ),\n    chatObservable.pipe(\n      map(([msg, participant]) => {\n        return { ...msg, from: participant };\n      }),\n    ),\n  ).pipe(\n    scan<ReceivedChatMessage | undefined, ReceivedChatMessage[]>((acc, value) => {\n      // ignore legacy message updates\n      if (!value) {\n        return acc;\n      }\n      // handle message updates\n      if (\n        'id' in value &&\n        acc.find((msg) => msg.from?.identity === value.from?.identity && msg.id === value.id)\n      ) {\n        const replaceIndex = acc.findIndex((msg) => msg.id === value.id);\n        if (replaceIndex > -1) {\n          const originalMsg = acc[replaceIndex];\n          acc[replaceIndex] = {\n            ...value,\n            timestamp: originalMsg.timestamp,\n            editTimestamp: value.editTimestamp ?? value.timestamp,\n          };\n        }\n\n        return [...acc];\n      }\n      return [...acc, value];\n    }, []),\n    takeUntil(onDestroyObservable),\n  );\n\n  const isSending$ = new BehaviorSubject<boolean>(false);\n\n  const finalMessageEncoder = messageEncoder ?? encode;\n\n  const send = async (message: string) => {\n    isSending$.next(true);\n    try {\n      const chatMessage = await sendChatMessage(message);\n      const encodedLegacyMsg = finalMessageEncoder({\n        ...chatMessage,\n        ignore: serverSupportsChatApi(),\n      });\n      await sendMessage(room.localParticipant, encodedLegacyMsg, {\n        reliable: true,\n        topic,\n      });\n      return chatMessage;\n    } finally {\n      isSending$.next(false);\n    }\n  };\n\n  const update = async (message: string, originalMessageOrId: string | ChatMessage) => {\n    const timestamp = Date.now();\n    const originalMessage: ChatMessage =\n      typeof originalMessageOrId === 'string'\n        ? { id: originalMessageOrId, message: '', timestamp }\n        : originalMessageOrId;\n    isSending$.next(true);\n    try {\n      const editedMessage = await room.localParticipant.editChatMessage(message, originalMessage);\n      const encodedLegacyMessage = finalMessageEncoder(editedMessage);\n      await sendMessage(room.localParticipant, encodedLegacyMessage, {\n        topic: updateTopic,\n        reliable: true,\n      });\n      return editedMessage;\n    } finally {\n      isSending$.next(false);\n    }\n  };\n\n  function destroy() {\n    onDestroyObservable.next();\n    onDestroyObservable.complete();\n    topicSubjectMap.delete(room);\n  }\n  room.once(RoomEvent.Disconnected, destroy);\n\n  return {\n    messageObservable: messagesObservable,\n    isSendingObservable: isSending$,\n    send,\n    update,\n  };\n}\n\nfunction isIgnorableChatMessage(\n  msg: ReceivedChatMessage | LegacyReceivedChatMessage,\n): msg is ReceivedChatMessage {\n  return (msg as LegacyChatMessage).ignore == true;\n}\n", "import type {\n  ChatMessage,\n  DataPublishOptions,\n  LocalParticipant,\n  Participant,\n  Room,\n} from 'livekit-client';\nimport type { Subscriber } from 'rxjs';\nimport { Observable, filter, map } from 'rxjs';\nimport { createChatObserver, createDataObserver } from './room';\n\nexport const DataTopic = {\n  CHAT: 'lk-chat-topic',\n  CHAT_UPDATE: 'lk-chat-update-topic',\n} as const;\n\n/** Publish data from the LocalParticipant. */\nexport async function sendMessage(\n  localParticipant: LocalParticipant,\n  payload: Uint8Array,\n  options: DataPublishOptions = {},\n) {\n  const { reliable, destinationIdentities, topic } = options;\n\n  await localParticipant.publishData(payload, {\n    destinationIdentities,\n    topic,\n    reliable,\n  });\n}\n\nexport interface BaseDataMessage<T extends string | undefined> {\n  topic?: T;\n  payload: Uint8Array;\n}\n\nexport interface ReceivedDataMessage<T extends string | undefined = string>\n  extends BaseDataMessage<T> {\n  from?: Participant;\n}\n\nexport function setupDataMessageHandler<T extends string>(\n  room: Room,\n  topic?: T | [T, ...T[]],\n  onMessage?: (msg: ReceivedDataMessage<T>) => void,\n) {\n  const topics = Array.isArray(topic) ? topic : [topic];\n  /** Setup a Observable that returns all data messages belonging to a topic. */\n  const messageObservable = createDataObserver(room).pipe(\n    filter(\n      ([, , , messageTopic]) =>\n        topic === undefined || (messageTopic !== undefined && topics.includes(messageTopic as T)),\n    ),\n    map(([payload, participant, , messageTopic]) => {\n      const msg = {\n        payload,\n        topic: messageTopic as T,\n        from: participant,\n      } satisfies ReceivedDataMessage<T>;\n      onMessage?.(msg);\n      return msg;\n    }),\n  );\n\n  let isSendingSubscriber: Subscriber<boolean>;\n  const isSendingObservable = new Observable<boolean>((subscriber) => {\n    isSendingSubscriber = subscriber;\n  });\n\n  const send = async (payload: Uint8Array, options: DataPublishOptions = {}) => {\n    isSendingSubscriber.next(true);\n    try {\n      await sendMessage(room.localParticipant, payload, { topic: topics[0], ...options });\n    } finally {\n      isSendingSubscriber.next(false);\n    }\n  };\n\n  return { messageObservable, isSendingObservable, send };\n}\n\nexport function setupChatMessageHandler(room: Room) {\n  const chatObservable = createChatObserver(room);\n\n  const send = async (text: string) => {\n    const msg = await room.localParticipant.sendChatMessage(text);\n    return msg;\n  };\n\n  const edit = async (text: string, originalMsg: ChatMessage) => {\n    const msg = await room.localParticipant.editChatMessage(text, originalMsg);\n    return msg;\n  };\n\n  return { chatObservable, send, edit };\n}\n", "import type { Room } from 'livekit-client';\nimport { log } from '../logger';\nimport { roomAudioPlaybackAllowedObservable } from '../observables/room';\nimport { prefixClass } from '../styles-interface';\n\nexport function setupStartAudio() {\n  const handleStartAudioPlayback = async (room: Room) => {\n    log.info('Start Audio for room: ', room);\n    await room.startAudio();\n  };\n  const className: string = prefixClass('start-audio-button');\n  return { className, roomAudioPlaybackAllowedObservable, handleStartAudioPlayback };\n}\n", "import type { Room } from 'livekit-client';\nimport { log } from '../logger';\nimport { roomVideoPlaybackAllowedObservable } from '../observables/room';\nimport { prefixClass } from '../styles-interface';\n\nexport function setupStartVideo() {\n  const handleStartVideoPlayback = async (room: Room) => {\n    log.info('Start Video for room: ', room);\n    await room.startVideo();\n  };\n  const className: string = prefixClass('start-audio-button');\n  return { className, roomVideoPlaybackAllowedObservable, handleStartVideoPlayback };\n}\n", "import { prefixClass } from '../styles-interface';\n\nexport function setupChatToggle() {\n  const className: string = [prefixClass('button'), prefixClass('chat-toggle')].join(' ');\n  return { className };\n}\n", "import { prefixClass } from '../styles-interface';\n\nexport function setupFocusToggle() {\n  const className: string = [prefixClass('button'), prefixClass('focus-toggle-button')].join(' ');\n  return { className };\n}\n", "// import { prefixClass } from '../styles-interface';\n\nexport function setupClearPinButton() {\n  // const className = prefixClass('clear-pin-button');\n  const className = 'lk-clear-pin-button lk-button';\n  return { className };\n}\n", "export function setupLiveKitRoom() {\n  const className = 'lk-room-container';\n  return { className };\n}\n", "import type {\n  LocalTrackPublication,\n  Participant,\n  RemoteTrackPublication,\n  Room,\n  Track,\n  TrackPublication,\n} from 'livekit-client';\nimport { RoomEvent, TrackEvent } from 'livekit-client';\nimport { map, Observable, startWith } from 'rxjs';\nimport { allParticipantRoomEvents, participantTrackEvents } from '../helper';\nimport { log } from '../logger';\nimport type { TrackReference } from '../track-reference';\nimport { observeRoomEvents } from './room';\nimport type { ParticipantTrackIdentifier } from '../types';\nimport { observeParticipantEvents } from './participant';\n// @ts-ignore some module resolutions (other than 'node') choke on this\nimport type { PublicationEventCallbacks } from 'livekit-client/dist/src/room/track/TrackPublication';\n\nexport function trackObservable(track: TrackPublication) {\n  const trackObserver = observeTrackEvents(\n    track,\n    TrackEvent.Muted,\n    TrackEvent.Unmuted,\n    TrackEvent.Subscribed,\n    TrackEvent.Unsubscribed,\n  );\n\n  return trackObserver;\n}\n\nexport function observeTrackEvents(track: TrackPublication, ...events: TrackEvent[]) {\n  const observable = new Observable<TrackPublication>((subscribe) => {\n    const onTrackUpdate = () => {\n      subscribe.next(track);\n    };\n\n    events.forEach((evt) => {\n      // @ts-expect-error type of `TrackEvent` and `PublicationCallbacks` are congruent\n      track.on(evt, onTrackUpdate);\n    });\n\n    const unsubscribe = () => {\n      events.forEach((evt) => {\n        // @ts-expect-error type of `TrackEvent` and `PublicationCallbacks` are congruent\n        track.off(evt, onTrackUpdate);\n      });\n    };\n    return unsubscribe;\n  }).pipe(startWith(track));\n\n  return observable;\n}\n\n/**\n * Create `TrackReferences` for all tracks that are included in the sources property.\n *  */\nfunction getTrackReferences(\n  room: Room,\n  sources: Track.Source[],\n  onlySubscribedTracks = true,\n): { trackReferences: TrackReference[]; participants: Participant[] } {\n  const localParticipant = room.localParticipant;\n  const allParticipants = [localParticipant, ...Array.from(room.remoteParticipants.values())];\n  const trackReferences: TrackReference[] = [];\n\n  allParticipants.forEach((participant) => {\n    sources.forEach((source) => {\n      const sourceReferences = Array.from<RemoteTrackPublication | LocalTrackPublication>(\n        participant.trackPublications.values(),\n      )\n        .filter(\n          (track) =>\n            track.source === source &&\n            // either return all or only the ones that are subscribed\n            (!onlySubscribedTracks || track.track),\n        )\n        .map((track): TrackReference => {\n          return {\n            participant: participant,\n            publication: track,\n            source: track.source,\n          };\n        });\n\n      trackReferences.push(...sourceReferences);\n    });\n  });\n\n  return { trackReferences, participants: allParticipants };\n}\n\n/**\n * Create `TrackReferences` for all tracks that are included in the sources property.\n *  */\nfunction getParticipantTrackRefs(\n  participant: Participant,\n  identifier: ParticipantTrackIdentifier,\n  onlySubscribedTracks = false,\n): TrackReference[] {\n  const { sources, kind, name } = identifier;\n  const sourceReferences = Array.from(participant.trackPublications.values())\n    .filter(\n      (pub) =>\n        (!sources || sources.includes(pub.source)) &&\n        (!kind || pub.kind === kind) &&\n        (!name || pub.trackName === name) &&\n        // either return all or only the ones that are subscribed\n        (!onlySubscribedTracks || pub.track),\n    )\n    .map((track): TrackReference => {\n      return {\n        participant: participant,\n        publication: track,\n        source: track.source,\n      };\n    });\n\n  return sourceReferences;\n}\n\ntype TrackReferencesObservableOptions = {\n  additionalRoomEvents?: RoomEvent[];\n  onlySubscribed?: boolean;\n};\n\nexport function trackReferencesObservable(\n  room: Room,\n  sources: Track.Source[],\n  options: TrackReferencesObservableOptions,\n): Observable<{ trackReferences: TrackReference[]; participants: Participant[] }> {\n  const additionalRoomEvents = options.additionalRoomEvents ?? allParticipantRoomEvents;\n  const onlySubscribedTracks: boolean = options.onlySubscribed ?? true;\n  const roomEvents = Array.from(\n    new Set([\n      RoomEvent.ParticipantConnected,\n      RoomEvent.ParticipantDisconnected,\n      RoomEvent.ConnectionStateChanged,\n      RoomEvent.LocalTrackPublished,\n      RoomEvent.LocalTrackUnpublished,\n      RoomEvent.TrackPublished,\n      RoomEvent.TrackUnpublished,\n      RoomEvent.TrackSubscriptionStatusChanged,\n      ...additionalRoomEvents,\n    ]).values(),\n  );\n\n  const observable = observeRoomEvents(room, ...roomEvents).pipe(\n    map((room) => {\n      const data = getTrackReferences(room, sources, onlySubscribedTracks);\n      log.debug(`TrackReference[] was updated. (length ${data.trackReferences.length})`, data);\n      return data;\n    }),\n    startWith(getTrackReferences(room, sources, onlySubscribedTracks)),\n  );\n\n  return observable;\n}\n\nexport function participantTracksObservable(\n  participant: Participant,\n  trackIdentifier: ParticipantTrackIdentifier,\n): Observable<TrackReference[]> {\n  const observable = observeParticipantEvents(participant, ...participantTrackEvents).pipe(\n    map((participant) => {\n      const data = getParticipantTrackRefs(participant, trackIdentifier);\n      log.debug(`TrackReference[] was updated. (length ${data.length})`, data);\n      return data;\n    }),\n    startWith(getParticipantTrackRefs(participant, trackIdentifier)),\n  );\n\n  return observable;\n}\n\nexport function trackEventSelector<T extends TrackEvent>(\n  publication: TrackPublication | Track,\n  event: T,\n) {\n  const observable = new Observable<\n    Parameters<PublicationEventCallbacks[Extract<T, keyof PublicationEventCallbacks>]>\n  >((subscribe) => {\n    const update = (\n      ...params: Parameters<PublicationEventCallbacks[Extract<T, keyof PublicationEventCallbacks>]>\n    ) => {\n      subscribe.next(params);\n    };\n    // @ts-expect-error not a perfect overlap between TrackEvent and keyof TrackEventCallbacks\n    publication.on(event, update);\n\n    const unsubscribe = () => {\n      // @ts-expect-error not a perfect overlap between TrackEvent and keyof TrackEventCallbacks\n      publication.off(event, update);\n    };\n    return unsubscribe;\n  });\n\n  return observable;\n}\n\nexport function trackTranscriptionObserver(publication: TrackPublication) {\n  return trackEventSelector(publication, TrackEvent.TranscriptionReceived);\n}\n\nexport function trackSyncTimeObserver(track: Track) {\n  return trackEventSelector(track, TrackEvent.TimeSyncUpdate).pipe(\n    map(([timeUpdate]) => timeUpdate),\n  );\n}\n", "import type { Observable } from 'rxjs';\nimport { concat, distinctUntilChanged, fromEvent, map, of, skipUntil, timeout } from 'rxjs';\n\n/**\n * Returns true if the user is interacting with the HTML element,\n * and returns false if there is no interaction for a specified period of time.\n *\n * @internal\n */\nexport function createInteractingObservable(htmlElement: HTMLElement | null, inactiveAfter = 1000) {\n  if (htmlElement === null) return of(false);\n  const move$ = fromEvent(htmlElement, 'mousemove', { passive: true }).pipe(map(() => true));\n  const moveAndStop$: Observable<boolean> = move$.pipe(\n    timeout({\n      each: inactiveAfter,\n      with: () => concat(of(false), moveAndStop$.pipe(skipUntil(move$))),\n    }),\n    distinctUntilChanged(),\n  );\n  return moveAndStop$;\n}\n", "import { log } from '../logger';\n\ntype JsonPrimitive = string | number | boolean | null;\ntype JsonArray = JsonValue[];\ntype JsonObject = { [key: string]: JsonValue };\ntype JsonValue = JsonPrimitive | JsonArray | JsonObject;\n\n/**\n * Persists a serializable object to local storage associated with the specified key.\n * @internal\n */\nfunction saveToLocalStorage<T extends JsonValue>(key: string, value: T): void {\n  if (typeof localStorage === 'undefined') {\n    log.error('Local storage is not available.');\n    return;\n  }\n\n  try {\n    if (value) {\n      const nonEmptySettings = Object.fromEntries(\n        Object.entries(value).filter(([, value]) => value !== ''),\n      );\n      localStorage.setItem(key, JSON.stringify(nonEmptySettings));\n    }\n  } catch (error) {\n    log.error(`Error setting item to local storage: ${error}`);\n  }\n}\n\n/**\n * Retrieves a serializable object from local storage by its key.\n * @internal\n */\nfunction loadFromLocalStorage<T extends JsonValue>(key: string): T | undefined {\n  if (typeof localStorage === 'undefined') {\n    log.error('Local storage is not available.');\n    return undefined;\n  }\n\n  try {\n    const item = localStorage.getItem(key);\n    if (!item) {\n      log.warn(`Item with key ${key} does not exist in local storage.`);\n      return undefined;\n    }\n    return JSON.parse(item);\n  } catch (error) {\n    log.error(`Error getting item from local storage: ${error}`);\n    return undefined;\n  }\n}\n\n/**\n * Generate a pair of functions to load and save a value of type T to local storage.\n * @internal\n */\nexport function createLocalStorageInterface<T extends JsonValue>(\n  key: string,\n): { load: () => T | undefined; save: (value: T) => void } {\n  return {\n    load: () => loadFromLocalStorage<T>(key),\n    save: (value: T) => saveToLocalStorage<T>(key, value),\n  };\n}\n", "import { cssPrefix } from '../constants';\nimport { createLocalStorageInterface } from './local-storage-helpers';\n\nconst USER_CHOICES_KEY = `${cssPrefix}-user-choices` as const;\n\n/**\n * @public\n * Represents the user's choices for video and audio input devices,\n * as well as their username.\n */\nexport type LocalUserChoices = {\n  /**\n   * Whether video input is enabled.\n   * @defaultValue `true`\n   */\n  videoEnabled: boolean;\n  /**\n   * Whether audio input is enabled.\n   * @defaultValue `true`\n   */\n  audioEnabled: boolean;\n  /**\n   * The device ID of the video input device to use.\n   * @defaultValue `''`\n   */\n  videoDeviceId: string;\n  /**\n   * The device ID of the audio input device to use.\n   * @defaultValue `''`\n   */\n  audioDeviceId: string;\n  /**\n   * The username to use.\n   * @defaultValue `''`\n   */\n  username: string;\n};\n\nexport const defaultUserChoices: LocalUserChoices = {\n  videoEnabled: true,\n  audioEnabled: true,\n  videoDeviceId: 'default',\n  audioDeviceId: 'default',\n  username: '',\n} as const;\n\n/**\n * The type of the object stored in local storage.\n * @remarks\n * TODO: Replace this type with `LocalUserChoices` after removing the deprecated properties from `LocalUserChoices`.\n * @internal\n */\ntype TempStorageType = Omit<LocalUserChoices, 'e2ee' | 'sharedPassphrase'>;\nconst { load, save } = createLocalStorageInterface<TempStorageType>(USER_CHOICES_KEY);\n\n/**\n * Saves user choices to local storage.\n * @alpha\n */\nexport function saveUserChoices(\n  userChoices: LocalUserChoices,\n  /**\n   * Whether to prevent saving user choices to local storage.\n   */\n  preventSave: boolean = false,\n): void {\n  if (preventSave === true) {\n    return;\n  }\n  save(userChoices);\n}\n\n/**\n * Reads the user choices from local storage, or returns the default settings if none are found.\n * @remarks\n * The deprecated parameters `e2ee` and `sharedPassphrase` are not read from local storage\n * and always return the value from the passed `defaults` or internal defaults.\n * @alpha\n */\nexport function loadUserChoices(\n  defaults?: Partial<LocalUserChoices>,\n  /**\n   * Whether to prevent loading from local storage and return default values instead.\n   * @defaultValue false\n   */\n  preventLoad: boolean = false,\n): LocalUserChoices {\n  const fallback: LocalUserChoices = {\n    videoEnabled: defaults?.videoEnabled ?? defaultUserChoices.videoEnabled,\n    audioEnabled: defaults?.audioEnabled ?? defaultUserChoices.audioEnabled,\n    videoDeviceId: defaults?.videoDeviceId ?? defaultUserChoices.videoDeviceId,\n    audioDeviceId: defaults?.audioDeviceId ?? defaultUserChoices.audioDeviceId,\n    username: defaults?.username ?? defaultUserChoices.username,\n  };\n\n  if (preventLoad) {\n    return fallback;\n  } else {\n    const maybeLoadedObject = load();\n    const result = { ...fallback, ...(maybeLoadedObject ?? {}) };\n    return result;\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAO,IAAM,YAAY;;;AC2BlB,SAAS,iBAAiB,gBAA2D;AAC1F,MAAI,OAAO,mBAAmB,aAAa;AACzC,WAAO;AAAA,EACT;AACA,SACE,2BAA2B,cAAgC,KAC3D,0BAA0B,cAAgC;AAE9D;AAEA,SAAS,2BAA2B,gBAAuD;AArC3F;AAsCE,MAAI,CAAC,gBAAgB;AACnB,WAAO;AAAA,EACT;AACA,SACE,eAAe,eAAe,aAAa,KAC3C,eAAe,eAAe,QAAQ,KACtC,eAAe,eAAe,OAAO,KACrC,SAAO,oBAAe,gBAAf,mBAA4B,WAAU;AAEjD;AAEA,SAAS,0BAA0B,gBAAuD;AACxF,MAAI,CAAC,gBAAgB;AACnB,WAAO;AAAA,EACT;AACA,SACE,eAAe,eAAe,aAAa,KAC3C,eAAe,eAAe,QAAQ,KACtC,eAAe,eAAe,aAAa,KAC3C,OAAO,eAAe,gBAAgB;AAE1C;AAEO,SAAS,4BACd,gBAC6C;AAC7C,MAAI,CAAC,gBAAgB;AACnB,WAAO;AAAA,EACT;AACA,SACE,eAAe,eAAe,aAAa,KAC3C,eAAe,eAAe,QAAQ,KACtC,OAAO,eAAe,gBAAgB;AAE1C;;;AC5DO,SAAS,oBAAoB,gBAAsD;AACxF,MAAI,OAAO,mBAAmB,YAAY,OAAO,mBAAmB,UAAU;AAC5E,WAAO,GAAG,cAAc;AAAA,EAC1B,WAAW,4BAA4B,cAAc,GAAG;AACtD,WAAO,GAAG,eAAe,YAAY,QAAQ,IAAI,eAAe,MAAM;AAAA,EACxE,WAAW,iBAAiB,cAAc,GAAG;AAC3C,WAAO,GAAG,eAAe,YAAY,QAAQ,IAAI,eAAe,YAAY,MAAM,IAAI,eAAe,YAAY,QAAQ;AAAA,EAC3H,OAAO;AACL,UAAM,IAAI,MAAM,sDAAsD,cAAc,EAAE;AAAA,EACxF;AACF;AAKO,SAAS,wBAAwB,gBAA2D;AACjG,MAAI,iBAAiB,cAAc,GAAG;AACpC,WAAO,eAAe,YAAY;AAAA,EACpC,OAAO;AACL,WAAO,eAAe;AAAA,EACxB;AACF;AAEO,SAAS,gBACd,GACA,GACS;AACT,MAAI,MAAM,UAAa,MAAM,QAAW;AACtC,WAAO;AAAA,EACT;AACA,MAAI,iBAAiB,CAAC,KAAK,iBAAiB,CAAC,GAAG;AAC9C,WAAO,EAAE,YAAY,aAAa,EAAE,YAAY;AAAA,EAClD,OAAO;AACL,WAAO,oBAAoB,CAAC,MAAM,oBAAoB,CAAC;AAAA,EACzD;AACF;AAKO,SAAS,uBACd,gBACA,UACS;AACT,MAAI,OAAO,aAAa,aAAa;AACnC,WAAO;AAAA,EACT;AACA,MAAI,iBAAiB,cAAc,GAAG;AACpC,WAAO,SAAS;AAAA,MACd,CAAC,yBACC,qBAAqB,YAAY,aAAa,eAAe,YAAY,YACzE,iBAAiB,oBAAoB,KACrC,qBAAqB,YAAY,aAAa,eAAe,YAAY;AAAA,IAC7E;AAAA,EACF,WAAW,4BAA4B,cAAc,GAAG;AACtD,WAAO,SAAS;AAAA,MACd,CAAC,yBACC,qBAAqB,YAAY,aAAa,eAAe,YAAY,YACzE,4BAA4B,oBAAoB,KAChD,qBAAqB,WAAW,eAAe;AAAA,IACnD;AAAA,EACF,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAOO,SAAS,yBACd,iBACA,cACA;AAIA,SACE,4BAA4B,eAAe,KAC3C,iBAAiB,YAAY,KAC7B,aAAa,YAAY,aAAa,gBAAgB,YAAY,YAClE,aAAa,WAAW,gBAAgB;AAE5C;;;AC1FO,SAAS,QAAQ,GAAgB;AACtC,SAAO,EAAE;AACX;AAEO,SAAS,SAAS,GAAgB;AACvC,SAAO,CAAC,EAAE;AACZ;AAEO,IAAM,qBAAqB,CAChC,aACA,YACG;AACH,MAAI,CAAC,YAAa;AAClB,QAAM,EAAE,cAAc,MAAM,IAAI;AAChC,MAAI,WAAW,OAAO;AACpB,QAAI,cAAc;AAChB,YAAM,OAAO,OAAO;AAAA,IACtB,OAAO;AACL,YAAM,OAAO,OAAO;AAAA,IACtB;AAAA,EACF;AACF;AAKO,SAAS,kCACd,UACA,UACS;AACT,MAAI,aAAa,QAAW;AAC1B,WAAO;AAAA,EACT;AAEA,SAAO,SAAS,KAAK,CAAC,mBAAmB,gBAAgB,gBAAgB,QAAQ,CAAC;AACpF;AAOO,SAAS,oBAAoB;AAClC,QAAM,QAAQ,SAAS,cAAc,GAAG;AACxC,QAAM,MAAM,QAAQ;AACpB,QAAM,MAAM,SAAS;AAErB,QAAM,QAAQ,SAAS,cAAc,KAAK;AAC1C,QAAM,MAAM,WAAW;AACvB,QAAM,MAAM,MAAM;AAClB,QAAM,MAAM,OAAO;AACnB,QAAM,MAAM,aAAa;AACzB,QAAM,MAAM,QAAQ;AACpB,QAAM,MAAM,SAAS;AACrB,QAAM,MAAM,WAAW;AACvB,QAAM,YAAY,KAAK;AAEvB,WAAS,KAAK,YAAY,KAAK;AAC/B,QAAM,KAAK,MAAM;AACjB,QAAM,MAAM,WAAW;AACvB,MAAI,KAAK,MAAM;AACf,MAAI,OAAO,IAAI;AACb,SAAK,MAAM;AAAA,EACb;AACA,WAAS,KAAK,YAAY,KAAK;AAC/B,QAAM,iBAAiB,KAAK;AAC5B,SAAO;AACT;;;ACtEO,SAAS,QAAiB;AAC/B,SAAO,OAAO,aAAa;AAC7B;AAWO,SAAS,kBAA2B;AACzC,SAAO,MAAM,IAAI,QAAQ,KAAK,OAAO,UAAU,SAAS,IAAI;AAC9D;;;ACYO,SAAS,gBAAgB,SAAuB;AACrD,YAAU,mBACL;AAGL,QAAM,WAAW;AACjB,QAAM,OAAO;AACb,QAAM,KAAK,IAAI;AAAA,IACb;AAAA,IACA;AAAA,EACF,EAAE;AACF,QAAM,OAAO;AACb,QAAM,SAAS;AACf,QAAM,MAAM;AACZ,QAAM,OAAO;AACb,QAAM,OAAO;AACb,QAAMA,SAAQ,MAAM,QAAQ,WAAW,IAAI,gBAAgB,EAAE,IAAI,IAAI,GAAG,MAAM,GAAG,GAAG,IAAI,IAAI,GAAG,IAAI;AAEnG,SAAO,QAAQ,QAAQ,IAAI,OAAO,OAAOA,MAAK,MAAM,GAAG,IAAI,IAAI,OAAOA,QAAO,IAAI;AACnF;;;AC/CA,IAAM,QAAQ;AAEd,SAAS,kBAAkB,EAAE,MAAM,IAAyB,CAAC,GAAG;AAC9D,SAAO,QAAQ,IAAI,OAAO,IAAI,KAAK,GAAG,IAAI,IAAI,OAAO,OAAO,GAAG;AACjE;;;ACNA,SAAS,iBAAiB,MAAM,QAAQ,aAAa;AAErD,SAAsB,oBACpB,QACA,MACmC;AAAA;AACnC,UAAM,EAAE,GAAG,EAAE,IAAI,MAAM,gBAAgB,QAAQ,MAAM;AAAA,MACnD,WAAW;AAAA,MACX,YAAY,CAAC,OAAO,CAAC,GAAG,KAAK,GAAG,MAAM,EAAE,SAAS,EAAE,CAAC,CAAC;AAAA,IACvD,CAAC;AACD,WAAO,EAAE,GAAG,EAAE;AAAA,EAChB;AAAA;AAEO,SAAS,gBAAgB,eAA4B,OAA4B;AACtF,QAAM,YAAY,CAAC,cAAc,SAAS,MAAM,MAAc;AAC9D,SAAO;AACT;;;ACXO,IAAM,uBAAuB,MAAM;AACxC,SAAO;AAAA,IACL,OAAO,kBAAkB;AAAA,IACzB,KAAK,gBAAgB,CAAC,CAAC;AAAA,EACzB;AACF;AAEO,SAAS,SAAoC,OAAe,SAAY;AAC7E,QAAM,UAAU,OAAO,QAAQ,OAAO,EACnC;AAAA,IAAI,CAAC,CAAC,MAAM,EAAE,GAAG,WAChB,MAAM,KAAK,MAAM,SAAS,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,OAAO,GAAG,QAAQ,OAAO;AAAA,MAC7D;AAAA,MACA;AAAA,MACA;AAAA,MACA,OAAO,wBAAS;AAAA,IAClB,EAAE;AAAA,EACJ,EACC,KAAK,EACL,KAAK,CAAC,GAAG,MAAM;AACd,UAAM,IAAI,EAAE,QAAQ,EAAE;AACtB,WAAO,MAAM,IAAI,IAAI,EAAE,SAAS,EAAE;AAAA,EACpC,CAAC,EACA,OAAO,CAAC,EAAE,MAAM,GAAG,GAAG,QAAQ;AAC7B,QAAI,MAAM,EAAG,QAAO;AACpB,UAAM,OAAO,IAAI,IAAI,CAAC;AACtB,WAAO,KAAK,QAAQ,KAAK,QAAQ,UAAU;AAAA,EAC7C,CAAC;AAEH,QAAM,SAAS,CAAC;AAChB,MAAI,MAAM;AACV,aAAW,EAAE,MAAM,SAAS,MAAM,KAAK,SAAS;AAC9C,QAAI,QAAQ,IAAK,QAAO,KAAK,MAAM,UAAU,KAAK,KAAK,CAAC;AACxD,WAAO,KAAK,EAAE,MAAM,QAAQ,CAAC;AAC7B,UAAM,QAAQ,QAAQ;AAAA,EACxB;AACA,MAAI,MAAM,SAAS,IAAK,QAAO,KAAK,MAAM,UAAU,GAAG,CAAC;AACxD,SAAO;AACT;;;AC1CA,SAAS,kBAAkB,iBAAiB;AAErC,IAAM,iCAAiC;AAAA,EAC5C,UAAU;AAAA,EACV,UAAU;AAAA,EAEV,UAAU;AAAA,EACV,UAAU;AAAA,EAEV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EAEV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACZ;AAEO,IAAM,2BAA2B;AAAA,EACtC,GAAG;AAAA,EACH,UAAU;AAAA,EACV,UAAU;AACZ;AAEO,IAAM,yBAAyB;AAAA,EACpC,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,iBAAiB;AACnB;AAEO,IAAM,6BAA6B;AAAA,EACxC,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EAEjB,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,iBAAiB;AACnB;AAEO,IAAM,uBAAuB;AAAA,EAClC,GAAG;AAAA,EACH,iBAAiB;AAAA,EACjB,iBAAiB;AACnB;;;AClEA;AAAA,EACE,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,YAAY;AAAA,OACP;AACP,OAAO,cAAc;AAEd,IAAM,MAAM,SAAS,UAAU,kBAAkB;AACxD,IAAI,gBAAgB,MAAM;AAYnB,SAAS,YAAY,OAAiB,UAA8B,CAAC,GAAS;AApBrF;AAqBE,MAAI,SAAS,KAAK;AAClB,wBAAqB,aAAQ,0BAAR,YAAiC,KAAK;AAC7D;AAYO,SAAS,gBAAgB,WAAyB,UAAkC,CAAC,GAAG;AAnC/F;AAoCE,QAAM,kBAAkB,IAAI;AAE5B,MAAI,gBAAgB,CAAC,YAAY,aAAa,eAAe;AAC3D,UAAM,YAAY,gBAAgB,YAAY,aAAa,UAAU;AAErE,UAAM,WAAW,aAAa,UAAU;AACxC,UAAM,UAAU,YAAY,eAAe,WAAW,aAAa;AAEnE,WAAO,CAAC,KAAK,YAA6C;AACxD,UAAI,QAAS,WAAU,KAAK,OAAO;AAAA,UAC9B,WAAU,GAAG;AAClB,UAAI,SAAS;AACX,kBAAU,UAAU,KAAK,OAAO;AAAA,MAClC;AAAA,IACF;AAAA,EACF;AACA,MAAI,SAAS,IAAI,SAAS,CAAC;AAC3B,4BAAyB,aAAQ,8BAAR,YAAqC,SAAS;AACzE;;;ACHO,IAAM,eAAuC;AAAA,EAClD;AAAA,IACE,SAAS;AAAA,IACT,MAAM;AAAA,EACR;AAAA,EACA;AAAA,IACE,SAAS;AAAA,IACT,MAAM;AAAA,IACN,aAAa;AAAA,EACf;AAAA,EACA;AAAA,IACE,SAAS;AAAA,IACT,MAAM;AAAA,IACN,aAAa;AAAA,EACf;AAAA,EACA;AAAA,IACE,SAAS;AAAA,IACT,MAAM;AAAA,IACN,UAAU;AAAA,EACZ;AAAA,EACA;AAAA,IACE,SAAS;AAAA,IACT,MAAM;AAAA,IACN,UAAU;AAAA,EACZ;AAAA,EACA;AAAA,IACE,SAAS;AAAA,IACT,MAAM;AAAA,IACN,UAAU;AAAA,EACZ;AAAA,EACA;AAAA,IACE,SAAS;AAAA,IACT,MAAM;AAAA,IACN,UAAU;AAAA,EACZ;AACF;AAEO,SAAS,iBACd,mBACA,kBACA,OACA,QACgB;AAChB,MAAI,kBAAkB,SAAS,GAAG;AAChC,UAAM,IAAI,MAAM,uDAAuD;AAAA,EACzE;AACA,QAAM,UAAU,+BAA+B,iBAAiB;AAChE,MAAI,SAAS,KAAK,UAAU,GAAG;AAC7B,WAAO,QAAQ,CAAC;AAAA,EAClB;AAEA,MAAI,qBAAqB;AACzB,QAAM,uBAAuB,QAAQ,SAAS,IAAI,cAAc;AAChE,MAAI,SAAS,QAAQ,KAAK,CAAC,SAAS,OAAO,eAAe;AACxD,yBAAqB;AACrB,UAAM,0BACJ,WAAW,UAAU,CAAC,GAAG,MAAM;AAC7B,YAAM,kBAAkB,CAAC,EAAE,eAAe,EAAE,gBAAgB;AAC5D,YAAM,4BAA4B,IAAI;AACtC,YAAM,qCAAqC,EAAE,aAAa,QAAQ;AAClE,aAAO,6BAA6B,sCAAsC;AAAA,IAC5E,CAAC,MAAM;AACT,WAAO,QAAQ,YAAY,oBAAoB,CAAC;AAAA,EAClD,CAAC;AACD,MAAI,WAAW,QAAW;AACxB,aAAS,QAAQ,QAAQ,SAAS,CAAC;AACnC,QAAI,QAAQ;AACV,UAAI;AAAA,QACF,0CAA0C,gBAAgB,mBAAmB,KAAK,IAAI,MAAM,0CAA0C,MAAM;AAAA,MAC9I;AAAA,IACF,OAAO;AACL,YAAM,IAAI,MAAM,qCAAqC;AAAA,IACvD;AAAA,EACF;AAGA,MAAI,QAAQ,OAAO,YAAY,SAAS,OAAO,WAAW;AAExD,QAAI,qBAAqB,GAAG;AAC1B,YAAM,gBAAgB,QAAQ,qBAAqB,CAAC;AACpD,eAAS;AAAA,QACP,QAAQ,MAAM,GAAG,kBAAkB;AAAA,QACnC,cAAc;AAAA,QACd;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAKO,SAAS,+BAA+B,SAAmD;AAChG,SAAO,CAAC,GAAG,OAAO,EACf,IAAI,CAAC,WAAW;AAnJrB;AAoJM,WAAO;AAAA,MACL,MAAM,GAAG,OAAO,OAAO,IAAI,OAAO,IAAI;AAAA,MACtC,SAAS,OAAO;AAAA,MAChB,MAAM,OAAO;AAAA,MACb,UAAU,OAAO,UAAU,OAAO;AAAA,MAClC,WAAU,YAAO,aAAP,YAAmB;AAAA,MAC7B,YAAW,YAAO,cAAP,YAAoB;AAAA,MAC/B,aAAa,OAAO;AAAA,IACtB;AAAA,EACF,CAAC,EACA,KAAK,CAAC,GAAG,MAAM;AACd,QAAI,EAAE,aAAa,EAAE,UAAU;AAC7B,aAAO,EAAE,WAAW,EAAE;AAAA,IACxB,WAAW,EAAE,aAAa,KAAK,EAAE,aAAa,GAAG;AAC/C,aAAO,EAAE,WAAW,EAAE;AAAA,IACxB,WAAW,EAAE,cAAc,KAAK,EAAE,cAAc,GAAG;AACjD,aAAO,EAAE,YAAY,EAAE;AAAA,IACzB,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AACL;;;ACzKO,SAAS,cAAiB,MAAc,MAAsB;AACnE,QAAM,cAAc,IAAI,IAAI,IAAI;AAChC,aAAW,QAAQ,MAAM;AACvB,gBAAY,OAAO,IAAI;AAAA,EACzB;AACA,SAAO;AACT;;;ACHO,SAAS,wBAAiC;AAC/C,SACE,OAAO,cAAc,eACrB,UAAU,gBACV,CAAC,CAAC,UAAU,aAAa;AAE7B;;;ACFO,SAAS,+BACd,UACA,WACA,SAAS,GACT;AACA,SAAO,SAAS,OAAO,CAAC,YAAY;AAZtC;AAaI,UAAM,eAAe,CAAC,CAAC,UAAU;AACjC,UAAM,oBAAmB,eAAU,iBAAV,YAA0B,YAAY,aAAa,YAAY,IAAI;AAE5F,UAAM,mBAAmB,eACrB,KAAK,IAAI,QAAQ,0BAA0B,QAAQ,SAAS,IAC5D,QAAQ;AAEZ,UAAM,kBAAkB,SAAS,QAAQ,UAAU,QAAQ;AAC3D,WACE,oBAAoB,oBAAoB,oBAAoB,mBAAmB;AAAA,EAEnF,CAAC;AACH;AAEO,SAAS,iCACd,SACA,YAC8B;AA9BhC;AA+BE,SAAO,iCACF,UADE;AAAA,IAEL,2BAA0B,gBAAW,iBAAX,YAA2B;AAAA,IACrD,YAAY,WAAW;AAAA,EACzB;AACF;AAKO,SAAS,eACd,cACA,aACA,YACA;AACA,SAAO,CAAC,GAAG,cAAc,GAAG,WAAW,EACpC,YAAY,CAAC,KAAK,YAAY;AAC7B,QAAI,CAAC,IAAI,KAAK,CAAC,QAAQ,IAAI,OAAO,QAAQ,EAAE,GAAG;AAC7C,UAAI,QAAQ,OAAO;AAAA,IACrB;AACA,WAAO;AAAA,EACT,GAAG,CAAC,CAAa,EAChB,MAAM,IAAI,UAAU;AACzB;AAEO,SAAS,wBACd,YACA,WACA;AACA,MAAI,UAAU,WAAW,WAAW,QAAQ;AAC1C,WAAO;AAAA,EACT;AACA,SAAO,CAAC,UAAU,MAAM,CAAC,eAAe;AACtC,WAAO,WAAW;AAAA,MAChB,CAAC,gBACC,YAAY,OAAO,WAAW,MAC9B,YAAY,SAAS,WAAW,QAChC,YAAY,UAAU,WAAW,SACjC,YAAY,aAAa,WAAW,YACpC,YAAY,cAAc,WAAW,aACrC,YAAY,YAAY,WAAW;AAAA,IACvC;AAAA,EACF,CAAC;AACH;;;ACpEO,IAAM,oBAA8B,CAAC;AASrC,IAAM,uBAAoC;AAAA,EAC/C,UAAU;AAAA,EACV,gBAAgB;AAAA,EAChB,cAAc;AAChB;AAQO,SAAS,mBAAmB,QAAgE;AACjG,SAAO,OAAO,WAAW;AAC3B;AAEO,SAAS,qBAAqB,SAA4D;AAC/F,SACE,MAAM,QAAQ,OAAO,KACpB,QAAqC,OAAO,kBAAkB,EAAE,SAAS;AAE9E;;;ACpCA,SAAS,SAAAC,cAAa;;;ACCtB,SAAS,aAAa;AAIf,SAAS,6BACd,GACA,GACQ;AACR,SAAO,EAAE,aAAa,EAAE;AAC1B;AAEO,SAAS,6BACd,GACA,GACQ;AACR,MAAI,EAAE,eAAe,EAAE,YAAY;AACjC,WAAO;AAAA,EACT,OAAO;AACL,WAAO,EAAE,aAAa,KAAK;AAAA,EAC7B;AACF;AAEO,SAAS,+BACd,GACA,GACQ;AA1BV;AA2BE,MAAI,EAAE,gBAAgB,UAAa,EAAE,gBAAgB,QAAW;AAC9D,aAAQ,aAAE,gBAAF,mBAAe,cAAf,YAA4B,OAAM,aAAE,gBAAF,mBAAe,cAAf,YAA4B;AAAA,EACxE,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAEO,SAAS,2BACd,GACA,GACA;AArCF;AAsCE,WAAQ,aAAE,aAAF,mBAAY,cAAZ,YAAyB,OAAM,aAAE,aAAF,mBAAY,cAAZ,YAAyB;AAClE;AAEO,SAAS,0BACd,GACA,GACA;AACA,MAAI,iBAAiB,CAAC,GAAG;AACvB,QAAI,iBAAiB,CAAC,GAAG;AACvB,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF,WAAW,iBAAiB,CAAC,GAAG;AAC9B,WAAO;AAAA,EACT,OAAO;AACL,WAAO;AAAA,EACT;AACF;AA4BO,SAAS,+BACd,GACA,GACA;AACA,QAAM,SAAS,EAAE,YAAY;AAC7B,QAAM,SAAS,EAAE,YAAY;AAE7B,MAAI,WAAW,QAAQ;AACrB,QAAI,QAAQ;AACV,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF,OAAO;AACL,WAAO;AAAA,EACT;AACF;;;AD9EO,SAAS,oBACd,QAC+B;AAC/B,QAAM,cAA6C,CAAC;AACpD,QAAM,oBAAmD,CAAC;AAC1D,QAAM,eAA8C,CAAC;AACrD,QAAM,kBAAiD,CAAC;AAExD,SAAO,QAAQ,CAAC,aAAa;AAC3B,QAAI,SAAS,YAAY,WAAW,SAAS,WAAWC,OAAM,OAAO,QAAQ;AAC3E,kBAAY,KAAK,QAAQ;AAAA,IAC3B,WAAW,SAAS,WAAWA,OAAM,OAAO,aAAa;AACvD,wBAAkB,KAAK,QAAQ;AAAA,IACjC,WAAW,SAAS,WAAWA,OAAM,OAAO,QAAQ;AAClD,mBAAa,KAAK,QAAQ;AAAA,IAC5B,OAAO;AACL,sBAAgB,KAAK,QAAQ;AAAA,IAC/B;AAAA,EACF,CAAC;AAED,QAAM,0BAA0B,sBAAsB,iBAAiB;AACvE,QAAM,qBAAqB,iBAAiB,YAAY;AAExD,SAAO,CAAC,GAAG,aAAa,GAAG,yBAAyB,GAAG,oBAAoB,GAAG,eAAe;AAC/F;AASA,SAAS,sBACP,mBAC+B;AAC/B,QAAM,oBAAmD,CAAC;AAC1D,QAAM,qBAAoD,CAAC;AAE3D,oBAAkB,QAAQ,CAAC,aAAa;AACtC,QAAI,SAAS,YAAY,SAAS;AAChC,wBAAkB,KAAK,QAAQ;AAAA,IACjC,OAAO;AACL,yBAAmB,KAAK,QAAQ;AAAA,IAClC;AAAA,EACF,CAAC;AAED,oBAAkB,KAAK,CAAC,GAAG,MAAM,2BAA2B,EAAE,aAAa,EAAE,WAAW,CAAC;AACzF,qBAAmB,KAAK,CAAC,GAAG,MAAM,2BAA2B,EAAE,aAAa,EAAE,WAAW,CAAC;AAE1F,QAAM,6BAA6B,CAAC,GAAG,oBAAoB,GAAG,iBAAiB;AAC/E,SAAO;AACT;AAEA,SAAS,iBACP,uBAC+B;AAC/B,QAAM,oBAAmD,CAAC;AAC1D,QAAM,qBAAoD,CAAC;AAE3D,wBAAsB,QAAQ,CAAC,aAAa;AAC1C,QAAI,SAAS,YAAY,SAAS;AAChC,wBAAkB,KAAK,QAAQ;AAAA,IACjC,OAAO;AACL,yBAAmB,KAAK,QAAQ;AAAA,IAClC;AAAA,EACF,CAAC;AAED,qBAAmB,KAAK,CAAC,GAAG,MAAM;AAEhC,QAAI,EAAE,YAAY,cAAc,EAAE,YAAY,YAAY;AACxD,aAAO,6BAA6B,EAAE,aAAa,EAAE,WAAW;AAAA,IAClE;AAGA,QAAI,EAAE,YAAY,eAAe,EAAE,YAAY,YAAY;AACzD,aAAO,6BAA6B,EAAE,aAAa,EAAE,WAAW;AAAA,IAClE;AAGA,QAAI,EAAE,YAAY,gBAAgB,EAAE,YAAY,aAAa;AAC3D,aAAO,+BAA+B,EAAE,aAAa,EAAE,WAAW;AAAA,IACpE;AAGA,QAAI,iBAAiB,CAAC,MAAM,iBAAiB,CAAC,GAAG;AAC/C,aAAO,0BAA0B,GAAG,CAAC;AAAA,IACvC;AAGA,QAAI,EAAE,YAAY,oBAAoB,EAAE,YAAY,iBAAiB;AACnE,aAAO,+BAA+B,GAAG,CAAC;AAAA,IAC5C;AAGA,WAAO,2BAA2B,EAAE,aAAa,EAAE,WAAW;AAAA,EAChE,CAAC;AAED,SAAO,CAAC,GAAG,mBAAmB,GAAG,kBAAkB;AACrD;;;AExGO,SAAS,iBAAiB,cAA4C;AAC3E,QAAM,qBAAqB,CAAC,GAAG,YAAY;AAC3C,qBAAmB,KAAK,CAAC,GAAG,MAAM;AAEhC,QAAI,EAAE,cAAc,EAAE,YAAY;AAChC,aAAO,6BAA6B,GAAG,CAAC;AAAA,IAC1C;AAGA,QAAI,EAAE,eAAe,EAAE,YAAY;AACjC,aAAO,6BAA6B,GAAG,CAAC;AAAA,IAC1C;AAGA,QAAI,EAAE,gBAAgB,EAAE,aAAa;AACnC,aAAO,+BAA+B,GAAG,CAAC;AAAA,IAC5C;AAGA,UAAM,SAAS,EAAE,uBAAuB,OAAO;AAC/C,UAAM,SAAS,EAAE,uBAAuB,OAAO;AAC/C,QAAI,WAAW,QAAQ;AACrB,UAAI,QAAQ;AACV,eAAO;AAAA,MACT,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AAGA,WAAO,2BAA2B,GAAG,CAAC;AAAA,EACxC,CAAC;AACD,QAAM,mBAAmB,mBAAmB,KAAK,CAAC,MAAM,EAAE,OAAO;AACjE,MAAI,kBAAkB;AACpB,UAAM,WAAW,mBAAmB,QAAQ,gBAAgB;AAC5D,QAAI,YAAY,GAAG;AACjB,yBAAmB,OAAO,UAAU,CAAC;AACrC,UAAI,mBAAmB,SAAS,GAAG;AACjC,2BAAmB,OAAO,GAAG,GAAG,gBAAgB;AAAA,MAClD,OAAO;AACL,2BAAmB,KAAK,gBAAgB;AAAA,MAC1C;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;;;AC9DO,SAAS,MAAS,OAAiB,MAAc;AACtD,SAAO,MAAM;AAAA,IACX,CAAC,KAAK,MAAM,QAAQ;AAClB,aAAO,MAAM,SAAS,IAClB,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,IACf,CAAC,GAAG,IAAI,MAAM,GAAG,EAAE,GAAG,CAAC,GAAG,IAAI,MAAM,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC;AAAA,IACvD;AAAA,IACA,CAAC;AAAA,EACH;AACF;AAEO,SAAS,IAAU,IAAc,IAAc;AACpD,QAAM,eAAe,KAAK,IAAI,GAAG,QAAQ,GAAG,MAAM;AAClD,SAAO,IAAI,MAAM,YAAY,EAAE,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,QAAQ,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;AAC/E;AAEO,SAAS,aAAgB,IAAc,IAAc,IAAwB;AAClF,SAAO,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,SAAS,GAAG,CAAC,CAAC,CAAC;AAC/D;;;ACZA,SAAS,aAAa,SAAAC,QAAO,wBAAwB;AA6E9C,SAAS,wBAAiD,MAAqB;AACpF,SAAO,KAAK,IAAI,CAAC,SAAS;AACxB,QAAI,OAAO,SAAS,YAAY,OAAO,SAAS,UAAU;AACxD,aAAO,GAAG,IAAI;AAAA,IAChB,OAAO;AACL,aAAO,oBAAoB,IAAI;AAAA,IACjC;AAAA,EACF,CAAC;AACH;;;ACxEO,SAAS,iBAA0C,OAAY,MAA6B;AACjG,SAAO;AAAA,IACL,SAAS,aAAa,OAAO,MAAM,mBAAmB;AAAA,IACtD,OAAO,aAAa,MAAM,OAAO,mBAAmB;AAAA,EACtD;AACF;AAEA,SAAS,kBAAqB,SAAoC;AAChE,SAAO,QAAQ,MAAM,WAAW,KAAK,QAAQ,QAAQ,WAAW;AAClE;AAEO,SAAS,UACd,gBACA,iBACQ;AACR,QAAM,iBAAiB,gBAAgB;AAAA,IACrC,CAAC,oBACC,oBAAoB,eAAe,MAAM,oBAAoB,cAAc;AAAA,EAC/E;AACA,MAAI,mBAAmB,IAAI;AACzB,UAAM,IAAI;AAAA,MACR,kCAAkC;AAAA,QAChC;AAAA,MACF,CAAC,WAAW,wBAAwB,eAAe,CAAC;AAAA,IACtD;AAAA,EACF;AACA,SAAO;AACT;AAGO,SAAS,UACd,aACA,UACA,iBACK;AACL,QAAM,iBAAiB,UAAU,aAAa,eAAe;AAC7D,QAAM,mBAAmB,UAAU,UAAU,eAAe;AAE5D,kBAAgB,OAAO,gBAAgB,GAAG,QAAQ;AAClD,kBAAgB,OAAO,kBAAkB,GAAG,WAAW;AAEvD,SAAO;AACT;AAEO,SAAS,SAAkC,YAAe,MAAgB;AAC/E,QAAM,uBAAuB,UAAU,YAAY,IAAI;AAEvD,OAAK,OAAO,sBAAsB,CAAC;AACnC,SAAO;AACT;AAEA,SAAS,QAAiC,WAAc,MAAgB;AACtE,SAAO,CAAC,GAAG,MAAM,SAAS;AAC5B;AAEO,SAAS,gBAAmB,MAAW,mBAAuC;AACnF,QAAM,QAAQ,MAAM,MAAM,iBAAiB;AAC3C,SAAO;AACT;AAGO,SAAS,YACd,aACA,UACA,gBACK;AACL,MAAI,cAAmB,YAAY,aAAa,QAAQ;AAExD,MAAI,YAAY,SAAS,SAAS,QAAQ;AAExC,UAAM,aAAa,aAAa,UAAU,aAAa,mBAAmB;AAC1E,kBAAc,CAAC,GAAG,aAAa,GAAG,UAAU;AAAA,EAC9C;AACA,QAAM,eAAe,gBAAgB,aAAa,cAAc;AAChE,QAAM,YAAY,gBAAgB,UAAU,cAAc;AAE1D,MAAI,cAAc,SAAS,EAAE,QAAQ,CAAC,CAAC,aAAa,QAAQ,GAAG,cAAc;AAC3E,QAAI,eAAe,UAAU;AAE3B,YAAM,cAAc,gBAAgB,aAAa,cAAc,EAAE,SAAS;AAC1E,YAAM,UAAU,iBAAiB,aAAa,QAAQ;AAEtD,UAAI,kBAAkB,OAAO,GAAG;AAC9B,YAAI;AAAA,UACF,oCAAoC,SAAS,cAAc;AAAA,YACzD;AAAA,UACF,CAAC,WAAW,wBAAwB,QAAQ,CAAC;AAAA,UAC7C,EAAE,QAAQ;AAAA,QACZ;AAEA,YAAI,QAAQ,MAAM,WAAW,QAAQ,QAAQ,QAAQ;AACnD,cAAI,QAAQ,OAAO,QAAQ,OAAO,EAAE,QAAQ,CAAC,CAAC,OAAO,OAAO,MAAM;AAChE,gBAAI,SAAS,SAAS;AACpB,4BAAc,UAAa,OAAO,SAAS,WAAW;AAAA,YACxD,OAAO;AACL,oBAAM,IAAI;AAAA,gBACR,sEAAsE,KAAK,KAAK,OAAO;AAAA,cACzF;AAAA,YACF;AAAA,UACF,CAAC;AAAA,QACH;AAEA,YAAI,QAAQ,MAAM,WAAW,KAAK,QAAQ,QAAQ,SAAS,GAAG;AAC5D,kBAAQ,QAAQ,QAAQ,CAAC,SAAS;AAChC,0BAAc,SAAY,MAAM,WAAW;AAAA,UAC7C,CAAC;AAAA,QACH;AAEA,YAAI,QAAQ,MAAM,SAAS,KAAK,QAAQ,QAAQ,WAAW,GAAG;AAC5D,kBAAQ,MAAM,QAAQ,CAAC,SAAS;AAC9B,0BAAc,QAAW,MAAM,WAAW;AAAA,UAC5C,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AAED,MAAI,YAAY,SAAS,SAAS,QAAQ;AAExC,UAAM,eAAe,aAAa,aAAa,UAAU,mBAAmB;AAC5E,kBAAc,YAAY;AAAA,MACxB,CAAC,SAAS,CAAC,aAAa,IAAI,mBAAmB,EAAE,SAAS,oBAAoB,IAAI,CAAC;AAAA,IACrF;AAAA,EACF;AAEA,SAAO;AACT;AASA,SAAS,YAAqC,aAAkB,UAAoB;AAClF,SAAO,YAAY,IAAI,CAAC,gBAAgB;AACtC,UAAM,uBAAuB,SAAS;AAAA,MACpC,CAAC;AAAA;AAAA,QAEC,oBAAoB,WAAW,MAAM,oBAAoB,QAAQ;AAAA,QAEhE,OAAO,gBAAgB,YACtB,4BAA4B,WAAW,KACvC,iBAAiB,QAAQ,KACzB,yBAAyB,aAAa,QAAQ;AAAA;AAAA,IACpD;AACA,WAAO,sDAAwB;AAAA,EACjC,CAAC;AACH;;;AChKA,SAAS,SAAAC,cAAa;AAEtB,SAAS,WAAAC,UAAS,OAAAC,MAAK,aAAAC,kBAAiB;;;ACRxC,SAAS,oBAAAC,mBAAkB,aAAAC,YAAW,SAAAC,cAAa;AAInD,SAAS,cAAAC,aAAY,OAAAC,MAAK,aAAAC,YAAW,iBAAiB;;;ACNtD,SAAS,SAAAC,cAAa;AACtB,SAAS,KAAK,iBAAiB;;;ACUxB,SAAS,YAA4C,qBAAwB;AAClF,SAAO,GAAG,SAAS,IAAI,mBAAmB;AAC5C;;;ADNO,SAAS,gBAAgB,iBAAkC;AAChE,QAAM,aAAa,qBAAqB,eAAe;AACvD,QAAM,gBAAgB,wBAAwB,gBAAgB,WAAW,EAAE;AAAA,IACzE,IAAI,MAAM;AACR,aAAO,qBAAqB,eAAe;AAAA,IAC7C,CAAC;AAAA,IACD,UAAU,UAAU;AAAA,EACtB;AACA,QAAM,YAAoB;AAAA,IACxB,gBAAgB,WAAWC,OAAM,OAAO,UACtC,gBAAgB,WAAWA,OAAM,OAAO,cACtC,4BACA;AAAA,EACN;AACA,SAAO,EAAE,WAAW,cAAc;AACpC;AAEO,SAAS,qBAAqB,SAA0B;AAC7D,MAAI,iBAAiB,OAAO,GAAG;AAC7B,WAAO,QAAQ;AAAA,EACjB,OAAO;AACL,UAAM,EAAE,QAAQ,MAAM,YAAY,IAAI;AACtC,QAAI,UAAU,MAAM;AAClB,aAAO,YACJ,qBAAqB,EACrB,KAAK,CAAC,QAAQ,IAAI,WAAW,UAAU,IAAI,cAAc,IAAI;AAAA,IAClE,WAAW,MAAM;AACf,aAAO,YAAY,0BAA0B,IAAI;AAAA,IACnD,WAAW,QAAQ;AACjB,aAAO,YAAY,oBAAoB,MAAM;AAAA,IAC/C,OAAO;AACL,YAAM,IAAI,MAAM,qDAAqD;AAAA,IACvE;AAAA,EACF;AACF;;;AExCA,SAAS,SAAS,OAAAC,MAAK,YAAY,aAAAC,YAAW,UAAU,QAAQ,cAAc;AAE9E,SAA2B,MAAM,aAAAC,YAAW,SAAAC,cAAa;AAIlD,SAAS,kBAAkB,SAAe,QAAuC;AACtF,QAAM,aAAa,IAAI,WAAiB,CAAC,cAAc;AACrD,UAAM,eAAe,MAAM;AACzB,gBAAU,KAAK,IAAI;AAAA,IACrB;AAEA,WAAO,QAAQ,CAAC,QAAQ;AACtB,WAAK,GAAG,KAAK,YAAY;AAAA,IAC3B,CAAC;AAED,UAAM,cAAc,MAAM;AACxB,aAAO,QAAQ,CAAC,QAAQ;AACtB,aAAK,IAAI,KAAK,YAAY;AAAA,MAC5B,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT,CAAC,EAAE,KAAKC,WAAU,IAAI,CAAC;AAEvB,SAAO;AACT;AAEO,SAAS,kBAAuC,MAAY,OAAU;AAC3E,QAAM,aAAa,IAAI,WAA8C,CAAC,cAAc;AAClF,UAAM,SAAS,IAAI,WAA8C;AAC/D,gBAAU,KAAK,MAAM;AAAA,IACvB;AACA,SAAK,GAAG,OAAmC,MAAM;AAEjD,UAAM,cAAc,MAAM;AACxB,WAAK,IAAI,OAAmC,MAAM;AAAA,IACpD;AACA,WAAO;AAAA,EACT,CAAC;AAED,SAAO;AACT;AAEO,SAAS,aAAa,MAAY;AACvC,QAAM,aAAa;AAAA,IACjB;AAAA,IACAC,WAAU;AAAA,IACVA,WAAU;AAAA,IACVA,WAAU;AAAA,IACVA,WAAU;AAAA,IACVA,WAAU;AAAA,IACVA,WAAU;AAAA,IACVA,WAAU;AAAA,IACVA,WAAU;AAAA,IACVA,WAAU;AAAA,EACZ,EAAE,KAAKD,WAAU,IAAI,CAAC;AAEtB,SAAO;AACT;AAEO,SAAS,wBAAwB,MAAY;AAClD,SAAO,kBAAkB,MAAMC,WAAU,sBAAsB,EAAE;AAAA,IAC/DC,KAAI,CAAC,CAAC,eAAe,MAAM,eAAe;AAAA,IAC1CF,WAAU,KAAK,KAAK;AAAA,EACtB;AACF;AAMO,SAAS,oBAAoB,MAAY;AAC9C,MAAI;AACJ,QAAM,YAAiC,CAAC;AAExC,QAAM,aAAa,IAAI,WAAgC,CAAC,eAAe;AACrE,4BAAwB;AACxB,WAAO,MAAM;AACX,gBAAU,QAAQ,CAAC,aAAa;AAC9B,iBAAS,YAAY;AAAA,MACvB,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACD,QAAM,oBAAyC,CAAC;AAEhD,QAAM,YAAY,CAAC,aAA+B,gBAA6B;AAC7E,QACE,YAAY,WAAWG,OAAM,OAAO,eACpC,YAAY,WAAWA,OAAM,OAAO,kBACpC;AACA;AAAA,IACF;AACA,QAAI,WAAW,kBAAkB,KAAK,CAAC,OAAO,GAAG,YAAY,aAAa,YAAY,QAAQ;AAC9F,UAAM,uBAAuB,CAACC,iBAA6B;AACzD,aAAOA,aACJ,qBAAqB,EACrB;AAAA,QACC,CAAC,WACE,MAAM,WAAWD,OAAM,OAAO,eAC7B,MAAM,WAAWA,OAAM,OAAO,qBAChC,MAAM;AAAA,MACV;AAAA,IACJ;AACA,QAAI,CAAC,UAAU;AACb,iBAAW;AAAA,QACT;AAAA,QACA,QAAQ,qBAAqB,WAAW;AAAA,MAC1C;AAAA,IACF,OAAO;AACL,YAAM,QAAQ,kBAAkB,QAAQ,QAAQ;AAChD,wBAAkB,OAAO,OAAO,CAAC;AACjC,eAAS,SAAS,qBAAqB,WAAW;AAAA,IACpD;AACA,QAAI,SAAS,OAAO,SAAS,GAAG;AAC9B,wBAAkB,KAAK,QAAQ;AAAA,IACjC;AAEA,0BAAsB,KAAK,iBAAiB;AAAA,EAC9C;AACA,YAAU;AAAA,IACR,kBAAkB,MAAMF,WAAU,eAAe,EAAE;AAAA,MAAU,CAAC,CAAC,EAAK,OAAI,MACtE,UAAU,GAAG,IAAI;AAAA,IACnB;AAAA,EACF;AACA,YAAU;AAAA,IACR,kBAAkB,MAAMA,WAAU,iBAAiB,EAAE;AAAA,MAAU,CAAC,CAAC,EAAK,OAAI,MACxE,UAAU,GAAG,IAAI;AAAA,IACnB;AAAA,EACF;AACA,YAAU;AAAA,IACR,kBAAkB,MAAMA,WAAU,mBAAmB,EAAE,UAAU,CAAC,SAAS,UAAU,GAAG,IAAI,CAAC;AAAA,EAC/F;AACA,YAAU;AAAA,IACR,kBAAkB,MAAMA,WAAU,qBAAqB,EAAE,UAAU,CAAC,SAAS;AAC3E,gBAAU,GAAG,IAAI;AAAA,IACnB,CAAC;AAAA,EACH;AACA,YAAU;AAAA,IACR,kBAAkB,MAAMA,WAAU,UAAU,EAAE,UAAU,CAAC,SAAS;AAChE,gBAAU,GAAG,IAAI;AAAA,IACnB,CAAC;AAAA,EACH;AACA,YAAU;AAAA,IACR,kBAAkB,MAAMA,WAAU,YAAY,EAAE,UAAU,CAAC,SAAS;AAClE,gBAAU,GAAG,IAAI;AAAA,IACnB,CAAC;AAAA,EACH;AACA,aAAW,MAAM;AAEf,eAAW,KAAK,KAAK,mBAAmB,OAAO,GAAG;AAChD,QAAE,qBAAqB,EAAE,QAAQ,CAAC,UAAU;AAC1C,kBAAU,OAAO,CAAC;AAAA,MACpB,CAAC;AAAA,IACH;AAAA,EACF,GAAG,CAAC;AAEJ,SAAO;AACT;AAEO,SAAS,iBAAiB,MAAY;AAC3C,QAAM,WAAW;AAAA,IACf;AAAA,IACAA,WAAU;AAAA,IACVA,WAAU;AAAA,EACZ,EAAE;AAAA,IACAC,KAAI,CAAC,MAAM;AACT,aAAO,EAAE,MAAM,EAAE,MAAM,UAAU,EAAE,SAAS;AAAA,IAC9C,CAAC;AAAA,EACH;AACA,SAAO;AACT;AAEO,SAAS,sBAAsB,MAAY;AAChD,SAAO,kBAAkB,MAAMD,WAAU,qBAAqB,EAAE;AAAA,IAC9DC,KAAI,CAAC,CAAC,QAAQ,MAAM,QAAQ;AAAA,EAC9B;AACF;AAEO,SAAS,0BACd,MACA,SACA,qBAAqB,MACrB;AAvLF;AAwLE,QAAM,iBAAiB,MAAY;AACjC,QAAI;AACF,YAAM,aAAa,MAAM,KAAK,gBAAgB,MAAM,kBAAkB;AACtE,oBAAc,KAAK,UAAU;AAAA,IAC/B,SAAS,GAAQ;AACf,yCAAU;AAAA,IACZ;AAAA,EACF;AACA,QAAM,gBAAgB,IAAI,QAA2B;AAErD,QAAM,aAAa,cAAc;AAAA,IAC/B,SAAS,MAAM;AAnMnB,UAAAG;AAoMM,OAAAA,MAAA,uCAAW,iBAAX,gBAAAA,IAAyB,oBAAoB,gBAAgB;AAAA,IAC/D,CAAC;AAAA,EACH;AAEA,MAAI,OAAO,WAAW,aAAa;AACjC,QAAI,CAAC,OAAO,iBAAiB;AAC3B,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AACA,iDAAW,iBAAX,mBAAyB,iBAAiB,gBAAgB;AAAA,EAC5D;AAEA,SAAO;AAAA,IACL,KAAK,gBAAgB,MAAM,kBAAkB,EAAE,MAAM,CAAC,MAAM;AAC1D,yCAAU;AACV,aAAO,CAAC;AAAA,IACV,CAAC;AAAA,IACD;AAAA,EACF;AACF;AAEO,SAAS,mBAAmB,MAAY;AAC7C,SAAO,kBAAkB,MAAMJ,WAAU,YAAY;AACvD;AAEO,SAAS,mBAAmB,MAAY;AAC7C,SAAO,kBAAkB,MAAMA,WAAU,WAAW;AACtD;AAEO,SAAS,mCAAmC,MAAY;AAC7D,QAAM,aAAa,kBAAkB,MAAMA,WAAU,0BAA0B,EAAE;AAAA,IAC/EC,KAAI,CAACI,UAAS;AACZ,aAAO,EAAE,cAAcA,MAAK,iBAAiB;AAAA,IAC/C,CAAC;AAAA,EACH;AACA,SAAO;AACT;AAEO,SAAS,mCAAmC,MAAY;AAC7D,QAAM,aAAa,kBAAkB,MAAML,WAAU,0BAA0B,EAAE;AAAA,IAC/EC,KAAI,CAACI,UAAS;AACZ,aAAO,EAAE,cAAcA,MAAK,iBAAiB;AAAA,IAC/C,CAAC;AAAA,EACH;AACA,SAAO;AACT;AAEO,SAAS,6BAA6B,MAAY,MAAuB;AAC9E,SAAO,kBAAkB,MAAML,WAAU,mBAAmB,EAAE;AAAA,IAC5D,OAAO,CAAC,CAAC,YAAY,MAAM,iBAAiB,IAAI;AAAA,IAChDC,KAAI,CAAC,CAACK,OAAM,QAAQ,MAAM;AACxB,UAAI,MAAM,0DAA0D,EAAE,MAAAA,OAAM,SAAS,CAAC;AACtF,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AACF;AAEO,SAAS,2BAA2B,MAAY,aAAsC;AAC3F,SAAO,kBAAkB,MAAMN,WAAU,kCAAkC,EAAE;AAAA,IAC3E;AAAA,MACE,CAAC,CAAC,EAAE,CAAC,OACH,2CAAa,eAAa,uBAAG,aAC5B,CAAC,MAAK,2CAAa,cAAa,KAAK,iBAAiB;AAAA,IAC3D;AAAA,IACAC,KAAI,CAAC,CAAC,SAAS,MAAM,SAAS;AAAA,IAC9BF;AAAA,OACE,2CAAa,WACR,YAAiC,gBAClC,CAAC,EAAC,2CAAa;AAAA,IACrB;AAAA,EACF;AACF;AAEO,SAAS,0BAA0B,MAAY;AACpD,SAAO,kBAAkB,MAAMC,WAAU,sBAAsB,EAAE;AAAA,IAC/DC,KAAI,CAAC,CAAC,SAAS,MAAM,SAAS;AAAA,IAC9BF,WAAU,KAAK,WAAW;AAAA,EAC5B;AACF;;;AHtQO,SAAS,yBACd,gBACG,QACH;AACA,QAAM,aAAa,IAAIQ,YAAc,CAAC,cAAc;AAClD,UAAM,sBAAsB,MAAM;AAChC,gBAAU,KAAK,WAAW;AAAA,IAC5B;AAEA,WAAO,QAAQ,CAAC,QAAQ;AACtB,kBAAY,GAAG,KAAwC,mBAAmB;AAAA,IAC5E,CAAC;AAED,UAAM,cAAc,MAAM;AACxB,aAAO,QAAQ,CAAC,QAAQ;AACtB,oBAAY,IAAI,KAAwC,mBAAmB;AAAA,MAC7E,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT,CAAC,EAAE,KAAKC,WAAU,WAAW,CAAC;AAE9B,SAAO;AACT;AAWO,SAAS,wBAA+C,aAAgB;AAC7E,QAAM,sBAAsB;AAAA,IAC1B;AAAA,IACAC,kBAAiB;AAAA,IACjBA,kBAAiB;AAAA,IACjBA,kBAAiB;AAAA;AAAA,IAEjBA,kBAAiB;AAAA,IACjBA,kBAAiB;AAAA,IACjBA,kBAAiB;AAAA,IACjBA,kBAAiB;AAAA,IACjBA,kBAAiB;AAAA,IACjBA,kBAAiB;AAAA;AAAA,EAEnB,EAAE;AAAA,IACAC,KAAI,CAAC,MAAM;AACT,YAAM,EAAE,qBAAqB,iBAAiB,qBAAqB,IAAI;AACvE,YAAM,kBAAkB,EAAE,oBAAoBC,OAAM,OAAO,UAAU;AACrE,YAAM,cAAc,EAAE,oBAAoBA,OAAM,OAAO,MAAM;AAC7D,YAAM,mBAAwC;AAAA,QAC5C;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,aAAa;AAAA,MACf;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAEA,SAAO;AACT;AAEO,SAAS,oBAAoB,aAA0B,SAA0B;AACtF,SAAO,wBAAwB,WAAW,EAAE;AAAA,IAC1CD,KAAI,MAAM;AACR,aAAO,EAAE,aAAa,qBAAqB,OAAO,EAAE;AAAA,IACtD,CAAC;AAAA,EACH;AACF;AAEO,SAAS,wBAAwB,aAA2B;AACjE,MAAI,CAAC,aAAa;AAChB,WAAO;AAAA,EACT;AACA,QAAM,WAAW;AAAA,IACf;AAAA,IACAD,kBAAiB;AAAA,IACjBA,kBAAiB;AAAA,EACnB,EAAE;AAAA,IACAC,KAAI,CAAC,EAAE,MAAM,UAAU,SAAS,MAAM;AACpC,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF,CAAC;AAAA,IACDF,WAAU;AAAA,MACR,MAAM,YAAY;AAAA,MAClB,UAAU,YAAY;AAAA,MACtB,UAAU,YAAY;AAAA,IACxB,CAAC;AAAA,EACH;AACA,SAAO;AACT;AAEO,SAAS,gCAAgC,aAA0B;AACxE,QAAM,WAAW;AAAA,IACf;AAAA,IACAC,kBAAiB;AAAA,EACnB,EAAE;AAAA,IACAC,KAAI,CAAC,CAAC,OAAO,MAAM,OAAO;AAAA,IAC1BF,WAAU,YAAY,iBAAiB;AAAA,EACzC;AACA,SAAO;AACT;AAEO,SAAS,yBACd,aACA,OACA;AACA,QAAM,aAAa,IAAID,YAErB,CAAC,cAAc;AACf,UAAM,SAAS,IACV,WACA;AACH,gBAAU,KAAK,MAAM;AAAA,IACvB;AAEA,gBAAY,GAAG,OAAO,MAAM;AAE5B,UAAM,cAAc,MAAM;AAExB,kBAAY,IAAI,OAAO,MAAM;AAAA,IAC/B;AACA,WAAO;AAAA,EACT,CAAC;AAED,SAAO;AACT;AAEO,SAAS,cAAc,UAAuC;AArJrE;AAsJE,SAAO;AAAA,IACL,SAAS;AAAA,IACTE,kBAAiB;AAAA,IACjBA,kBAAiB;AAAA,IACjBA,kBAAiB;AAAA,IACjBA,kBAAiB;AAAA,IACjBA,kBAAiB;AAAA,IACjBA,kBAAiB;AAAA,EACnB,EAAE;AAAA,IACAC,KAAI,CAAC,gBAAgB;AA/JzB,UAAAE,KAAAC;AAgKM,YAAM,OAAMD,MAAA,SAAS,gBAAT,OAAAA,MAAwB,YAAY,oBAAoB,SAAS,MAAM;AACnF,cAAOC,MAAA,2BAAK,YAAL,OAAAA,MAAgB;AAAA,IACzB,CAAC;AAAA,IACDL;AAAA,OACE,0BAAS,gBAAT,mBAAsB,YAAtB,aACE,cAAS,YAAY,oBAAoB,SAAS,MAAM,MAAxD,mBAA2D,YAD7D,YAEE;AAAA,IACJ;AAAA,EACF;AACF;AAEO,SAAS,yBAAyB,aAA0B;AACjE,SAAO,yBAAyB,aAAaC,kBAAiB,iBAAiB,EAAE;AAAA,IAC/EC,KAAI,CAAC,CAAC,UAAU,MAAM,UAAU;AAAA,EAClC;AACF;AAMO,SAAS,8BACd,MACA,UAAgD,CAAC,GACjD;AAxLF;AAyLE,MAAI;AAEJ,QAAM,aAAa,IAAIH,YAAgC,CAAC,QAAQ;AAC9D,iBAAa;AACb,WAAO,MAAM,SAAS,YAAY;AAAA,EACpC,CAAC,EAAE,KAAKC,WAAU,MAAM,KAAK,KAAK,mBAAmB,OAAO,CAAC,CAAC,CAAC;AAE/D,QAAM,wBAAuB,aAAQ,yBAAR,YAAgC;AAE7D,QAAM,aAAa,MAAM;AAAA,IACvB,oBAAI,IAAI;AAAA,MACNM,WAAU;AAAA,MACVA,WAAU;AAAA,MACVA,WAAU;AAAA,MACV,GAAG;AAAA,IACL,CAAC;AAAA,EACH;AAEA,QAAM,WAAW,kBAAkB,MAAM,GAAG,UAAU,EAAE;AAAA,IAAU,CAAC,MACjE,yCAAY,KAAK,MAAM,KAAK,EAAE,mBAAmB,OAAO,CAAC;AAAA,EAC3D;AACA,MAAI,KAAK,mBAAmB,OAAO,GAAG;AACpC,6CAAY,KAAK,MAAM,KAAK,KAAK,mBAAmB,OAAO,CAAC;AAAA,EAC9D;AACA,SAAO;AACT;AAMO,SAAS,6BACd,MACA,UACA,UAA+C,CAAC,GAChD;AA5NF;AA6NE,QAAM,oBAAmB,aAAQ,qBAAR,YAA4B;AACrD,QAAM,aAAa;AAAA,IACjB;AAAA,IACAA,WAAU;AAAA,IACVA,WAAU;AAAA,IACVA,WAAU;AAAA,EACZ,EAAE;AAAA,IACA,UAAU,CAAC,MAAM;AACf,YAAM,cAAc,EAAE,yBAAyB,QAAQ;AACvD,UAAI,aAAa;AACf,eAAO,yBAAyB,aAAa,GAAG,gBAAgB;AAAA,MAClE,OAAO;AACL,eAAO,IAAIP,YAAsB,CAAC,cAAc,UAAU,KAAK,MAAS,CAAC;AAAA,MAC3E;AAAA,IACF,CAAC;AAAA,IACDC,WAAU,KAAK,yBAAyB,QAAQ,CAAkC;AAAA,EACpF;AAEA,SAAO;AACT;AAEO,SAAS,8BACd,aAC+C;AAC/C,QAAM,WAAW;AAAA,IACf;AAAA,IACAC,kBAAiB;AAAA,EACnB,EAAE;AAAA,IACAC,KAAI,MAAM,YAAY,WAAW;AAAA,IACjCF,WAAU,YAAY,WAAW;AAAA,EACnC;AACA,SAAO;AACT;AAEO,SAAS,gCACd,MACA,EAAE,MAAM,SAAS,GACjB,UAA+C,CAAC,GACL;AAnQ7C;AAoQE,QAAM,oBAAmB,aAAQ,qBAAR,YAA4B;AACrD,QAAM,oBAAoB,CAAC,gBAAmC;AAC5D,QAAI,UAAU;AACd,QAAI,MAAM;AACR,gBAAU,WAAW,YAAY,SAAS;AAAA,IAC5C;AACA,QAAI,UAAU;AACZ,gBAAU,WAAW,YAAY,aAAa;AAAA,IAChD;AACA,WAAO;AAAA,EACT;AACA,QAAM,aAAa;AAAA,IACjB;AAAA,IACAM,WAAU;AAAA,IACVA,WAAU;AAAA,IACVA,WAAU;AAAA,EACZ,EAAE;AAAA,IACA,UAAU,CAAC,MAAM;AACf,YAAM,cAAc,MAAM,KAAK,EAAE,mBAAmB,OAAO,CAAC,EAAE;AAAA,QAAK,CAAC,MAClE,kBAAkB,CAAC;AAAA,MACrB;AACA,UAAI,aAAa;AACf,eAAO,yBAAyB,aAAa,GAAG,gBAAgB;AAAA,MAClE,OAAO;AACL,eAAO,IAAIP,YAAsB,CAAC,cAAc,UAAU,KAAK,MAAS,CAAC;AAAA,MAC3E;AAAA,IACF,CAAC;AAAA,IACDC,WAAU,MAAM,KAAK,KAAK,mBAAmB,OAAO,CAAC,EAAE,KAAK,CAAC,MAAM,kBAAkB,CAAC,CAAC,CAAC;AAAA,EAC1F;AAEA,SAAO;AACT;AAUO,SAAS,8BAA8B,aAAsC;AAClF,MAAI,OAAO,gBAAgB,aAAa;AACtC,WAAO,IAAID,YAA0D;AAAA,EACvE;AACA,SAAO,yBAAyB,aAAaE,kBAAiB,iBAAiB,EAAE;AAAA,IAC/EC,KAAI,CAAC,CAAC,iBAAiB,MAAM;AAC3B,aAAO;AAAA,QACL,SAAS;AAAA,QACT,YAAY,YAAY;AAAA,MAC1B;AAAA,IACF,CAAC;AAAA,IACDF,WAAU,EAAE,SAAS,YAAY,YAAY,YAAY,YAAY,WAAW,CAAC;AAAA,EACnF;AACF;;;ADrRO,SAAS,iBACd,QACA,MACA,SACA,gBACA,SACoB;AACpB,QAAM,EAAE,iBAAiB,IAAI;AAE7B,QAAM,mBAAmB,CAACO,SAAsBC,sBAAuC;AACrF,QAAI,YAAY;AAChB,YAAQD,SAAQ;AAAA,MACd,KAAKE,OAAM,OAAO;AAChB,oBAAYD,kBAAiB;AAC7B;AAAA,MACF,KAAKC,OAAM,OAAO;AAChB,oBAAYD,kBAAiB;AAC7B;AAAA,MACF,KAAKC,OAAM,OAAO;AAChB,oBAAYD,kBAAiB;AAC7B;AAAA,MACF;AACE;AAAA,IACJ;AACA,WAAO;AAAA,EACT;AAEA,QAAM,kBAAkB,wBAAwB,gBAAgB,EAAE;AAAA,IAChEE,KAAI,CAAC,UAAU;AACb,aAAO,iBAAiB,QAAQ,MAAM,WAA+B;AAAA,IACvE,CAAC;AAAA,IACDC,WAAU,iBAAiB,QAAQ,gBAAgB,CAAC;AAAA,EACtD;AAEA,QAAM,iBAAiB,IAAIC,SAAiB;AAC5C,QAAM,SAAS,CAAO,YAAsB,mBAA+C;AACzF,QAAI;AACF,iEAAmB;AAEnB,qBAAe,KAAK,IAAI;AACxB,cAAQ,QAAQ;AAAA,QACd,KAAKH,OAAM,OAAO;AAChB,gBAAM,iBAAiB;AAAA,YACrB,kCAAc,CAAC,iBAAiB;AAAA,YAChC;AAAA,YACA;AAAA,UACF;AACA,iBAAO,iBAAiB;AAAA,QAC1B,KAAKA,OAAM,OAAO;AAChB,gBAAM,iBAAiB;AAAA,YACrB,kCAAc,CAAC,iBAAiB;AAAA,YAChC;AAAA,YACA;AAAA,UACF;AACA,iBAAO,iBAAiB;AAAA,QAC1B,KAAKA,OAAM,OAAO;AAChB,gBAAM,iBAAiB;AAAA,YACrB,kCAAc,CAAC,iBAAiB;AAAA,YAChC;AAAA,YACA;AAAA,UACF;AACA,iBAAO,iBAAiB;AAAA,QAC1B;AACE,gBAAM,IAAI,UAAU,oCAAoC;AAAA,MAC5D;AAAA,IACF,SAAS,GAAG;AACV,UAAI,WAAW,aAAa,OAAO;AACjC,2CAAU;AACV,eAAO;AAAA,MACT,OAAO;AACL,cAAM;AAAA,MACR;AAAA,IACF,UAAE;AACA,qBAAe,KAAK,KAAK;AAAA,IAE3B;AAAA,EACF;AAEA,QAAM,YAAoB,YAAY,QAAQ;AAC9C,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA,iBAAiB,eAAe,aAAa;AAAA,EAC/C;AACF;AAEO,SAAS,oBAAoB;AAClC,MAAI,QAAQ;AAEZ,QAAM,iBAAiB,IAAIG,SAAiB;AAE5C,QAAM,iBAAiB,IAAIA,SAAiB;AAE5C,QAAM,SAAS,CAAO,eAAyB;AAC7C,mBAAe,KAAK,IAAI;AACxB,YAAQ,kCAAc,CAAC;AACvB,mBAAe,KAAK,KAAK;AACzB,mBAAe,KAAK,KAAK;AAAA,EAC3B;AACA,QAAM,YAAoB,YAAY,QAAQ;AAC9C,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,iBAAiB,eAAe,aAAa;AAAA,IAC7C,iBAAiB,eAAe,aAAa;AAAA,EAC/C;AACF;;;AKhJA;AAAA,EACE,SAAAC;AAAA,OAKK;AACP,SAAS,uBAAuB;AAazB,SAAS,oBACd,MACA,MACA,YACA;AACA,QAAM,sBAAsB,IAAI,gBAAoC,MAAS;AAE7E,QAAM,yBAAyB,6BAA6B,MAAM,IAAI;AAEtE,QAAM,uBAAuB,CAAO,OAAoD,sBAApD,IAAoD,mBAApD,IAAY,UAAiC,CAAC,GAAM;AA7B1F;AA8BI,QAAI,YAAY;AACd,YAAM,WAAW,YAAY,QAAQ,QAAQ,EAAE,OAAO,GAAG,IAAI,EAAE;AAC/D,YAAM,WAAW,MAAM,WAAW,YAAY,KAAK;AACnD,0BAAoB;AAAA,QAClB,OAAO,aAAa,WAAW,iBAAiB,MAAM,WAAW,SAAS,IAAI,KAAK;AAAA,MACrF;AAAA,IACF,WAAW,MAAM;AACf,UAAI,MAAM,oCAAoC,IAAI,aAAa,EAAE,GAAG;AACpE,YAAM,KAAK,mBAAmB,MAAM,IAAI,QAAQ,KAAK;AACrD,YAAM,kBAAqC,UAAK,gBAAgB,IAAI,MAAzB,YAA8B;AACzE,UAAI,mBAAmB,MAAM,OAAO,WAAW;AAC7C,YAAI;AAAA,UACF,0CAA0C,EAAE,4DAA4D,cAAc;AAAA,QACxH;AAAA,MACF;AACA,UAAI,cAAsC;AAC1C,UAAI,SAAS;AACX,uBAAc,UAAK,iBAAiB,oBAAoBC,OAAM,OAAO,UAAU,MAAjE,mBAAoE;AAAA,eAC3E,SAAS,cAAc;AAC9B,uBAAc,UAAK,iBAAiB,oBAAoBA,OAAM,OAAO,MAAM,MAA7D,mBAAgE;AAAA,MAChF;AACA,YAAM,aACH,OAAO,aAAa,CAAC,eACrB,OAAO,cAAa,2CAAa,iBAAiB,MAAM,WAAW;AACtE,0BAAoB,KAAK,aAAa,KAAK,cAAc;AAAA,IAC3D;AAAA,EACF;AACA,QAAM,YAAoB,YAAY,qBAAqB;AAC3D,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;;;AC5DO,SAAS,sBAAsB,MAAY;AAChD,QAAM,aAAa,CAAC,eAAyB;AAC3C,SAAK,WAAW,UAAU;AAAA,EAC5B;AACA,QAAM,YAAoB,YAAY,mBAAmB;AACzD,SAAO,EAAE,WAAW,WAAW;AACjC;;;ACLO,SAAS,gCAAgC,aAA0B;AACxE,QAAM,YAAY,YAAY,oBAAoB;AAClD,QAAM,4BAA4B,gCAAgC,WAAW;AAC7E,SAAO,EAAE,WAAW,0BAA0B;AAChD;;;ACNA,SAAS,SAAAC,cAAa;AAKf,SAAS,yBAAyB,UAAuC;AAC9E,MAAI,iBAA+B;AACnC,UAAQ,SAAS,QAAQ;AAAA,IACvB,KAAKC,OAAM,OAAO;AAChB,uBAAiB;AACjB;AAAA,IACF,KAAKA,OAAM,OAAO;AAChB,uBAAiB;AACjB;AAAA,IAEF;AACE;AAAA,EACJ;AACA,QAAM,YAAoB,YAAY,cAAc;AACpD,QAAM,qBAAqB,cAAc,QAAQ;AAEjD,SAAO,EAAE,WAAW,mBAAmB;AACzC;;;ACrBO,SAAS,qBAAqB,aAA0B;AAC7D,QAAM,eAAe,wBAAwB,WAAW;AACxD,SAAO,EAAE,WAAW,uBAAuB,aAAa;AAC1D;;;ACJO,SAAS,uBAAuB;AACrC,QAAM,YAAoB,YAAY,kBAAkB;AACxD,SAAO;AAAA,IACL;AAAA,EACF;AACF;;;ACLA,SAAS,iBAAiB,aAAAC,kBAAiB;AAC3C,SAAS,mBAAAC,kBAAiB,WAAAC,UAAS,MAAM,OAAAC,MAAK,WAAW,aAAa;;;ACKtE,SAAS,cAAAC,aAAY,UAAAC,SAAQ,OAAAC,YAAW;AAGjC,IAAM,YAAY;AAAA,EACvB,MAAM;AAAA,EACN,aAAa;AACf;AAGA,SAAsB,YACpB,IACA,IAEA;AAAA,6CAHA,kBACA,SACA,UAA8B,CAAC,GAC/B;AACA,UAAM,EAAE,UAAU,uBAAuB,MAAM,IAAI;AAEnD,UAAM,iBAAiB,YAAY,SAAS;AAAA,MAC1C;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAYO,SAAS,wBACd,MACA,OACA,WACA;AACA,QAAM,SAAS,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK;AAEpD,QAAM,oBAAoB,mBAAmB,IAAI,EAAE;AAAA,IACjDC;AAAA,MACE,CAAC,CAAC,EAAE,EAAE,EAAE,YAAY,MAClB,UAAU,UAAc,iBAAiB,UAAa,OAAO,SAAS,YAAiB;AAAA,IAC3F;AAAA,IACAC,KAAI,CAAC,CAAC,SAAS,aAAa,EAAE,YAAY,MAAM;AAC9C,YAAM,MAAM;AAAA,QACV;AAAA,QACA,OAAO;AAAA,QACP,MAAM;AAAA,MACR;AACA,6CAAY;AACZ,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAEA,MAAI;AACJ,QAAM,sBAAsB,IAAIC,YAAoB,CAAC,eAAe;AAClE,0BAAsB;AAAA,EACxB,CAAC;AAED,QAAM,OAAO,CAAO,OAA0D,sBAA1D,IAA0D,mBAA1D,SAAqB,UAA8B,CAAC,GAAM;AAC5E,wBAAoB,KAAK,IAAI;AAC7B,QAAI;AACF,YAAM,YAAY,KAAK,kBAAkB,SAAS,iBAAE,OAAO,OAAO,CAAC,KAAM,QAAS;AAAA,IACpF,UAAE;AACA,0BAAoB,KAAK,KAAK;AAAA,IAChC;AAAA,EACF;AAEA,SAAO,EAAE,mBAAmB,qBAAqB,KAAK;AACxD;AAEO,SAAS,wBAAwB,MAAY;AAClD,QAAM,iBAAiB,mBAAmB,IAAI;AAE9C,QAAM,OAAO,CAAO,SAAiB;AACnC,UAAM,MAAM,MAAM,KAAK,iBAAiB,gBAAgB,IAAI;AAC5D,WAAO;AAAA,EACT;AAEA,QAAM,OAAO,CAAO,MAAc,gBAA6B;AAC7D,UAAM,MAAM,MAAM,KAAK,iBAAiB,gBAAgB,MAAM,WAAW;AACzE,WAAO;AAAA,EACT;AAEA,SAAO,EAAE,gBAAgB,MAAM,KAAK;AACtC;;;ADxCA,IAAM,UAAU,IAAI,YAAY;AAChC,IAAM,UAAU,IAAI,YAAY;AAEhC,IAAM,kBAA+D,oBAAI,IAAI;AAE7E,IAAM,SAAS,CAAC,YAAuC,QAAQ,OAAO,KAAK,UAAU,OAAO,CAAC;AAE7F,IAAM,SAAS,CAAC,YACd,KAAK,MAAM,QAAQ,OAAO,OAAO,CAAC;AAE7B,SAAS,UAAU,MAAY,SAAuB;AAjE7D;AAkEE,QAAM,sBAAsB,IAAIC,SAAc;AAE9C,QAAM,wBAAwB,MAAG;AApEnC,QAAAC,KAAAC,KAAA;AAqEI,aAAAD,MAAA,KAAK,eAAL,gBAAAA,IAAiB,aAAY,KAC5B,CAAC,GAACC,MAAA,KAAK,eAAL,gBAAAA,IAAiB,YAAW,iBAAgB,UAAK,eAAL,mBAAiB,SAAS,QAAQ,IAAI;AAAA;AAEvF,QAAM,EAAE,gBAAgB,gBAAgB,cAAc,mBAAmB,IAAI,4BAAW,CAAC;AAEzF,QAAM,QAAQ,sCAAgB,UAAU;AAExC,QAAM,cAAc,kDAAsB,UAAU;AAEpD,MAAI,aAAa;AACjB,MAAI,CAAC,gBAAgB,IAAI,IAAI,GAAG;AAC9B,iBAAa;AAAA,EACf;AACA,QAAM,YAAW,qBAAgB,IAAI,IAAI,MAAxB,YAA6B,oBAAI,IAAiC;AACnF,QAAM,kBAAiB,cAAS,IAAI,KAAK,MAAlB,YAAuB,IAAIF,SAAoB;AACtE,WAAS,IAAI,OAAO,cAAc;AAClC,kBAAgB,IAAI,MAAM,QAAQ;AAElC,MAAI,YAAY;AAEd,UAAM,EAAE,kBAAkB,IAAI,wBAAwB,MAAM,CAAC,OAAO,WAAW,CAAC;AAChF,sBAAkB,KAAK,UAAU,mBAAmB,CAAC,EAAE,UAAU,cAAc;AAAA,EACjF;AACA,QAAM,EAAE,gBAAgB,MAAM,gBAAgB,IAAI,wBAAwB,IAAI;AAE9E,QAAM,sBAAsB,0CAAkB;AAG9C,QAAM,qBAAqB;AAAA,IACzB,eAAe;AAAA,MACbG,KAAI,CAAC,QAAQ;AACX,cAAM,gBAAgB,oBAAoB,IAAI,OAAO;AACrD,cAAM,aAAa,iCAAK,gBAAL,EAAoB,MAAM,IAAI,KAAK;AACtD,YAAI,uBAAuB,UAAU,GAAG;AACtC,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AAAA,IACA,eAAe;AAAA,MACbA,KAAI,CAAC,CAAC,KAAK,WAAW,MAAM;AAC1B,eAAO,iCAAK,MAAL,EAAU,MAAM,YAAY;AAAA,MACrC,CAAC;AAAA,IACH;AAAA,EACF,EAAE;AAAA,IACA,KAA6D,CAAC,KAAK,UAAU;AAlHjF,UAAAF;AAoHM,UAAI,CAAC,OAAO;AACV,eAAO;AAAA,MACT;AAEA,UACE,QAAQ,SACR,IAAI,KAAK,CAAC,QAAK;AA1HvB,YAAAA,KAAAC;AA0H0B,iBAAAD,MAAA,IAAI,SAAJ,gBAAAA,IAAU,gBAAaC,MAAA,MAAM,SAAN,gBAAAA,IAAY,aAAY,IAAI,OAAO,MAAM;AAAA,OAAE,GACpF;AACA,cAAM,eAAe,IAAI,UAAU,CAAC,QAAQ,IAAI,OAAO,MAAM,EAAE;AAC/D,YAAI,eAAe,IAAI;AACrB,gBAAM,cAAc,IAAI,YAAY;AACpC,cAAI,YAAY,IAAI,iCACf,QADe;AAAA,YAElB,WAAW,YAAY;AAAA,YACvB,gBAAeD,MAAA,MAAM,kBAAN,OAAAA,MAAuB,MAAM;AAAA,UAC9C;AAAA,QACF;AAEA,eAAO,CAAC,GAAG,GAAG;AAAA,MAChB;AACA,aAAO,CAAC,GAAG,KAAK,KAAK;AAAA,IACvB,GAAG,CAAC,CAAC;AAAA,IACL,UAAU,mBAAmB;AAAA,EAC/B;AAEA,QAAM,aAAa,IAAIG,iBAAyB,KAAK;AAErD,QAAM,sBAAsB,0CAAkB;AAE9C,QAAM,OAAO,CAAO,YAAoB;AACtC,eAAW,KAAK,IAAI;AACpB,QAAI;AACF,YAAM,cAAc,MAAM,gBAAgB,OAAO;AACjD,YAAM,mBAAmB,oBAAoB,iCACxC,cADwC;AAAA,QAE3C,QAAQ,sBAAsB;AAAA,MAChC,EAAC;AACD,YAAM,YAAY,KAAK,kBAAkB,kBAAkB;AAAA,QACzD,UAAU;AAAA,QACV;AAAA,MACF,CAAC;AACD,aAAO;AAAA,IACT,UAAE;AACA,iBAAW,KAAK,KAAK;AAAA,IACvB;AAAA,EACF;AAEA,QAAM,SAAS,CAAO,SAAiB,wBAA8C;AACnF,UAAM,YAAY,KAAK,IAAI;AAC3B,UAAM,kBACJ,OAAO,wBAAwB,WAC3B,EAAE,IAAI,qBAAqB,SAAS,IAAI,UAAU,IAClD;AACN,eAAW,KAAK,IAAI;AACpB,QAAI;AACF,YAAM,gBAAgB,MAAM,KAAK,iBAAiB,gBAAgB,SAAS,eAAe;AAC1F,YAAM,uBAAuB,oBAAoB,aAAa;AAC9D,YAAM,YAAY,KAAK,kBAAkB,sBAAsB;AAAA,QAC7D,OAAO;AAAA,QACP,UAAU;AAAA,MACZ,CAAC;AACD,aAAO;AAAA,IACT,UAAE;AACA,iBAAW,KAAK,KAAK;AAAA,IACvB;AAAA,EACF;AAEA,WAAS,UAAU;AACjB,wBAAoB,KAAK;AACzB,wBAAoB,SAAS;AAC7B,oBAAgB,OAAO,IAAI;AAAA,EAC7B;AACA,OAAK,KAAKC,WAAU,cAAc,OAAO;AAEzC,SAAO;AAAA,IACL,mBAAmB;AAAA,IACnB,qBAAqB;AAAA,IACrB;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,uBACP,KAC4B;AAC5B,SAAQ,IAA0B,UAAU;AAC9C;;;AErMO,SAAS,kBAAkB;AAChC,QAAM,2BAA2B,CAAO,SAAe;AACrD,QAAI,KAAK,0BAA0B,IAAI;AACvC,UAAM,KAAK,WAAW;AAAA,EACxB;AACA,QAAM,YAAoB,YAAY,oBAAoB;AAC1D,SAAO,EAAE,WAAW,oCAAoC,yBAAyB;AACnF;;;ACPO,SAAS,kBAAkB;AAChC,QAAM,2BAA2B,CAAO,SAAe;AACrD,QAAI,KAAK,0BAA0B,IAAI;AACvC,UAAM,KAAK,WAAW;AAAA,EACxB;AACA,QAAM,YAAoB,YAAY,oBAAoB;AAC1D,SAAO,EAAE,WAAW,oCAAoC,yBAAyB;AACnF;;;ACVO,SAAS,kBAAkB;AAChC,QAAM,YAAoB,CAAC,YAAY,QAAQ,GAAG,YAAY,aAAa,CAAC,EAAE,KAAK,GAAG;AACtF,SAAO,EAAE,UAAU;AACrB;;;ACHO,SAAS,mBAAmB;AACjC,QAAM,YAAoB,CAAC,YAAY,QAAQ,GAAG,YAAY,qBAAqB,CAAC,EAAE,KAAK,GAAG;AAC9F,SAAO,EAAE,UAAU;AACrB;;;ACHO,SAAS,sBAAsB;AAEpC,QAAM,YAAY;AAClB,SAAO,EAAE,UAAU;AACrB;;;ACNO,SAAS,mBAAmB;AACjC,QAAM,YAAY;AAClB,SAAO,EAAE,UAAU;AACrB;;;ACKA,SAAS,aAAAC,YAAW,kBAAkB;AACtC,SAAS,OAAAC,MAAK,cAAAC,aAAY,aAAAC,kBAAiB;AAUpC,SAAS,gBAAgB,OAAyB;AACvD,QAAM,gBAAgB;AAAA,IACpB;AAAA,IACA,WAAW;AAAA,IACX,WAAW;AAAA,IACX,WAAW;AAAA,IACX,WAAW;AAAA,EACb;AAEA,SAAO;AACT;AAEO,SAAS,mBAAmB,UAA4B,QAAsB;AACnF,QAAM,aAAa,IAAIC,YAA6B,CAAC,cAAc;AACjE,UAAM,gBAAgB,MAAM;AAC1B,gBAAU,KAAK,KAAK;AAAA,IACtB;AAEA,WAAO,QAAQ,CAAC,QAAQ;AAEtB,YAAM,GAAG,KAAK,aAAa;AAAA,IAC7B,CAAC;AAED,UAAM,cAAc,MAAM;AACxB,aAAO,QAAQ,CAAC,QAAQ;AAEtB,cAAM,IAAI,KAAK,aAAa;AAAA,MAC9B,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT,CAAC,EAAE,KAAKC,WAAU,KAAK,CAAC;AAExB,SAAO;AACT;AAKA,SAAS,mBACP,MACA,SACA,uBAAuB,MAC6C;AACpE,QAAM,mBAAmB,KAAK;AAC9B,QAAM,kBAAkB,CAAC,kBAAkB,GAAG,MAAM,KAAK,KAAK,mBAAmB,OAAO,CAAC,CAAC;AAC1F,QAAM,kBAAoC,CAAC;AAE3C,kBAAgB,QAAQ,CAAC,gBAAgB;AACvC,YAAQ,QAAQ,CAAC,WAAW;AAC1B,YAAM,mBAAmB,MAAM;AAAA,QAC7B,YAAY,kBAAkB,OAAO;AAAA,MACvC,EACG;AAAA,QACC,CAAC,UACC,MAAM,WAAW;AAAA,SAEhB,CAAC,wBAAwB,MAAM;AAAA,MACpC,EACC,IAAI,CAAC,UAA0B;AAC9B,eAAO;AAAA,UACL;AAAA,UACA,aAAa;AAAA,UACb,QAAQ,MAAM;AAAA,QAChB;AAAA,MACF,CAAC;AAEH,sBAAgB,KAAK,GAAG,gBAAgB;AAAA,IAC1C,CAAC;AAAA,EACH,CAAC;AAED,SAAO,EAAE,iBAAiB,cAAc,gBAAgB;AAC1D;AAKA,SAAS,wBACP,aACA,YACA,uBAAuB,OACL;AAClB,QAAM,EAAE,SAAS,MAAM,KAAK,IAAI;AAChC,QAAM,mBAAmB,MAAM,KAAK,YAAY,kBAAkB,OAAO,CAAC,EACvE;AAAA,IACC,CAAC,SACE,CAAC,WAAW,QAAQ,SAAS,IAAI,MAAM,OACvC,CAAC,QAAQ,IAAI,SAAS,UACtB,CAAC,QAAQ,IAAI,cAAc;AAAA,KAE3B,CAAC,wBAAwB,IAAI;AAAA,EAClC,EACC,IAAI,CAAC,UAA0B;AAC9B,WAAO;AAAA,MACL;AAAA,MACA,aAAa;AAAA,MACb,QAAQ,MAAM;AAAA,IAChB;AAAA,EACF,CAAC;AAEH,SAAO;AACT;AAOO,SAAS,0BACd,MACA,SACA,SACgF;AAlIlF;AAmIE,QAAM,wBAAuB,aAAQ,yBAAR,YAAgC;AAC7D,QAAM,wBAAgC,aAAQ,mBAAR,YAA0B;AAChE,QAAM,aAAa,MAAM;AAAA,KACvB,oBAAI,IAAI;AAAA,MACNC,WAAU;AAAA,MACVA,WAAU;AAAA,MACVA,WAAU;AAAA,MACVA,WAAU;AAAA,MACVA,WAAU;AAAA,MACVA,WAAU;AAAA,MACVA,WAAU;AAAA,MACVA,WAAU;AAAA,MACV,GAAG;AAAA,IACL,CAAC,GAAE,OAAO;AAAA,EACZ;AAEA,QAAM,aAAa,kBAAkB,MAAM,GAAG,UAAU,EAAE;AAAA,IACxDC,KAAI,CAACC,UAAS;AACZ,YAAM,OAAO,mBAAmBA,OAAM,SAAS,oBAAoB;AACnE,UAAI,MAAM,yCAAyC,KAAK,gBAAgB,MAAM,KAAK,IAAI;AACvF,aAAO;AAAA,IACT,CAAC;AAAA,IACDH,WAAU,mBAAmB,MAAM,SAAS,oBAAoB,CAAC;AAAA,EACnE;AAEA,SAAO;AACT;AAEO,SAAS,4BACd,aACA,iBAC8B;AAC9B,QAAM,aAAa,yBAAyB,aAAa,GAAG,sBAAsB,EAAE;AAAA,IAClFE,KAAI,CAACE,iBAAgB;AACnB,YAAM,OAAO,wBAAwBA,cAAa,eAAe;AACjE,UAAI,MAAM,yCAAyC,KAAK,MAAM,KAAK,IAAI;AACvE,aAAO;AAAA,IACT,CAAC;AAAA,IACDJ,WAAU,wBAAwB,aAAa,eAAe,CAAC;AAAA,EACjE;AAEA,SAAO;AACT;AAEO,SAAS,mBACd,aACA,OACA;AACA,QAAM,aAAa,IAAID,YAErB,CAAC,cAAc;AACf,UAAM,SAAS,IACV,WACA;AACH,gBAAU,KAAK,MAAM;AAAA,IACvB;AAEA,gBAAY,GAAG,OAAO,MAAM;AAE5B,UAAM,cAAc,MAAM;AAExB,kBAAY,IAAI,OAAO,MAAM;AAAA,IAC/B;AACA,WAAO;AAAA,EACT,CAAC;AAED,SAAO;AACT;AAEO,SAAS,2BAA2B,aAA+B;AACxE,SAAO,mBAAmB,aAAa,WAAW,qBAAqB;AACzE;AAEO,SAAS,sBAAsB,OAAc;AAClD,SAAO,mBAAmB,OAAO,WAAW,cAAc,EAAE;AAAA,IAC1DG,KAAI,CAAC,CAAC,UAAU,MAAM,UAAU;AAAA,EAClC;AACF;;;AC/MA,SAAS,UAAAG,SAAQ,sBAAsB,WAAW,OAAAC,MAAK,IAAI,WAAW,eAAe;AAQ9E,SAAS,4BAA4B,aAAiC,gBAAgB,KAAM;AACjG,MAAI,gBAAgB,KAAM,QAAO,GAAG,KAAK;AACzC,QAAM,QAAQ,UAAU,aAAa,aAAa,EAAE,SAAS,KAAK,CAAC,EAAE,KAAKA,KAAI,MAAM,IAAI,CAAC;AACzF,QAAM,eAAoC,MAAM;AAAA,IAC9C,QAAQ;AAAA,MACN,MAAM;AAAA,MACN,MAAM,MAAMD,QAAO,GAAG,KAAK,GAAG,aAAa,KAAK,UAAU,KAAK,CAAC,CAAC;AAAA,IACnE,CAAC;AAAA,IACD,qBAAqB;AAAA,EACvB;AACA,SAAO;AACT;;;ACTA,SAAS,mBAAwC,KAAa,OAAgB;AAC5E,MAAI,OAAO,iBAAiB,aAAa;AACvC,QAAI,MAAM,iCAAiC;AAC3C;AAAA,EACF;AAEA,MAAI;AACF,QAAI,OAAO;AACT,YAAM,mBAAmB,OAAO;AAAA,QAC9B,OAAO,QAAQ,KAAK,EAAE,OAAO,CAAC,CAAC,EAAEE,MAAK,MAAMA,WAAU,EAAE;AAAA,MAC1D;AACA,mBAAa,QAAQ,KAAK,KAAK,UAAU,gBAAgB,CAAC;AAAA,IAC5D;AAAA,EACF,SAAS,OAAO;AACd,QAAI,MAAM,wCAAwC,KAAK,EAAE;AAAA,EAC3D;AACF;AAMA,SAAS,qBAA0C,KAA4B;AAC7E,MAAI,OAAO,iBAAiB,aAAa;AACvC,QAAI,MAAM,iCAAiC;AAC3C,WAAO;AAAA,EACT;AAEA,MAAI;AACF,UAAM,OAAO,aAAa,QAAQ,GAAG;AACrC,QAAI,CAAC,MAAM;AACT,UAAI,KAAK,iBAAiB,GAAG,mCAAmC;AAChE,aAAO;AAAA,IACT;AACA,WAAO,KAAK,MAAM,IAAI;AAAA,EACxB,SAAS,OAAO;AACd,QAAI,MAAM,0CAA0C,KAAK,EAAE;AAC3D,WAAO;AAAA,EACT;AACF;AAMO,SAAS,4BACd,KACyD;AACzD,SAAO;AAAA,IACL,MAAM,MAAM,qBAAwB,GAAG;AAAA,IACvC,MAAM,CAAC,UAAa,mBAAsB,KAAK,KAAK;AAAA,EACtD;AACF;;;AC5DA,IAAM,mBAAmB,GAAG,SAAS;AAmC9B,IAAM,qBAAuC;AAAA,EAClD,cAAc;AAAA,EACd,cAAc;AAAA,EACd,eAAe;AAAA,EACf,eAAe;AAAA,EACf,UAAU;AACZ;AASA,IAAM,EAAE,MAAM,KAAK,IAAI,4BAA6C,gBAAgB;AAM7E,SAAS,gBACd,aAIA,cAAuB,OACjB;AACN,MAAI,gBAAgB,MAAM;AACxB;AAAA,EACF;AACA,OAAK,WAAW;AAClB;AASO,SAAS,gBACd,UAKA,cAAuB,OACL;AAtFpB;AAuFE,QAAM,WAA6B;AAAA,IACjC,eAAc,0CAAU,iBAAV,YAA0B,mBAAmB;AAAA,IAC3D,eAAc,0CAAU,iBAAV,YAA0B,mBAAmB;AAAA,IAC3D,gBAAe,0CAAU,kBAAV,YAA2B,mBAAmB;AAAA,IAC7D,gBAAe,0CAAU,kBAAV,YAA2B,mBAAmB;AAAA,IAC7D,WAAU,0CAAU,aAAV,YAAsB,mBAAmB;AAAA,EACrD;AAEA,MAAI,aAAa;AACf,WAAO;AAAA,EACT,OAAO;AACL,UAAM,oBAAoB,KAAK;AAC/B,UAAM,SAAS,kCAAK,WAAc,gDAAqB,CAAC;AACxD,WAAO;AAAA,EACT;AACF;", "names": ["regex", "Track", "Track", "Track", "Track", "Subject", "map", "startWith", "ParticipantEvent", "RoomEvent", "Track", "Observable", "map", "startWith", "Track", "Track", "map", "startWith", "RoomEvent", "Track", "startWith", "RoomEvent", "map", "Track", "participant", "_a", "room", "kind", "Observable", "startWith", "ParticipantEvent", "map", "Track", "_a", "_b", "RoomEvent", "source", "localParticipant", "Track", "map", "startWith", "Subject", "Track", "Track", "Track", "Track", "RoomEvent", "BehaviorSubject", "Subject", "map", "Observable", "filter", "map", "filter", "map", "Observable", "Subject", "_a", "_b", "map", "BehaviorSubject", "RoomEvent", "RoomEvent", "map", "Observable", "startWith", "Observable", "startWith", "RoomEvent", "map", "room", "participant", "concat", "map", "value"]}