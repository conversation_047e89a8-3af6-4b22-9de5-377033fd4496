"use client";

import { useState, useRef } from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Room<PERSON>udio<PERSON><PERSON><PERSON>, AgentState } from "@livekit/components-react";
import { useTheme } from "next-themes";
import { toast } from "sonner";

// Components
import SimpleVoiceAssistant from "@/components/voice/SimpleVoiceAssistant";
import ControlBar from "@/components/voice/ControlBar";
import AssistantForm from "@/components/voice/AssistantForm";
import AssistantList from "@/components/voice/AssistantList";
import { NoAgentNotification } from "@/components/voice/NoAgentNotification";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";

// Hooks
import { useVoiceAssistants } from "@/hooks/useVoiceAssistants";

const VoiceAssistantPage: React.FC = () => {
  const { theme } = useTheme();
  const darkMode = theme === "dark";
  
  // State
  const [agentState, setAgentState] = useState<AgentState>("disconnected");
  const [activeTab, setActiveTab] = useState("assistants");
  
  // Refs
  const conversationRef = useRef<HTMLDivElement>(null);
  
  // Custom hook
  const {
    assistants,
    voices,
    models,
    selectedAssistant,
    connectionDetails,
    connectingAssistantId,
    loading,
    error,
    assistantName,
    systemPrompt,
    selectedVoice,
    selectedModel,
    setAssistantName,
    setSystemPrompt,
    setSelectedVoice,
    setSelectedModel,
    createAssistant,
    connectAssistant,
    disconnectAssistant,
    updateAssistant,
    deleteAssistant,
    setError,
  } = useVoiceAssistants();

  // Handlers
  const handleAgentStateChange = (state: AgentState) => {
    setAgentState(state);
  };

  const handleConnectAssistant = async (assistant: any) => {
    try {
      await connectAssistant(assistant);
      toast.success(`Connected to ${assistant.name}`);
    } catch (err) {
      toast.error("Failed to connect to assistant");
    }
  };

  const handleDisconnectAssistant = () => {
    disconnectAssistant();
    toast.info("Disconnected from assistant");
  };

  const handleCreateAssistant = async () => {
    try {
      await createAssistant();
      toast.success("Assistant created successfully");
    } catch (err) {
      toast.error("Failed to create assistant");
    }
  };

  const handleEditAssistant = (assistant: any) => {
    // TODO: Implement edit modal
    toast.info("Edit functionality coming soon");
  };

  const handleDeleteAssistant = async (id: string) => {
    if (confirm("Are you sure you want to delete this assistant?")) {
      try {
        await deleteAssistant(id);
        toast.success("Assistant deleted successfully");
      } catch (err) {
        toast.error("Failed to delete assistant");
      }
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto p-6 space-y-6">
        {/* Header */}
        <div className="space-y-2">
          <h1 className="text-3xl font-bold tracking-tight text-foreground">
            Voice Agents
          </h1>
          <p className="text-lg text-muted-foreground">
            Create and manage AI voice assistants for your calls
          </p>
        </div>

        {/* Error Alert */}
        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Main Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-2 h-10">
            <TabsTrigger value="assistants" className="text-sm">
              Assistants
            </TabsTrigger>
            <TabsTrigger value="conversation" className="text-sm">
              Live Conversation
            </TabsTrigger>
          </TabsList>

          <TabsContent value="assistants" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Create Assistant Form */}
              <div>
                <AssistantForm
                  assistantName={assistantName}
                  systemPrompt={systemPrompt}
                  selectedVoice={selectedVoice}
                  selectedModel={selectedModel}
                  voices={voices}
                  models={models}
                  loading={loading}
                  onNameChange={setAssistantName}
                  onPromptChange={setSystemPrompt}
                  onVoiceChange={setSelectedVoice}
                  onModelChange={setSelectedModel}
                  onSubmit={handleCreateAssistant}
                />
              </div>

              {/* Assistant List */}
              <div>
                <AssistantList
                  assistants={assistants}
                  selectedAssistant={selectedAssistant}
                  connectionDetails={connectionDetails}
                  connectingAssistantId={connectingAssistantId}
                  onConnect={handleConnectAssistant}
                  onEdit={handleEditAssistant}
                  onDelete={handleDeleteAssistant}
                />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="conversation" className="space-y-6">
            {connectionDetails ? (
              <Card>
                <CardHeader>
                  <CardTitle>
                    Live Conversation with {selectedAssistant?.name}
                  </CardTitle>
                  <CardDescription>
                    You are now connected to your voice assistant
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div ref={conversationRef} id="livekit-room" className="space-y-6">
                    <LiveKitRoom
                      token={connectionDetails.participantToken}
                      serverUrl={connectionDetails.serverUrl}
                      connect={true}
                      audio={true}
                      video={false}
                      onMediaDeviceFailure={(error) => {
                        if (error) {
                          console.error(error);
                          toast.error("Error acquiring microphone permissions.");
                        }
                      }}
                      onDisconnected={handleDisconnectAssistant}
                      className="lk-room-container space-y-4"
                    >
                      <div className="bg-muted/50 rounded-lg p-6">
                        <SimpleVoiceAssistant
                          onStateChange={handleAgentStateChange}
                        />
                      </div>
                      <ControlBar
                        onConnectButtonClicked={() => {}}
                        agentState={agentState}
                      />
                      <RoomAudioRenderer />
                      <NoAgentNotification state={agentState} />
                    </LiveKitRoom>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-12 text-center">
                  <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mb-4">
                    <svg className="w-8 h-8 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold mb-2">No Active Conversation</h3>
                  <p className="text-muted-foreground max-w-md">
                    Connect to an assistant from the Assistants tab to start a voice conversation.
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default VoiceAssistantPage;
