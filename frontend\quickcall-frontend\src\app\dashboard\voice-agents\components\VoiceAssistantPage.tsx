"use client";

import { useState, useRef } from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Room<PERSON>udio<PERSON><PERSON><PERSON>, AgentState } from "@livekit/components-react";
import { useTheme } from "next-themes";
import { toast } from "sonner";

// Components
import SimpleVoiceAssistant from "@/components/voice/SimpleVoiceAssistant";
import ControlBar from "@/components/voice/ControlBar";
import AssistantForm from "@/components/voice/AssistantForm";
import AssistantList from "@/components/voice/AssistantList";
import { NoAgentNotification } from "@/components/voice/NoAgentNotification";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";

// Hooks
import { useVoiceAssistants } from "@/hooks/useVoiceAssistants";

const VoiceAssistantPage: React.FC = () => {
  const { theme } = useTheme();
  const darkMode = theme === "dark";
  
  // State
  const [agentState, setAgentState] = useState<AgentState>("disconnected");
  const [activeTab, setActiveTab] = useState("assistants");
  
  // Refs
  const conversationRef = useRef<HTMLDivElement>(null);
  
  // Custom hook
  const {
    assistants,
    voices,
    models,
    selectedAssistant,
    connectionDetails,
    connectingAssistantId,
    loading,
    error,
    assistantName,
    systemPrompt,
    selectedVoice,
    selectedModel,
    setAssistantName,
    setSystemPrompt,
    setSelectedVoice,
    setSelectedModel,
    createAssistant,
    connectAssistant,
    disconnectAssistant,
    updateAssistant,
    deleteAssistant,
    setError,
  } = useVoiceAssistants();

  // Handlers
  const handleAgentStateChange = (state: AgentState) => {
    setAgentState(state);
  };

  const handleConnectAssistant = async (assistant: any) => {
    try {
      await connectAssistant(assistant);
      toast.success(`Connected to ${assistant.name}`);
    } catch (err) {
      toast.error("Failed to connect to assistant");
    }
  };

  const handleDisconnectAssistant = () => {
    disconnectAssistant();
    toast.info("Disconnected from assistant");
  };

  const handleCreateAssistant = async () => {
    try {
      await createAssistant();
      toast.success("Assistant created successfully");
    } catch (err) {
      toast.error("Failed to create assistant");
    }
  };

  const handleEditAssistant = (assistant: any) => {
    // TODO: Implement edit modal
    toast.info("Edit functionality coming soon");
  };

  const handleDeleteAssistant = async (id: string) => {
    if (confirm("Are you sure you want to delete this assistant?")) {
      try {
        await deleteAssistant(id);
        toast.success("Assistant deleted successfully");
      } catch (err) {
        toast.error("Failed to delete assistant");
      }
    }
  };

  return (
    <div className={`min-h-screen p-6 ${darkMode ? 'bg-gray-900' : 'bg-gray-50'}`}>
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <div>
          <h1 className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            Voice Agents
          </h1>
          <p className={`text-lg ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
            Create and manage AI voice assistants for your calls
          </p>
        </div>

        {/* Error Alert */}
        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Main Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="assistants">Assistants</TabsTrigger>
            <TabsTrigger value="conversation">Live Conversation</TabsTrigger>
          </TabsList>

          <TabsContent value="assistants" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Create Assistant Form */}
              <div>
                <AssistantForm
                  assistantName={assistantName}
                  systemPrompt={systemPrompt}
                  selectedVoice={selectedVoice}
                  selectedModel={selectedModel}
                  voices={voices}
                  models={models}
                  loading={loading}
                  onNameChange={setAssistantName}
                  onPromptChange={setSystemPrompt}
                  onVoiceChange={setSelectedVoice}
                  onModelChange={setSelectedModel}
                  onSubmit={handleCreateAssistant}
                  darkMode={darkMode}
                />
              </div>

              {/* Assistant List */}
              <div>
                <AssistantList
                  assistants={assistants}
                  selectedAssistant={selectedAssistant}
                  connectionDetails={connectionDetails}
                  connectingAssistantId={connectingAssistantId}
                  onConnect={handleConnectAssistant}
                  onEdit={handleEditAssistant}
                  onDelete={handleDeleteAssistant}
                  darkMode={darkMode}
                />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="conversation" className="space-y-6">
            {connectionDetails ? (
              <Card className={darkMode ? 'bg-gray-800 border-gray-700' : ''}>
                <CardHeader>
                  <CardTitle className={darkMode ? 'text-white' : ''}>
                    Live Conversation with {selectedAssistant?.name}
                  </CardTitle>
                  <CardDescription className={darkMode ? 'text-gray-300' : ''}>
                    You are now connected to your voice assistant
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div ref={conversationRef} id="livekit-room">
                    <LiveKitRoom
                      token={connectionDetails.participantToken}
                      serverUrl={connectionDetails.serverUrl}
                      connect={true}
                      audio={true}
                      video={false}
                      onMediaDeviceFailure={(error) => {
                        if (error) {
                          console.error(error);
                          toast.error("Error acquiring microphone permissions.");
                        }
                      }}
                      onDisconnected={handleDisconnectAssistant}
                      className="space-y-4"
                    >
                      <SimpleVoiceAssistant
                        onStateChange={handleAgentStateChange}
                        darkMode={darkMode}
                      />
                      <ControlBar
                        onConnectButtonClicked={() => {}}
                        agentState={agentState}
                        darkMode={darkMode}
                      />
                      <RoomAudioRenderer />
                      <NoAgentNotification state={agentState} />
                    </LiveKitRoom>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <Card className={darkMode ? 'bg-gray-800 border-gray-700' : ''}>
                <CardContent className="p-6 text-center">
                  <p className={darkMode ? 'text-gray-300' : 'text-gray-600'}>
                    Connect to an assistant from the Assistants tab to start a conversation.
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default VoiceAssistantPage;
