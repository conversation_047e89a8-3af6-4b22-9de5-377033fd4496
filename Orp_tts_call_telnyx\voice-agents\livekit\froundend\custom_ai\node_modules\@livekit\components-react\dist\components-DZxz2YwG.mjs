import * as e from "react";
import { useState as x, useRef as P, useEffect as T, use<PERSON><PERSON><PERSON> as he } from "react";
import { u as ge, a as K, b as ve, c as Ee, d as pe, e as we, f as ee, g as ke, h as Me, i as Re, j as ye, k as be, l as te, m as Se, n as Ce, o as Ie, p as Pe, q as Te, r as xe, s as ne, t as Ae, v as Ne } from "./hooks-JwdUfgCD.mjs";
import { a as R, c as O } from "./room-BBI9Ul2f.mjs";
import { RoomEvent as Le, Track as M, ConnectionQuality as V, RemoteTrackPublication as H, RemoteAudioTrack as ze, ConnectionState as F } from "livekit-client";
import { a9 as _, aa as ae, w as Ve, f as re, c as Fe, ab as Ze, X as He, Y as Be, a1 as z, l as A, ac as L, ad as je, i as N, B as Oe, ae as ce, af as W, j as _e, ag as We, m as $e, p as le, ah as qe, ai as De, aj as Ge, ak as Qe, al as Ue } from "./contexts-B7YgC7ji.mjs";
const Kt = /* @__PURE__ */ e.forwardRef(
  function(a, n) {
    const { buttonProps: r } = ge(a);
    return /* @__PURE__ */ e.createElement("button", { ref: n, ...r }, a.children);
  }
), en = /* @__PURE__ */ e.forwardRef(
  function({ room: a, ...n }, r) {
    const c = K(a);
    return /* @__PURE__ */ e.createElement("div", { ref: r, ...n }, c);
  }
), tn = /* @__PURE__ */ e.forwardRef(
  function(a, n) {
    const { mergedProps: r } = ve({ props: a });
    return /* @__PURE__ */ e.createElement("button", { ref: n, ...r }, a.children);
  }
), nn = /* @__PURE__ */ e.forwardRef(
  function(a, n) {
    const { buttonProps: r } = Ee(a);
    return /* @__PURE__ */ e.createElement("button", { ref: n, ...r }, a.children);
  }
), Xe = (t) => /* @__PURE__ */ e.createElement("svg", { xmlns: "http://www.w3.org/2000/svg", width: 16, height: 16, fill: "currentColor", ...t }, /* @__PURE__ */ e.createElement("path", { d: "M1.354.646a.5.5 0 1 0-.708.708l14 14a.5.5 0 0 0 .708-.708L11 10.293V4.5A1.5 1.5 0 0 0 9.5 3H3.707zM0 4.5a1.5 1.5 0 0 1 .943-1.393l9.532 9.533c-.262.224-.603.36-.975.36h-8A1.5 1.5 0 0 1 0 11.5z" }), /* @__PURE__ */ e.createElement("path", { d: "m15.2 3.6-2.8 2.1a1 1 0 0 0-.4.8v3a1 1 0 0 0 .4.8l2.8 2.1a.5.5 0 0 0 .8-.4V4a.5.5 0 0 0-.8-.4z" })), Ye = (t) => /* @__PURE__ */ e.createElement("svg", { xmlns: "http://www.w3.org/2000/svg", width: 16, height: 16, fill: "currentColor", ...t }, /* @__PURE__ */ e.createElement("path", { d: "M0 4.5A1.5 1.5 0 0 1 1.5 3h8A1.5 1.5 0 0 1 11 4.5v7A1.5 1.5 0 0 1 9.5 13h-8A1.5 1.5 0 0 1 0 11.5zM15.2 3.6l-2.8 2.1a1 1 0 0 0-.4.8v3a1 1 0 0 0 .4.8l2.8 2.1a.5.5 0 0 0 .8-.4V4a.5.5 0 0 0-.8-.4z" })), an = (t) => /* @__PURE__ */ e.createElement("svg", { xmlns: "http://www.w3.org/2000/svg", width: 16, height: 16, viewBox: "0 0 24 24", ...t }, /* @__PURE__ */ e.createElement(
  "path",
  {
    fill: "#FFF",
    d: "M4.99 3.99a1 1 0 0 0-.697 1.717L10.586 12l-6.293 6.293a1 1 0 1 0 1.414 1.414L12 13.414l6.293 6.293a1 1 0 1 0 1.414-1.414L13.414 12l6.293-6.293a1 1 0 0 0-.727-1.717 1 1 0 0 0-.687.303L12 10.586 5.707 4.293a1 1 0 0 0-.717-.303z"
  }
)), rn = (t) => /* @__PURE__ */ e.createElement("svg", { xmlns: "http://www.w3.org/2000/svg", width: 16, height: 18, fill: "none", ...t }, /* @__PURE__ */ e.createElement(
  "path",
  {
    fill: "currentColor",
    fillRule: "evenodd",
    d: "M0 2.75A2.75 2.75 0 0 1 2.75 0h10.5A2.75 2.75 0 0 1 16 2.75v13.594a.75.75 0 0 1-1.234.572l-3.691-3.12a1.25 1.25 0 0 0-.807-.296H2.75A2.75 2.75 0 0 1 0 10.75v-8ZM2.75 1.5c-.69 0-1.25.56-1.25 1.25v8c0 .69.56 1.25 1.25 1.25h7.518c.65 0 1.279.23 1.775.65l2.457 2.077V2.75c0-.69-.56-1.25-1.25-1.25H2.75Z",
    clipRule: "evenodd"
  }
), /* @__PURE__ */ e.createElement(
  "path",
  {
    fill: "currentColor",
    fillRule: "evenodd",
    d: "M3 4.5a.5.5 0 0 1 .5-.5h9a.5.5 0 0 1 0 1h-9a.5.5 0 0 1-.5-.5Zm0 2a.5.5 0 0 1 .5-.5h9a.5.5 0 0 1 0 1h-9a.5.5 0 0 1-.5-.5Zm0 2a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5Z",
    clipRule: "evenodd"
  }
)), D = (t) => /* @__PURE__ */ e.createElement("svg", { xmlns: "http://www.w3.org/2000/svg", width: 16, height: 16, fill: "none", ...t }, /* @__PURE__ */ e.createElement(
  "path",
  {
    fill: "currentcolor",
    fillRule: "evenodd",
    d: "M5.293 2.293a1 1 0 0 1 1.414 0l4.823 4.823a1.25 1.25 0 0 1 0 1.768l-4.823 4.823a1 1 0 0 1-1.414-1.414L9.586 8 5.293 3.707a1 1 0 0 1 0-1.414z",
    clipRule: "evenodd"
  }
)), Je = (t) => /* @__PURE__ */ e.createElement("svg", { xmlns: "http://www.w3.org/2000/svg", width: 16, height: 16, fill: "none", ...t }, /* @__PURE__ */ e.createElement("g", { stroke: "currentColor", strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 1.5 }, /* @__PURE__ */ e.createElement("path", { d: "M10 1.75h4.25m0 0V6m0-4.25L9 7M6 14.25H1.75m0 0V10m0 4.25L7 9" }))), cn = (t) => /* @__PURE__ */ e.createElement("svg", { xmlns: "http://www.w3.org/2000/svg", width: 16, height: 16, fill: "none", ...t }, /* @__PURE__ */ e.createElement(
  "path",
  {
    fill: "currentcolor",
    fillRule: "evenodd",
    d: "M8.961.894C8.875-.298 7.125-.298 7.04.894c-.066.912-1.246 1.228-1.76.472-.67-.99-2.186-.115-1.664.96.399.824-.465 1.688-1.288 1.289-1.076-.522-1.95.994-.961 1.665.756.513.44 1.693-.472 1.759-1.192.086-1.192 1.836 0 1.922.912.066 1.228 1.246.472 1.76-.99.67-.115 2.186.96 1.664.824-.399 1.688.465 1.289 1.288-.522 1.076.994 1.95 1.665.961.513-.756 1.693-.44 1.759.472.086 1.192 1.836 1.192 1.922 0 .066-.912 1.246-1.228 1.76-.472.67.99 2.186.115 1.664-.96-.399-.824.465-1.688 1.288-1.289 1.076.522 1.95-.994.961-1.665-.756-.513-.44-1.693.472-1.759 1.192-.086 1.192-1.836 0-1.922-.912-.066-1.228-1.246-.472-1.76.99-.67.115-2.186-.96-1.664-.824.399-1.688-.465-1.289-1.288.522-1.076-.994-1.95-1.665-.961-.513.756-1.693.44-1.759-.472ZM8 13A5 5 0 1 0 8 3a5 5 0 0 0 0 10Z",
    clipRule: "evenodd"
  }
)), ln = (t) => /* @__PURE__ */ e.createElement("svg", { xmlns: "http://www.w3.org/2000/svg", width: 16, height: 16, fill: "none", ...t }, /* @__PURE__ */ e.createElement(
  "path",
  {
    fill: "currentColor",
    fillRule: "evenodd",
    d: "M2 2.75A2.75 2.75 0 0 1 4.75 0h6.5A2.75 2.75 0 0 1 14 2.75v10.5A2.75 2.75 0 0 1 11.25 16h-6.5A2.75 2.75 0 0 1 2 13.25v-.5a.75.75 0 0 1 1.5 0v.5c0 .69.56 1.25 1.25 1.25h6.5c.69 0 1.25-.56 1.25-1.25V2.75c0-.69-.56-1.25-1.25-1.25h-6.5c-.69 0-1.25.56-1.25 1.25v.5a.75.75 0 0 1-1.5 0v-.5Z",
    clipRule: "evenodd"
  }
), /* @__PURE__ */ e.createElement(
  "path",
  {
    fill: "currentColor",
    fillRule: "evenodd",
    d: "M8.78 7.47a.75.75 0 0 1 0 1.06l-2.25 2.25a.75.75 0 1 1-1.06-1.06l.97-.97H1.75a.75.75 0 0 1 0-1.5h4.69l-.97-.97a.75.75 0 0 1 1.06-1.06l2.25 2.25Z",
    clipRule: "evenodd"
  }
)), Ke = (t) => /* @__PURE__ */ e.createElement("svg", { xmlns: "http://www.w3.org/2000/svg", width: 16, height: 16, fill: "none", ...t }, /* @__PURE__ */ e.createElement(
  "path",
  {
    fill: "currentcolor",
    fillRule: "evenodd",
    d: "M4 6.104V4a4 4 0 1 1 8 0v2.104c1.154.326 2 1.387 2 2.646v4.5A2.75 2.75 0 0 1 11.25 16h-6.5A2.75 2.75 0 0 1 2 13.25v-4.5c0-1.259.846-2.32 2-2.646ZM5.5 4a2.5 2.5 0 0 1 5 0v2h-5V4Z",
    clipRule: "evenodd"
  }
)), et = (t) => /* @__PURE__ */ e.createElement("svg", { xmlns: "http://www.w3.org/2000/svg", width: 16, height: 16, fill: "currentColor", ...t }, /* @__PURE__ */ e.createElement("path", { d: "M12.227 11.52a5.477 5.477 0 0 0 1.246-******** 0 0 0-.995-.1 4.478 4.478 0 0 1-.962 2.359l-1.07-1.07C10.794 9.247 11 8.647 11 8V3a3 3 0 0 0-6 0v1.293L1.354.646a.5.5 0 1 0-.708.708l14 14a.5.5 0 0 0 .708-.708zM8 12.5c.683 0 1.33-.152 1.911-.425l.743.743c-.649.359-1.378.59-2.154.66V15h2a.5.5 0 0 1 0 1h-5a.5.5 0 0 1 0-1h2v-1.522a5.502 5.502 0 0 1-4.973-4.929.5.5 0 0 1 .995-.098A4.5 4.5 0 0 0 8 12.5z" }), /* @__PURE__ */ e.createElement("path", { d: "M8.743 10.907 5 7.164V8a3 3 0 0 0 3.743 2.907z" })), tt = (t) => /* @__PURE__ */ e.createElement("svg", { xmlns: "http://www.w3.org/2000/svg", width: 16, height: 16, fill: "currentColor", ...t }, /* @__PURE__ */ e.createElement(
  "path",
  {
    fillRule: "evenodd",
    d: "M2.975 8.002a.5.5 0 0 1 .547.449 4.5 4.5 0 0 0 8.956 0 .5.5 0 1 1 .995.098A5.502 5.502 0 0 1 8.5 13.478V15h2a.5.5 0 0 1 0 1h-5a.5.5 0 0 1 0-1h2v-1.522a5.502 5.502 0 0 1-4.973-4.929.5.5 0 0 1 .448-.547z",
    clipRule: "evenodd"
  }
), /* @__PURE__ */ e.createElement("path", { d: "M5 3a3 3 0 1 1 6 0v5a3 3 0 0 1-6 0z" })), nt = (t) => /* @__PURE__ */ e.createElement("svg", { xmlns: "http://www.w3.org/2000/svg", width: 16, height: 16, fill: "currentcolor", ...t }, /* @__PURE__ */ e.createElement("path", { d: "M0 11.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v4a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5zm6-5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v9a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5zm6-6a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v15a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5z" }), /* @__PURE__ */ e.createElement("path", { d: "M0 11.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v4a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5zm6-5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v9a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5zm6-6a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v15a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5z" })), at = (t) => /* @__PURE__ */ e.createElement("svg", { xmlns: "http://www.w3.org/2000/svg", width: 16, height: 16, fill: "currentcolor", ...t }, /* @__PURE__ */ e.createElement("path", { d: "M0 11.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v4a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5zm6-5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v9a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5z" }), /* @__PURE__ */ e.createElement("path", { d: "M0 11.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v4a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5zm6-5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v9a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5z" }), /* @__PURE__ */ e.createElement("g", { opacity: 0.25 }, /* @__PURE__ */ e.createElement("path", { d: "M12 .5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v15a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5z" }), /* @__PURE__ */ e.createElement("path", { d: "M12 .5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v15a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5z" }))), rt = (t) => /* @__PURE__ */ e.createElement("svg", { xmlns: "http://www.w3.org/2000/svg", width: 16, height: 16, fill: "currentcolor", ...t }, /* @__PURE__ */ e.createElement("path", { d: "M0 11.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v4a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5z" }), /* @__PURE__ */ e.createElement("path", { d: "M0 11.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v4a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5z" }), /* @__PURE__ */ e.createElement("g", { opacity: 0.25 }, /* @__PURE__ */ e.createElement("path", { d: "M6 6.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v9a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5z" }), /* @__PURE__ */ e.createElement("path", { d: "M6 6.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v9a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5zm6-6a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v15a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5z" }), /* @__PURE__ */ e.createElement("path", { d: "M12 .5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v15a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5z" }))), ct = (t) => /* @__PURE__ */ e.createElement("svg", { xmlns: "http://www.w3.org/2000/svg", width: 16, height: 16, fill: "currentColor", ...t }, /* @__PURE__ */ e.createElement("g", { opacity: 0.25 }, /* @__PURE__ */ e.createElement("path", { d: "M0 11.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v4a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-4Zm6-5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v9a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-9Zm6-6a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v15a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5V.5Z" }), /* @__PURE__ */ e.createElement("path", { d: "M0 11.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v4a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-4Zm6-5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v9a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-9Zm6-6a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v15a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5V.5Z" }))), ie = (t) => /* @__PURE__ */ e.createElement("svg", { xmlns: "http://www.w3.org/2000/svg", width: 20, height: 16, fill: "none", ...t }, /* @__PURE__ */ e.createElement(
  "path",
  {
    fill: "currentColor",
    fillRule: "evenodd",
    d: "M0 2.75A2.75 2.75 0 0 1 2.75 0h14.5A2.75 2.75 0 0 1 20 2.75v10.5A2.75 2.75 0 0 1 17.25 16H2.75A2.75 2.75 0 0 1 0 13.25V2.75ZM2.75 1.5c-.69 0-1.25.56-1.25 1.25v10.5c0 .69.56 1.25 1.25 1.25h14.5c.69 0 1.25-.56 1.25-1.25V2.75c0-.69-.56-1.25-1.25-1.25H2.75Z",
    clipRule: "evenodd"
  }
), /* @__PURE__ */ e.createElement(
  "path",
  {
    fill: "currentColor",
    fillRule: "evenodd",
    d: "M9.47 4.22a.75.75 0 0 1 1.06 0l2.25 2.25a.75.75 0 0 1-1.06 1.06l-.97-.97v4.69a.75.75 0 0 1-1.5 0V6.56l-.97.97a.75.75 0 0 1-1.06-1.06l2.25-2.25Z",
    clipRule: "evenodd"
  }
)), lt = (t) => /* @__PURE__ */ e.createElement("svg", { xmlns: "http://www.w3.org/2000/svg", width: 20, height: 16, fill: "none", ...t }, /* @__PURE__ */ e.createElement("g", { fill: "currentColor" }, /* @__PURE__ */ e.createElement("path", { d: "M7.28 4.22a.75.75 0 0 0-1.06 1.06L8.94 8l-2.72 2.72a.75.75 0 1 0 1.06 1.06L10 9.06l2.72 2.72a.75.75 0 1 0 1.06-1.06L11.06 8l2.72-2.72a.75.75 0 0 0-1.06-1.06L10 6.94z" }), /* @__PURE__ */ e.createElement(
  "path",
  {
    fillRule: "evenodd",
    d: "M2.75 0A2.75 2.75 0 0 0 0 2.75v10.5A2.75 2.75 0 0 0 2.75 16h14.5A2.75 2.75 0 0 0 20 13.25V2.75A2.75 2.75 0 0 0 17.25 0zM1.5 2.75c0-.69.56-1.25 1.25-1.25h14.5c.69 0 1.25.56 1.25 1.25v10.5c0 .69-.56 1.25-1.25 1.25H2.75c-.69 0-1.25-.56-1.25-1.25z",
    clipRule: "evenodd"
  }
))), G = (t) => /* @__PURE__ */ e.createElement("svg", { xmlns: "http://www.w3.org/2000/svg", width: 16, height: 16, fill: "none", ...t }, /* @__PURE__ */ e.createElement(
  "path",
  {
    fill: "currentColor",
    fillRule: "evenodd",
    d: "M8 0a.75.75 0 0 1 .75.75v2.5a.75.75 0 0 1-1.5 0V.75A.75.75 0 0 1 8 0Z",
    clipRule: "evenodd"
  }
), /* @__PURE__ */ e.createElement(
  "path",
  {
    fill: "currentColor",
    fillRule: "evenodd",
    d: "M8 12a.75.75 0 0 1 .75.75v2.5a.75.75 0 0 1-1.5 0v-2.5A.75.75 0 0 1 8 12Z",
    clipRule: "evenodd",
    opacity: 0.7
  }
), /* @__PURE__ */ e.createElement(
  "path",
  {
    fill: "currentColor",
    fillRule: "evenodd",
    d: "M12 1.072a.75.75 0 0 1 .274 1.024l-1.25 2.165a.75.75 0 0 1-1.299-.75l1.25-2.165A.75.75 0 0 1 12 1.072Z",
    clipRule: "evenodd"
  }
), /* @__PURE__ */ e.createElement(
  "path",
  {
    fill: "currentColor",
    fillRule: "evenodd",
    d: "M6 11.464a.75.75 0 0 1 .274 1.025l-1.25 2.165a.75.75 0 0 1-1.299-.75l1.25-2.165A.75.75 0 0 1 6 11.464Z",
    clipRule: "evenodd",
    opacity: 0.6
  }
), /* @__PURE__ */ e.createElement(
  "path",
  {
    fill: "currentColor",
    fillRule: "evenodd",
    d: "M14.928 4a.75.75 0 0 1-.274 1.025l-2.165 1.25a.75.75 0 1 1-.75-1.3l2.165-1.25A.75.75 0 0 1 14.928 4Z",
    clipRule: "evenodd"
  }
), /* @__PURE__ */ e.createElement(
  "path",
  {
    fill: "currentColor",
    fillRule: "evenodd",
    d: "M4.536 10a.75.75 0 0 1-.275 1.024l-2.165 1.25a.75.75 0 0 1-.75-1.298l2.165-1.25A.75.75 0 0 1 4.536 10Z",
    clipRule: "evenodd",
    opacity: 0.5
  }
), /* @__PURE__ */ e.createElement(
  "path",
  {
    fill: "currentColor",
    fillRule: "evenodd",
    d: "M16 8a.75.75 0 0 1-.75.75h-2.5a.75.75 0 0 1 0-1.5h2.5A.75.75 0 0 1 16 8Z",
    clipRule: "evenodd"
  }
), /* @__PURE__ */ e.createElement(
  "path",
  {
    fill: "currentColor",
    fillRule: "evenodd",
    d: "M4 8a.75.75 0 0 1-.75.75H.75a.75.75 0 0 1 0-1.5h2.5A.75.75 0 0 1 4 8Z",
    clipRule: "evenodd",
    opacity: 0.4
  }
), /* @__PURE__ */ e.createElement(
  "path",
  {
    fill: "currentColor",
    fillRule: "evenodd",
    d: "M14.928 12a.75.75 0 0 1-1.024.274l-2.165-1.25a.75.75 0 0 1 .75-1.299l2.165 1.25A.75.75 0 0 1 14.928 12Z",
    clipRule: "evenodd",
    opacity: 0.9
  }
), /* @__PURE__ */ e.createElement(
  "path",
  {
    fill: "currentColor",
    fillRule: "evenodd",
    d: "M4.536 6a.75.75 0 0 1-1.025.275l-2.165-1.25a.75.75 0 1 1 .75-1.3l2.165 1.25A.75.75 0 0 1 4.536 6Z",
    clipRule: "evenodd",
    opacity: 0.3
  }
), /* @__PURE__ */ e.createElement(
  "path",
  {
    fill: "currentColor",
    fillRule: "evenodd",
    d: "M12 14.928a.75.75 0 0 1-1.024-.274l-1.25-2.165a.75.75 0 0 1 1.298-.75l1.25 2.165A.75.75 0 0 1 12 14.928Z",
    clipRule: "evenodd",
    opacity: 0.8
  }
), /* @__PURE__ */ e.createElement(
  "path",
  {
    fill: "currentColor",
    fillRule: "evenodd",
    d: "M6 4.536a.75.75 0 0 1-1.024-.275l-1.25-2.165a.75.75 0 1 1 1.299-.75l1.25 2.165A.75.75 0 0 1 6 4.536Z",
    clipRule: "evenodd",
    opacity: 0.2
  }
)), it = (t) => /* @__PURE__ */ e.createElement("svg", { xmlns: "http://www.w3.org/2000/svg", width: 16, height: 16, fill: "none", ...t }, /* @__PURE__ */ e.createElement("g", { stroke: "currentColor", strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 1.5 }, /* @__PURE__ */ e.createElement("path", { d: "M13.25 7H9m0 0V2.75M9 7l5.25-5.25M2.75 9H7m0 0v4.25M7 9l-5.25 5.25" }))), st = /* @__PURE__ */ e.forwardRef(
  function({ trackRef: a, ...n }, r) {
    const c = _(), { mergedProps: s, inFocus: o } = pe({
      trackRef: a ?? c,
      props: n
    });
    return /* @__PURE__ */ e.createElement(ae.Consumer, null, (l) => l !== void 0 && /* @__PURE__ */ e.createElement("button", { ref: r, ...s }, n.children ? n.children : o ? /* @__PURE__ */ e.createElement(it, null) : /* @__PURE__ */ e.createElement(Je, null)));
  }
), sn = /* @__PURE__ */ e.forwardRef(
  function({
    kind: a,
    initialSelection: n,
    onActiveDeviceChange: r,
    onDeviceListChange: c,
    onDeviceSelectError: s,
    exactMatch: o,
    track: l,
    requestPermissions: i,
    onError: u,
    ...d
  }, f) {
    const m = Ve(), E = e.useCallback(
      (g) => {
        m && m.emit(Le.MediaDevicesError, g), u == null || u(g);
      },
      [m, u]
    ), { devices: p, activeDeviceId: h, setActiveMediaDevice: v, className: k } = we({
      kind: a,
      room: m,
      track: l,
      requestPermissions: i,
      onError: E
    });
    e.useEffect(() => {
      n !== void 0 && v(n);
    }, [v]), e.useEffect(() => {
      typeof c == "function" && c(p);
    }, [c, p]), e.useEffect(() => {
      h && h !== "" && (r == null || r(h));
    }, [h]);
    const I = async (g) => {
      try {
        await v(g, { exact: o });
      } catch (w) {
        if (w instanceof Error)
          s == null || s(w);
        else
          throw w;
      }
    }, y = e.useMemo(
      () => R(d, { className: k }, { className: "lk-list" }),
      [k, d]
    ), S = !!p.find((g) => g.label.toLowerCase().startsWith("default"));
    function b(g, w, C) {
      return g === w || !S && C === 0 && w === "default";
    }
    return /* @__PURE__ */ e.createElement("ul", { ref: f, ...y }, p.map((g, w) => /* @__PURE__ */ e.createElement(
      "li",
      {
        key: g.deviceId,
        id: g.deviceId,
        "data-lk-active": b(g.deviceId, h, w),
        "aria-selected": b(g.deviceId, h, w),
        role: "option"
      },
      /* @__PURE__ */ e.createElement("button", { className: "lk-button", onClick: () => I(g.deviceId) }, g.label)
    )));
  }
), on = /* @__PURE__ */ e.forwardRef(
  function({ label: a = "Allow Audio", ...n }, r) {
    const c = re(), { mergedProps: s } = ee({ room: c, props: n });
    return /* @__PURE__ */ e.createElement("button", { ref: r, ...s }, a);
  }
), un = /* @__PURE__ */ e.forwardRef(
  function({ label: a, ...n }, r) {
    const c = re(), { mergedProps: s, canPlayAudio: o } = ee({ room: c, props: n }), { mergedProps: l, canPlayVideo: i } = ke({ room: c, props: s }), { style: u, ...d } = l;
    return u.display = o && i ? "none" : "block", /* @__PURE__ */ e.createElement("button", { ref: r, style: u, ...d }, a ?? `Start ${o ? "Video" : "Audio"}`);
  }
);
function se(t, a) {
  switch (t) {
    case M.Source.Microphone:
      return a ? /* @__PURE__ */ e.createElement(tt, null) : /* @__PURE__ */ e.createElement(et, null);
    case M.Source.Camera:
      return a ? /* @__PURE__ */ e.createElement(Ye, null) : /* @__PURE__ */ e.createElement(Xe, null);
    case M.Source.ScreenShare:
      return a ? /* @__PURE__ */ e.createElement(lt, null) : /* @__PURE__ */ e.createElement(ie, null);
    default:
      return;
  }
}
function ot(t) {
  switch (t) {
    case V.Excellent:
      return /* @__PURE__ */ e.createElement(nt, null);
    case V.Good:
      return /* @__PURE__ */ e.createElement(at, null);
    case V.Poor:
      return /* @__PURE__ */ e.createElement(rt, null);
    default:
      return /* @__PURE__ */ e.createElement(ct, null);
  }
}
const dn = /* @__PURE__ */ e.forwardRef(function({ showIcon: a, ...n }, r) {
  const { buttonProps: c, enabled: s } = Me(n), [o, l] = e.useState(!1);
  return e.useEffect(() => {
    l(!0);
  }, []), o && /* @__PURE__ */ e.createElement("button", { ref: r, ...c }, (a ?? !0) && se(n.source, s), n.children);
}), oe = /* @__PURE__ */ e.forwardRef(function(a, n) {
  const { className: r, quality: c } = Re(a), s = e.useMemo(() => ({ ...R(a, { className: r }), "data-lk-quality": c }), [c, a, r]);
  return /* @__PURE__ */ e.createElement("div", { ref: n, ...s }, a.children ?? ot(c));
}), B = /* @__PURE__ */ e.forwardRef(
  function({ participant: a, ...n }, r) {
    const c = Fe(a), { className: s, infoObserver: o } = e.useMemo(() => Ze(c), [c]), { identity: l, name: i } = ye(o, {
      name: c.name,
      identity: c.identity,
      metadata: c.metadata
    }), u = e.useMemo(() => R(n, { className: s, "data-lk-participant-name": i }), [n, s, i]);
    return /* @__PURE__ */ e.createElement("span", { ref: r, ...u }, i !== "" ? i : l, n.children);
  }
), ue = /* @__PURE__ */ e.forwardRef(
  function({ trackRef: a, show: n = "always", ...r }, c) {
    const { className: s, isMuted: o } = be(a), l = n === "always" || n === "muted" && o || n === "unmuted" && !o, i = e.useMemo(
      () => R(r, {
        className: s
      }),
      [s, r]
    );
    return l ? /* @__PURE__ */ e.createElement("div", { ref: c, ...i, "data-lk-muted": o }, r.children ?? se(a.source, !o)) : null;
  }
), ut = (t) => /* @__PURE__ */ e.createElement(
  "svg",
  {
    width: 320,
    height: 320,
    viewBox: "0 0 320 320",
    preserveAspectRatio: "xMidYMid meet",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    ...t
  },
  /* @__PURE__ */ e.createElement(
    "path",
    {
      d: "M160 180C204.182 180 240 144.183 240 100C240 55.8172 204.182 20 160 20C115.817 20 79.9997 55.8172 79.9997 100C79.9997 144.183 115.817 180 160 180Z",
      fill: "white",
      fillOpacity: 0.25
    }
  ),
  /* @__PURE__ */ e.createElement(
    "path",
    {
      d: "M97.6542 194.614C103.267 191.818 109.841 192.481 115.519 195.141C129.025 201.466 144.1 205 159.999 205C175.899 205 190.973 201.466 204.48 195.141C210.158 192.481 216.732 191.818 222.345 194.614C262.703 214.719 291.985 253.736 298.591 300.062C300.15 310.997 291.045 320 280 320H39.9997C28.954 320 19.8495 310.997 21.4087 300.062C28.014 253.736 57.2966 214.72 97.6542 194.614Z",
      fill: "white",
      fillOpacity: 0.25
    }
  )
);
function de(t, a = {}) {
  const [n, r] = e.useState(He(t)), [c, s] = e.useState(n == null ? void 0 : n.isMuted), [o, l] = e.useState(n == null ? void 0 : n.isSubscribed), [i, u] = e.useState(n == null ? void 0 : n.track), [d, f] = e.useState("landscape"), m = e.useRef(), { className: E, trackObserver: p } = e.useMemo(() => Be(t), [
    t.participant.sid ?? t.participant.identity,
    t.source,
    z(t) && t.publication.trackSid
  ]);
  return e.useEffect(() => {
    const h = p.subscribe((v) => {
      A.debug("update track", v), r(v), s(v == null ? void 0 : v.isMuted), l(v == null ? void 0 : v.isSubscribed), u(v == null ? void 0 : v.track);
    });
    return () => h == null ? void 0 : h.unsubscribe();
  }, [p]), e.useEffect(() => {
    var h, v;
    return i && (m.current && i.detach(m.current), (h = a.element) != null && h.current && !(t.participant.isLocal && (i == null ? void 0 : i.kind) === "audio") && i.attach(a.element.current)), m.current = (v = a.element) == null ? void 0 : v.current, () => {
      m.current && (i == null || i.detach(m.current));
    };
  }, [i, a.element]), e.useEffect(() => {
    var h, v;
    if (typeof ((h = n == null ? void 0 : n.dimensions) == null ? void 0 : h.width) == "number" && typeof ((v = n == null ? void 0 : n.dimensions) == null ? void 0 : v.height) == "number") {
      const k = n.dimensions.width > n.dimensions.height ? "landscape" : "portrait";
      f(k);
    }
  }, [n]), {
    publication: n,
    isMuted: c,
    isSubscribed: o,
    track: i,
    elementProps: R(a.props, {
      className: E,
      "data-lk-local-participant": t.participant.isLocal,
      "data-lk-source": n == null ? void 0 : n.source,
      ...(n == null ? void 0 : n.kind) === "video" && { "data-lk-orientation": d }
    })
  };
}
var dt = "Expected a function", Q = NaN, mt = "[object Symbol]", ft = /^\s+|\s+$/g, ht = /^[-+]0x[0-9a-f]+$/i, gt = /^0b[01]+$/i, vt = /^0o[0-7]+$/i, Et = parseInt, pt = typeof L == "object" && L && L.Object === Object && L, wt = typeof self == "object" && self && self.Object === Object && self, kt = pt || wt || Function("return this")(), Mt = Object.prototype, Rt = Mt.toString, yt = Math.max, bt = Math.min, Z = function() {
  return kt.Date.now();
};
function St(t, a, n) {
  var r, c, s, o, l, i, u = 0, d = !1, f = !1, m = !0;
  if (typeof t != "function")
    throw new TypeError(dt);
  a = U(a) || 0, j(n) && (d = !!n.leading, f = "maxWait" in n, s = f ? yt(U(n.maxWait) || 0, a) : s, m = "trailing" in n ? !!n.trailing : m);
  function E(g) {
    var w = r, C = c;
    return r = c = void 0, u = g, o = t.apply(C, w), o;
  }
  function p(g) {
    return u = g, l = setTimeout(k, a), d ? E(g) : o;
  }
  function h(g) {
    var w = g - i, C = g - u, q = a - w;
    return f ? bt(q, s - C) : q;
  }
  function v(g) {
    var w = g - i, C = g - u;
    return i === void 0 || w >= a || w < 0 || f && C >= s;
  }
  function k() {
    var g = Z();
    if (v(g))
      return I(g);
    l = setTimeout(k, h(g));
  }
  function I(g) {
    return l = void 0, m && r ? E(g) : (r = c = void 0, o);
  }
  function y() {
    l !== void 0 && clearTimeout(l), u = 0, r = i = c = l = void 0;
  }
  function S() {
    return l === void 0 ? o : I(Z());
  }
  function b() {
    var g = Z(), w = v(g);
    if (r = arguments, c = this, i = g, w) {
      if (l === void 0)
        return p(i);
      if (f)
        return l = setTimeout(k, a), E(i);
    }
    return l === void 0 && (l = setTimeout(k, a)), o;
  }
  return b.cancel = y, b.flush = S, b;
}
function j(t) {
  var a = typeof t;
  return !!t && (a == "object" || a == "function");
}
function Ct(t) {
  return !!t && typeof t == "object";
}
function It(t) {
  return typeof t == "symbol" || Ct(t) && Rt.call(t) == mt;
}
function U(t) {
  if (typeof t == "number")
    return t;
  if (It(t))
    return Q;
  if (j(t)) {
    var a = typeof t.valueOf == "function" ? t.valueOf() : t;
    t = j(a) ? a + "" : a;
  }
  if (typeof t != "string")
    return t === 0 ? t : +t;
  t = t.replace(ft, "");
  var n = gt.test(t);
  return n || vt.test(t) ? Et(t.slice(2), n ? 2 : 8) : ht.test(t) ? Q : +t;
}
var Pt = St;
const X = /* @__PURE__ */ je(Pt);
function Tt(t) {
  const a = P(t);
  a.current = t, T(
    () => () => {
      a.current();
    },
    []
  );
}
function xt(t, a = 500, n) {
  const r = P();
  Tt(() => {
    r.current && r.current.cancel();
  });
  const c = he(() => {
    const s = X(t, a, n), o = (...l) => s(...l);
    return o.cancel = () => {
      s.cancel();
    }, o.isPending = () => !!r.current, o.flush = () => s.flush(), o;
  }, [t, a, n]);
  return T(() => {
    r.current = X(t, a, n);
  }, [t, a, n]), c;
}
function At(t, a, n) {
  const r = (u, d) => u === d, c = t instanceof Function ? t() : t, [s, o] = x(c), l = P(c), i = xt(
    o,
    a,
    n
  );
  return r(l.current, c) || (i(c), l.current = c), [s, i];
}
function Nt({
  threshold: t = 0,
  root: a = null,
  rootMargin: n = "0%",
  freezeOnceVisible: r = !1,
  initialIsIntersecting: c = !1,
  onChange: s
} = {}) {
  var o;
  const [l, i] = x(null), [u, d] = x(() => ({
    isIntersecting: c,
    entry: void 0
  })), f = P();
  f.current = s;
  const m = ((o = u.entry) == null ? void 0 : o.isIntersecting) && r;
  T(() => {
    if (!l || !("IntersectionObserver" in window) || m)
      return;
    let h;
    const v = new IntersectionObserver(
      (k) => {
        const I = Array.isArray(v.thresholds) ? v.thresholds : [v.thresholds];
        k.forEach((y) => {
          const S = y.isIntersecting && I.some((b) => y.intersectionRatio >= b);
          d({ isIntersecting: S, entry: y }), f.current && f.current(S, y), S && r && h && (h(), h = void 0);
        });
      },
      { threshold: t, root: a, rootMargin: n }
    );
    return v.observe(l), () => {
      v.disconnect();
    };
  }, [
    l,
    // eslint-disable-next-line react-hooks/exhaustive-deps
    JSON.stringify(t),
    a,
    n,
    m,
    r
  ]);
  const E = P(null);
  T(() => {
    var h;
    !l && ((h = u.entry) != null && h.target) && !r && !m && E.current !== u.entry.target && (E.current = u.entry.target, d({ isIntersecting: c, entry: void 0 }));
  }, [l, u.entry, r, m, c]);
  const p = [
    i,
    !!u.isIntersecting,
    u.entry
  ];
  return p.ref = p[0], p.isIntersecting = p[1], p.entry = p[2], p;
}
const Lt = /* @__PURE__ */ e.forwardRef(
  function({
    onTrackClick: a,
    onClick: n,
    onSubscriptionStatusChanged: r,
    trackRef: c,
    manageSubscription: s,
    ...o
  }, l) {
    const i = N(c), u = e.useRef(null);
    e.useImperativeHandle(l, () => u.current);
    const d = Nt({ root: u.current }), [f] = At(d, 3e3);
    e.useEffect(() => {
      s && i.publication instanceof H && (f == null ? void 0 : f.isIntersecting) === !1 && (d == null ? void 0 : d.isIntersecting) === !1 && i.publication.setSubscribed(!1);
    }, [f, i, s]), e.useEffect(() => {
      s && i.publication instanceof H && (d == null ? void 0 : d.isIntersecting) === !0 && i.publication.setSubscribed(!0);
    }, [d, i, s]);
    const {
      elementProps: m,
      publication: E,
      isSubscribed: p
    } = de(i, {
      element: u,
      props: o
    });
    e.useEffect(() => {
      r == null || r(!!p);
    }, [p, r]);
    const h = (v) => {
      n == null || n(v), a == null || a({ participant: i == null ? void 0 : i.participant, track: E });
    };
    return /* @__PURE__ */ e.createElement("video", { ref: u, ...m, muted: !0, onClick: h });
  }
), $ = /* @__PURE__ */ e.forwardRef(
  function({ trackRef: a, onSubscriptionStatusChanged: n, volume: r, ...c }, s) {
    const o = N(a), l = e.useRef(null);
    e.useImperativeHandle(s, () => l.current);
    const {
      elementProps: i,
      isSubscribed: u,
      track: d,
      publication: f
    } = de(o, {
      element: l,
      props: c
    });
    return e.useEffect(() => {
      n == null || n(!!u);
    }, [u, n]), e.useEffect(() => {
      d === void 0 || r === void 0 || (d instanceof ze ? d.setVolume(r) : A.warn("Volume can only be set on remote audio tracks."));
    }, [r, d]), e.useEffect(() => {
      f === void 0 || c.muted === void 0 || (f instanceof H ? f.setEnabled(!c.muted) : A.warn("Can only call setEnabled on remote track publications."));
    }, [c.muted, f, d]), /* @__PURE__ */ e.createElement("audio", { ref: l, ...i });
  }
);
function zt(t) {
  const a = !!Oe();
  return t.participant && !a ? /* @__PURE__ */ e.createElement(ce.Provider, { value: t.participant }, t.children) : /* @__PURE__ */ e.createElement(e.Fragment, null, t.children);
}
function Vt(t) {
  const a = !!_();
  return t.trackRef && !a ? /* @__PURE__ */ e.createElement(W.Provider, { value: t.trackRef }, t.children) : /* @__PURE__ */ e.createElement(e.Fragment, null, t.children);
}
const Ft = /* @__PURE__ */ e.forwardRef(
  function({
    trackRef: a,
    children: n,
    onParticipantClick: r,
    disableSpeakingIndicator: c,
    ...s
  }, o) {
    var E, p;
    const l = N(a), { elementProps: i } = te({
      htmlProps: s,
      disableSpeakingIndicator: c,
      onParticipantClick: r,
      trackRef: l
    }), u = Se(l.participant), d = _e(), f = (E = We()) == null ? void 0 : E.autoSubscription, m = e.useCallback(
      (h) => {
        l.source && !h && d && d.pin.dispatch && $e(l, d.pin.state) && d.pin.dispatch({ msg: "clear_pin" });
      },
      [l, d]
    );
    return /* @__PURE__ */ e.createElement("div", { ref: o, style: { position: "relative" }, ...i }, /* @__PURE__ */ e.createElement(Vt, { trackRef: l }, /* @__PURE__ */ e.createElement(zt, { participant: l.participant }, n ?? /* @__PURE__ */ e.createElement(e.Fragment, null, z(l) && (((p = l.publication) == null ? void 0 : p.kind) === "video" || l.source === M.Source.Camera || l.source === M.Source.ScreenShare) ? /* @__PURE__ */ e.createElement(
      Lt,
      {
        trackRef: l,
        onSubscriptionStatusChanged: m,
        manageSubscription: f
      }
    ) : z(l) && /* @__PURE__ */ e.createElement(
      $,
      {
        trackRef: l,
        onSubscriptionStatusChanged: m
      }
    ), /* @__PURE__ */ e.createElement("div", { className: "lk-participant-placeholder" }, /* @__PURE__ */ e.createElement(ut, null)), /* @__PURE__ */ e.createElement("div", { className: "lk-participant-metadata" }, /* @__PURE__ */ e.createElement("div", { className: "lk-participant-metadata-item" }, l.source === M.Source.Camera ? /* @__PURE__ */ e.createElement(e.Fragment, null, u && /* @__PURE__ */ e.createElement(Ke, { style: { marginRight: "0.25rem" } }), /* @__PURE__ */ e.createElement(
      ue,
      {
        trackRef: {
          participant: l.participant,
          source: M.Source.Microphone
        },
        show: "muted"
      }
    ), /* @__PURE__ */ e.createElement(B, null)) : /* @__PURE__ */ e.createElement(e.Fragment, null, /* @__PURE__ */ e.createElement(ie, { style: { marginRight: "0.25rem" } }), /* @__PURE__ */ e.createElement(B, null, "'s screen"))), /* @__PURE__ */ e.createElement(oe, { className: "lk-participant-metadata-item" }))), /* @__PURE__ */ e.createElement(st, { trackRef: l }))));
  }
);
function mn(t) {
  const a = R(t, { className: "lk-focus-layout" });
  return /* @__PURE__ */ e.createElement("div", { ...a }, t.children);
}
function fn({ trackRef: t, ...a }) {
  return /* @__PURE__ */ e.createElement(Ft, { trackRef: t, ...a });
}
function me({ tracks: t, ...a }) {
  return /* @__PURE__ */ e.createElement(e.Fragment, null, t.map((n) => /* @__PURE__ */ e.createElement(
    W.Provider,
    {
      value: n,
      key: le(n)
    },
    O(a.children)
  )));
}
function Zt({
  totalPageCount: t,
  nextPage: a,
  prevPage: n,
  currentPage: r,
  pagesContainer: c
}) {
  const [s, o] = e.useState(!1);
  return e.useEffect(() => {
    let l;
    return c && (l = qe(c.current, 2e3).subscribe(
      o
    )), () => {
      l && l.unsubscribe();
    };
  }, [c]), /* @__PURE__ */ e.createElement("div", { className: "lk-pagination-control", "data-lk-user-interaction": s }, /* @__PURE__ */ e.createElement("button", { className: "lk-button", onClick: n }, /* @__PURE__ */ e.createElement(D, null)), /* @__PURE__ */ e.createElement("span", { className: "lk-pagination-count" }, `${r} of ${t}`), /* @__PURE__ */ e.createElement("button", { className: "lk-button", onClick: a }, /* @__PURE__ */ e.createElement(D, null)));
}
const Ht = /* @__PURE__ */ e.forwardRef(
  function({ totalPageCount: a, currentPage: n }, r) {
    const c = new Array(a).fill("").map((s, o) => o + 1 === n ? /* @__PURE__ */ e.createElement("span", { "data-lk-active": !0, key: o }) : /* @__PURE__ */ e.createElement("span", { key: o }));
    return /* @__PURE__ */ e.createElement("div", { ref: r, className: "lk-pagination-indicator" }, c);
  }
);
function hn({ tracks: t, ...a }) {
  const n = e.createRef(), r = e.useMemo(
    () => R(a, { className: "lk-grid-layout" }),
    [a]
  ), { layout: c } = Ce(n, t.length), s = Ie(c.maxTiles, t);
  return Pe(n, {
    onLeftSwipe: s.nextPage,
    onRightSwipe: s.prevPage
  }), /* @__PURE__ */ e.createElement("div", { ref: n, "data-lk-pagination": s.totalPageCount > 1, ...r }, /* @__PURE__ */ e.createElement(me, { tracks: s.tracks }, a.children), t.length > c.maxTiles && /* @__PURE__ */ e.createElement(e.Fragment, null, /* @__PURE__ */ e.createElement(
    Ht,
    {
      totalPageCount: s.totalPageCount,
      currentPage: s.currentPage
    }
  ), /* @__PURE__ */ e.createElement(Zt, { pagesContainer: n, ...s })));
}
const Bt = 130, jt = 140, Y = 1, fe = 16 / 10, Ot = (1 - fe) * -1;
function gn({ tracks: t, orientation: a, ...n }) {
  const r = e.useRef(null), [c, s] = e.useState(0), { width: o, height: l } = Te(r), i = a || (l >= o ? "vertical" : "horizontal"), u = i === "vertical" ? Math.max(o * Ot, Bt) : Math.max(l * fe, jt), d = De(), f = Math.max(i === "vertical" ? (l - d) / u : (o - d) / u, Y);
  let m = Math.round(f);
  Math.abs(f - c) < 0.5 ? m = Math.round(c) : c !== f && s(f);
  const E = xe(t, m);
  return e.useLayoutEffect(() => {
    r.current && (r.current.dataset.lkOrientation = i, r.current.style.setProperty("--lk-max-visible-tiles", m.toString()));
  }, [m, i]), /* @__PURE__ */ e.createElement("aside", { key: i, className: "lk-carousel", ref: r, ...n }, /* @__PURE__ */ e.createElement(me, { tracks: E }, n.children));
}
function vn({
  value: t,
  onPinChange: a,
  onWidgetChange: n,
  children: r
}) {
  const c = Ge(t);
  return e.useEffect(() => {
    A.debug("PinState Updated", { state: c.pin.state }), a && c.pin.state && a(c.pin.state);
  }, [c.pin.state, a]), e.useEffect(() => {
    A.debug("Widget Updated", { widgetState: c.widget.state }), n && c.widget.state && n(c.widget.state);
  }, [n, c.widget.state]), /* @__PURE__ */ e.createElement(ae.Provider, { value: c }, r);
}
const En = /* @__PURE__ */ e.forwardRef(
  function({ trackRef: a, ...n }, r) {
    const d = N(a), f = ne(d, { bands: 7, loPass: 300 });
    return /* @__PURE__ */ e.createElement(
      "svg",
      {
        ref: r,
        width: "100%",
        height: "100%",
        viewBox: "0 0 200 90",
        ...n,
        className: "lk-audio-visualizer"
      },
      /* @__PURE__ */ e.createElement("rect", { x: "0", y: "0", width: "100%", height: "100%" }),
      /* @__PURE__ */ e.createElement(
        "g",
        {
          style: {
            transform: `translate(${(200 - 7 * 10) / 2}px, 0)`
          }
        },
        f.map((m, E) => /* @__PURE__ */ e.createElement(
          "rect",
          {
            key: E,
            x: E * 10,
            y: 90 / 2 - m * 50 / 2,
            width: 6,
            height: m * 50
          }
        ))
      )
    );
  }
);
function pn({ participants: t, ...a }) {
  return /* @__PURE__ */ e.createElement(e.Fragment, null, t.map((n) => /* @__PURE__ */ e.createElement(ce.Provider, { value: n, key: n.identity }, O(a.children))));
}
function wn({ volume: t, muted: a }) {
  const n = Ae(
    [M.Source.Microphone, M.Source.ScreenShareAudio, M.Source.Unknown],
    {
      updateOnlyOn: [],
      onlySubscribed: !0
    }
  ).filter((r) => !r.participant.isLocal && r.publication.kind === M.Kind.Audio);
  return /* @__PURE__ */ e.createElement("div", { style: { display: "none" } }, n.map((r) => /* @__PURE__ */ e.createElement(
    $,
    {
      key: le(r),
      trackRef: r,
      volume: t,
      muted: a
    }
  )));
}
const kn = /* @__PURE__ */ e.forwardRef(function({ childrenPosition: a = "before", children: n, ...r }, c) {
  const { name: s } = Ne();
  return /* @__PURE__ */ e.createElement("span", { ref: c, ...r }, a === "before" && n, s, a === "after" && n);
});
function _t(t) {
  const a = e.useMemo(() => R(t, { className: "lk-toast" }), [t]);
  return /* @__PURE__ */ e.createElement("div", { ...a }, t.children);
}
const Wt = (t) => {
  const a = [];
  for (let n = 0; n < t; n++)
    a.push([n, t - 1 - n]);
  return a;
}, J = (t) => [[Math.floor(t / 2)], [-1]], $t = (t, a, n) => {
  const [r, c] = x(0), [s, o] = x([[]]);
  T(() => {
    if (t === "thinking")
      o(J(a));
    else if (t === "connecting" || t === "initializing") {
      const i = [...Wt(a)];
      o(i);
    } else o(t === "listening" ? J(a) : t === void 0 ? [new Array(a).fill(0).map((i, u) => u)] : [[]]);
    c(0);
  }, [t, a]);
  const l = P(null);
  return T(() => {
    let i = performance.now();
    const u = (d) => {
      d - i >= n && (c((m) => m + 1), i = d), l.current = requestAnimationFrame(u);
    };
    return l.current = requestAnimationFrame(u), () => {
      l.current !== null && cancelAnimationFrame(l.current);
    };
  }, [n, a, t, s.length]), s[r % s.length];
}, qt = /* @__PURE__ */ new Map([
  ["connecting", 2e3],
  ["initializing", 2e3],
  ["listening", 500],
  ["thinking", 150]
]), Dt = (t, a) => {
  if (t === void 0)
    return 1e3;
  let n = qt.get(t);
  if (n)
    switch (t) {
      case "connecting":
        n /= a;
        break;
    }
  return n;
}, Gt = /* @__PURE__ */ e.forwardRef(
  function({ state: a, options: n, barCount: r = 15, trackRef: c, children: s, ...o }, l) {
    const i = R(o, { className: "lk-audio-bar-visualizer" });
    let u = _();
    c && (u = c);
    const d = ne(u, {
      bands: r,
      loPass: 100,
      hiPass: 200
    }), f = (n == null ? void 0 : n.minHeight) ?? 20, m = (n == null ? void 0 : n.maxHeight) ?? 100, E = $t(
      a,
      r,
      Dt(a, r) ?? 100
    );
    return /* @__PURE__ */ e.createElement("div", { ref: l, ...i, "data-lk-va-state": a }, d.map(
      (p, h) => s ? O(s, {
        "data-lk-highlighted": E.includes(h),
        "data-lk-bar-index": h,
        className: "lk-audio-bar",
        style: { height: `${Math.min(m, Math.max(f, p * 100 + 5))}%` }
      }) : /* @__PURE__ */ e.createElement(
        "span",
        {
          key: h,
          "data-lk-highlighted": E.includes(h),
          "data-lk-bar-index": h,
          className: `lk-audio-bar ${E.includes(h) && "lk-highlighted"}`,
          style: {
            // TODO transform animations would be more performant, however the border-radius gets distorted when using scale transforms. a 9-slice approach (or 3 in this case) could work
            // transform: `scale(1, ${Math.min(maxHeight, Math.max(minHeight, volume))}`,
            height: `${Math.min(m, Math.max(f, p * 100 + 5))}%`
          }
        }
      )
    ));
  }
), Mn = /* @__PURE__ */ e.forwardRef(
  function({
    children: a,
    disableSpeakingIndicator: n,
    onParticipantClick: r,
    trackRef: c,
    ...s
  }, o) {
    const l = N(c), { elementProps: i } = te({
      trackRef: l,
      htmlProps: s,
      disableSpeakingIndicator: n,
      onParticipantClick: r
    });
    return /* @__PURE__ */ e.createElement("div", { ref: o, style: { position: "relative", minHeight: "160px" }, ...i }, /* @__PURE__ */ e.createElement(W.Provider, { value: l }, a ?? /* @__PURE__ */ e.createElement(e.Fragment, null, z(l) && /* @__PURE__ */ e.createElement($, { trackRef: l }), /* @__PURE__ */ e.createElement(Gt, { barCount: 7, options: { minHeight: 8 } }), /* @__PURE__ */ e.createElement("div", { className: "lk-participant-metadata" }, /* @__PURE__ */ e.createElement("div", { className: "lk-participant-metadata-item" }, /* @__PURE__ */ e.createElement(ue, { trackRef: l }), /* @__PURE__ */ e.createElement(B, null)), /* @__PURE__ */ e.createElement(oe, { className: "lk-participant-metadata-item" })))));
  }
);
function Rn(t) {
  const [a, n] = e.useState(void 0), r = K(t.room);
  return e.useEffect(() => {
    switch (r) {
      case F.Reconnecting:
        n(
          /* @__PURE__ */ e.createElement(e.Fragment, null, /* @__PURE__ */ e.createElement(G, { className: "lk-spinner" }), " Reconnecting")
        );
        break;
      case F.Connecting:
        n(
          /* @__PURE__ */ e.createElement(e.Fragment, null, /* @__PURE__ */ e.createElement(G, { className: "lk-spinner" }), " Connecting")
        );
        break;
      case F.Disconnected:
        n(/* @__PURE__ */ e.createElement(e.Fragment, null, "Disconnected"));
        break;
      default:
        n(void 0);
        break;
    }
  }, [r]), a ? /* @__PURE__ */ e.createElement(_t, { className: "lk-toast-connection-state" }, a) : /* @__PURE__ */ e.createElement(e.Fragment, null);
}
const yn = /* @__PURE__ */ e.forwardRef(
  function({ entry: a, hideName: n = !1, hideTimestamp: r = !1, messageFormatter: c, ...s }, o) {
    var f, m, E;
    const l = e.useMemo(() => c ? c(a.message) : a.message, [a.message, c]), i = !!a.editTimestamp, u = new Date(a.timestamp), d = navigator ? navigator.language : "en-US";
    return /* @__PURE__ */ e.createElement(
      "li",
      {
        ref: o,
        className: "lk-chat-entry",
        title: u.toLocaleTimeString(d, { timeStyle: "full" }),
        "data-lk-message-origin": (f = a.from) != null && f.isLocal ? "local" : "remote",
        ...s
      },
      (!r || !n || i) && /* @__PURE__ */ e.createElement("span", { className: "lk-meta-data" }, !n && /* @__PURE__ */ e.createElement("strong", { className: "lk-participant-name" }, ((m = a.from) == null ? void 0 : m.name) ?? ((E = a.from) == null ? void 0 : E.identity)), (!r || i) && /* @__PURE__ */ e.createElement("span", { className: "lk-timestamp" }, i && "edited ", u.toLocaleTimeString(d, { timeStyle: "short" }))),
      /* @__PURE__ */ e.createElement("span", { className: "lk-message-body" }, l)
    );
  }
);
function bn(t) {
  return Qe(t, Ue()).map((a, n) => {
    if (typeof a == "string")
      return a;
    {
      const r = a.content.toString(), c = a.type === "url" ? /^http(s?):\/\//.test(r) ? r : `https://${r}` : `mailto:${r}`;
      return /* @__PURE__ */ e.createElement("a", { className: "lk-chat-link", key: n, href: c, target: "_blank", rel: "noreferrer" }, r);
    }
  });
}
export {
  En as A,
  Gt as B,
  tn as C,
  nn as D,
  Ye as E,
  mn as F,
  hn as G,
  D as H,
  Je as I,
  Ke as J,
  et as K,
  vn as L,
  sn as M,
  tt as N,
  nt as O,
  Ft as P,
  at as Q,
  wn as R,
  an as S,
  dn as T,
  rt as U,
  Lt as V,
  ct as W,
  ie as X,
  lt as Y,
  G as Z,
  it as _,
  yn as a,
  ut as b,
  rn as c,
  cn as d,
  ln as e,
  un as f,
  gn as g,
  fn as h,
  Rn as i,
  me as j,
  Mn as k,
  _t as l,
  bn as m,
  Kt as n,
  en as o,
  st as p,
  on as q,
  oe as r,
  $ as s,
  B as t,
  ue as u,
  pn as v,
  kn as w,
  zt as x,
  Vt as y,
  Xe as z
};
//# sourceMappingURL=components-DZxz2YwG.mjs.map
