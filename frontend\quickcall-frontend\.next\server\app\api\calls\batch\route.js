(()=>{var e={};e.id=4,e.ids=[4],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},23916:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>m,routeModule:()=>p,serverHooks:()=>x,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>h});var s={};t.r(s),t.d(s,{POST:()=>l});var a=t(96559),o=t(48088),n=t(37719),i=t(32190),c=t(79646),u=t(28354);async function l(e){try{let{phoneNumbers:r}=await e.json();if(!r||!Array.isArray(r)||0===r.length)return i.NextResponse.json({error:"Valid phone numbers array is required"},{status:400});let t=[];for(let e of r)try{let r=`lk dispatch create --new-room --agent-name outbound-caller --metadata ${e}`;console.log(`Simulating execution of: ${r}`),t.push({phoneNumber:e,callId:`call-batch-${Date.now()}-${Math.floor(1e3*Math.random())}`,status:"initiated",command:r})}catch(r){console.error(`Error executing LiveKit command for ${e}:`,r),t.push({phoneNumber:e,status:"failed",error:"Failed to initiate call"})}return i.NextResponse.json({success:!0,batchId:`batch-${Date.now()}`,totalCalls:r.length,results:t})}catch(e){return console.error("Error processing batch request:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}t.n(u)().promisify(c.exec);let p=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/calls/batch/route",pathname:"/api/calls/batch",filename:"route",bundlePath:"app/api/calls/batch/route"},resolvedPagePath:"E:\\quickcall\\frontend\\quickcall-frontend\\src\\app\\api\\calls\\batch\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:h,serverHooks:x}=p;function m(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:h})}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79646:e=>{"use strict";e.exports=require("child_process")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,580],()=>t(23916));module.exports=s})();