// Copyright 2021-2024 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
import { Message } from "../message.js";
/**
 * Create a new message type using the given runtime.
 */
export function makeMessageType(runtime, typeName, fields, opt) {
    var _a;
    const localName = (_a = opt === null || opt === void 0 ? void 0 : opt.localName) !== null && _a !== void 0 ? _a : typeName.substring(typeName.lastIndexOf(".") + 1);
    const type = {
        [localName]: function (data) {
            runtime.util.initFields(this);
            runtime.util.initPartial(data, this);
        },
    }[localName];
    Object.setPrototypeOf(type.prototype, new Message());
    Object.assign(type, {
        runtime,
        typeName,
        fields: runtime.util.newFieldList(fields),
        fromBinary(bytes, options) {
            return new type().fromBinary(bytes, options);
        },
        fromJson(jsonValue, options) {
            return new type().fromJson(jsonValue, options);
        },
        fromJsonString(jsonString, options) {
            return new type().fromJsonString(jsonString, options);
        },
        equals(a, b) {
            return runtime.util.equals(type, a, b);
        },
    });
    return type;
}
