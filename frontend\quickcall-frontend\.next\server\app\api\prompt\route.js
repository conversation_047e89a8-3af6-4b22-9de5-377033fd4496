(()=>{var e={};e.id=508,e.ids=[508],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9781:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>d,serverHooks:()=>h,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>y});var o={};t.r(o),t.d(o,{GET:()=>c,POST:()=>l});var s=t(96559),n=t(48088),a=t(37719),i=t(32190);async function p(){try{return`# Who You Are
You are [<PERSON><PERSON>], a professional and approachable Voice AI Utility Scout for [SolarPros Solutions]. Your role is to engage potential customers, qualify them for solar solutions, and connect them with a solar energy consultant for a discovery call.

# Your Goal for the Call
Your primary objective is to identify prospects interested in solar energy solutions, qualify their eligibility based on predefined criteria, and schedule a discovery appointment to discuss personalized solar plans.

# Call Actions
## Introduction
Start the conversation warmly and professionally.
After the custom greeting:
[Hello, this is [<PERSON>my] with [SolarPros Solutions]. Is this David I'm speaking with?]
If the user confirms:
"Great! We're reaching out because we noticed your area is ideal for solar energy, and we'd love to share how switching to solar can save you money and reduce your energy footprint, do you have a quick minute to chat? It won't take long."
If the user is hesitant:
"I promise to keep this brief. You might find this opportunity really beneficial."`}catch(e){throw console.error("Error extracting system prompt:",e),e}}async function u(e){try{return console.log("New system prompt:",e),!0}catch(e){throw console.error("Error updating system prompt:",e),e}}async function c(){try{let e=await p();return i.NextResponse.json({systemPrompt:e})}catch(e){return console.error("Error getting system prompt:",e),i.NextResponse.json({error:"Failed to get system prompt"},{status:500})}}async function l(e){try{let{prompt:r}=await e.json();if(!r)return i.NextResponse.json({error:"System prompt is required"},{status:400});if(await u(r))return i.NextResponse.json({success:!0,message:"System prompt updated successfully"});return i.NextResponse.json({error:"Failed to update system prompt"},{status:500})}catch(e){return console.error("Error updating system prompt:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let d=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/prompt/route",pathname:"/api/prompt",filename:"route",bundlePath:"app/api/prompt/route"},resolvedPagePath:"E:\\quickcall\\frontend\\quickcall-frontend\\src\\app\\api\\prompt\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:m,workUnitAsyncStorage:y,serverHooks:h}=d;function f(){return(0,a.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:y})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[447,580],()=>t(9781));module.exports=o})();