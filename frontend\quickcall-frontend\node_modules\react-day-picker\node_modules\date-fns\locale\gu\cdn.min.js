(()=>{var A;function U(B){return U=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(C){return typeof C}:function(C){return C&&typeof Symbol=="function"&&C.constructor===Symbol&&C!==Symbol.prototype?"symbol":typeof C},U(B)}function K(B,C){var G=Object.keys(B);if(Object.getOwnPropertySymbols){var H=Object.getOwnPropertySymbols(B);C&&(H=H.filter(function(J){return Object.getOwnPropertyDescriptor(B,J).enumerable})),G.push.apply(G,H)}return G}function Q(B){for(var C=1;C<arguments.length;C++){var G=arguments[C]!=null?arguments[C]:{};C%2?K(Object(G),!0).forEach(function(H){x(B,H,G[H])}):Object.getOwnPropertyDescriptors?Object.defineProperties(B,Object.getOwnPropertyDescriptors(G)):K(Object(G)).forEach(function(H){Object.defineProperty(B,H,Object.getOwnPropertyDescriptor(G,H))})}return B}function x(B,C,G){if(C=N(C),C in B)Object.defineProperty(B,C,{value:G,enumerable:!0,configurable:!0,writable:!0});else B[C]=G;return B}function N(B){var C=z(B,"string");return U(C)=="symbol"?C:String(C)}function z(B,C){if(U(B)!="object"||!B)return B;var G=B[Symbol.toPrimitive];if(G!==void 0){var H=G.call(B,C||"default");if(U(H)!="object")return H;throw new TypeError("@@toPrimitive must return a primitive value.")}return(C==="string"?String:Number)(B)}var W=Object.defineProperty,GB=function B(C,G){for(var H in G)W(C,H,{get:G[H],enumerable:!0,configurable:!0,set:function J(X){return G[H]=function(){return X}}})},S={lessThanXSeconds:{one:"\u0AB9\u0AAE\u0AA3\u0ABE\u0A82",other:"\u200B\u0A86\u0AB6\u0AB0\u0AC7 {{count}} \u0AB8\u0AC7\u0A95\u0A82\u0AA1"},xSeconds:{one:"1 \u0AB8\u0AC7\u0A95\u0A82\u0AA1",other:"{{count}} \u0AB8\u0AC7\u0A95\u0A82\u0AA1"},halfAMinute:"\u0A85\u0AA1\u0AA7\u0AC0 \u0AAE\u0ABF\u0AA8\u0ABF\u0A9F",lessThanXMinutes:{one:"\u0A86 \u0AAE\u0ABF\u0AA8\u0ABF\u0A9F",other:"\u200B\u0A86\u0AB6\u0AB0\u0AC7 {{count}} \u0AAE\u0ABF\u0AA8\u0ABF\u0A9F"},xMinutes:{one:"1 \u0AAE\u0ABF\u0AA8\u0ABF\u0A9F",other:"{{count}} \u0AAE\u0ABF\u0AA8\u0ABF\u0A9F"},aboutXHours:{one:"\u200B\u0A86\u0AB6\u0AB0\u0AC7 1 \u0A95\u0AB2\u0ABE\u0A95",other:"\u200B\u0A86\u0AB6\u0AB0\u0AC7 {{count}} \u0A95\u0AB2\u0ABE\u0A95"},xHours:{one:"1 \u0A95\u0AB2\u0ABE\u0A95",other:"{{count}} \u0A95\u0AB2\u0ABE\u0A95"},xDays:{one:"1 \u0AA6\u0ABF\u0AB5\u0AB8",other:"{{count}} \u0AA6\u0ABF\u0AB5\u0AB8"},aboutXWeeks:{one:"\u0A86\u0AB6\u0AB0\u0AC7 1 \u0A85\u0AA0\u0AB5\u0ABE\u0AA1\u0ABF\u0AAF\u0AC1\u0A82",other:"\u0A86\u0AB6\u0AB0\u0AC7 {{count}} \u0A85\u0AA0\u0AB5\u0ABE\u0AA1\u0ABF\u0AAF\u0ABE"},xWeeks:{one:"1 \u0A85\u0AA0\u0AB5\u0ABE\u0AA1\u0ABF\u0AAF\u0AC1\u0A82",other:"{{count}} \u0A85\u0AA0\u0AB5\u0ABE\u0AA1\u0ABF\u0AAF\u0ABE"},aboutXMonths:{one:"\u0A86\u0AB6\u0AB0\u0AC7 1 \u0AAE\u0AB9\u0ABF\u0AA8\u0ACB",other:"\u0A86\u0AB6\u0AB0\u0AC7 {{count}} \u0AAE\u0AB9\u0ABF\u0AA8\u0ABE"},xMonths:{one:"1 \u0AAE\u0AB9\u0ABF\u0AA8\u0ACB",other:"{{count}} \u0AAE\u0AB9\u0ABF\u0AA8\u0ABE"},aboutXYears:{one:"\u0A86\u0AB6\u0AB0\u0AC7 1 \u0AB5\u0AB0\u0ACD\u0AB7",other:"\u0A86\u0AB6\u0AB0\u0AC7 {{count}} \u0AB5\u0AB0\u0ACD\u0AB7"},xYears:{one:"1 \u0AB5\u0AB0\u0ACD\u0AB7",other:"{{count}} \u0AB5\u0AB0\u0ACD\u0AB7"},overXYears:{one:"1 \u0AB5\u0AB0\u0ACD\u0AB7\u0AA5\u0AC0 \u0AB5\u0AA7\u0AC1",other:"{{count}} \u0AB5\u0AB0\u0ACD\u0AB7\u0AA5\u0AC0 \u0AB5\u0AA7\u0AC1"},almostXYears:{one:"\u0AB2\u0A97\u0AAD\u0A97 1 \u0AB5\u0AB0\u0ACD\u0AB7",other:"\u0AB2\u0A97\u0AAD\u0A97 {{count}} \u0AB5\u0AB0\u0ACD\u0AB7"}},D=function B(C,G,H){var J,X=S[C];if(typeof X==="string")J=X;else if(G===1)J=X.one;else J=X.other.replace("{{count}}",String(G));if(H!==null&&H!==void 0&&H.addSuffix)if(H.comparison&&H.comparison>0)return J+"\u0AAE\u0ABE\u0A82";else return J+" \u0AAA\u0AB9\u0AC7\u0AB2\u0ABE\u0A82";return J};function $(B){return function(){var C=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},G=C.width?String(C.width):B.defaultWidth,H=B.formats[G]||B.formats[B.defaultWidth];return H}}var M={full:"EEEE, d MMMM, y",long:"d MMMM, y",medium:"d MMM, y",short:"d/M/yy"},R={full:"hh:mm:ss a zzzz",long:"hh:mm:ss a z",medium:"hh:mm:ss a",short:"hh:mm a"},L={full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},V={date:$({formats:M,defaultWidth:"full"}),time:$({formats:R,defaultWidth:"full"}),dateTime:$({formats:L,defaultWidth:"full"})},j={lastWeek:"'\u0AAA\u0ABE\u0A9B\u0AB2\u0ABE' eeee p",yesterday:"'\u0A97\u0A88\u0A95\u0ABE\u0AB2\u0AC7' p",today:"'\u0A86\u0A9C\u0AC7' p",tomorrow:"'\u0A86\u0AB5\u0AA4\u0AC0\u0A95\u0ABE\u0AB2\u0AC7' p",nextWeek:"eeee p",other:"P"},w=function B(C,G,H,J){return j[C]};function I(B){return function(C,G){var H=G!==null&&G!==void 0&&G.context?String(G.context):"standalone",J;if(H==="formatting"&&B.formattingValues){var X=B.defaultFormattingWidth||B.defaultWidth,Y=G!==null&&G!==void 0&&G.width?String(G.width):X;J=B.formattingValues[Y]||B.formattingValues[X]}else{var Z=B.defaultWidth,q=G!==null&&G!==void 0&&G.width?String(G.width):B.defaultWidth;J=B.values[q]||B.values[Z]}var T=B.argumentCallback?B.argumentCallback(C):C;return J[T]}}var _={narrow:["\u0A88\u0AB8\u0AAA\u0AC2","\u0A88\u0AB8"],abbreviated:["\u0A88.\u0AB8.\u0AAA\u0AC2\u0AB0\u0ACD\u0AB5\u0AC7","\u0A88.\u0AB8."],wide:["\u0A88\u0AB8\u0AB5\u0AC0\u0AB8\u0AA8 \u0AAA\u0AC2\u0AB0\u0ACD\u0AB5\u0AC7","\u0A88\u0AB8\u0AB5\u0AC0\u0AB8\u0AA8"]},f={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1\u0AB2\u0ACB \u0AA4\u0ACD\u0AB0\u0ABF\u0AAE\u0ABE\u0AB8","2\u0A9C\u0ACB \u0AA4\u0ACD\u0AB0\u0ABF\u0AAE\u0ABE\u0AB8","3\u0A9C\u0ACB \u0AA4\u0ACD\u0AB0\u0ABF\u0AAE\u0ABE\u0AB8","4\u0AA5\u0ACB \u0AA4\u0ACD\u0AB0\u0ABF\u0AAE\u0ABE\u0AB8"]},v={narrow:["\u0A9C\u0ABE","\u0AAB\u0AC7","\u0AAE\u0ABE","\u0A8F","\u0AAE\u0AC7","\u0A9C\u0AC2","\u0A9C\u0AC1","\u0A93","\u0AB8","\u0A93","\u0AA8","\u0AA1\u0ABF"],abbreviated:["\u0A9C\u0ABE\u0AA8\u0ACD\u0AAF\u0AC1","\u0AAB\u0AC7\u0AAC\u0ACD\u0AB0\u0AC1","\u0AAE\u0ABE\u0AB0\u0ACD\u0A9A","\u0A8F\u0AAA\u0ACD\u0AB0\u0ABF\u0AB2","\u0AAE\u0AC7","\u0A9C\u0AC2\u0AA8","\u0A9C\u0AC1\u0AB2\u0ABE\u0A88","\u0A91\u0A97\u0AB8\u0ACD\u0A9F","\u0AB8\u0AAA\u0ACD\u0A9F\u0AC7","\u0A93\u0A95\u0ACD\u0A9F\u0ACB","\u0AA8\u0AB5\u0AC7","\u0AA1\u0ABF\u0AB8\u0AC7"],wide:["\u0A9C\u0ABE\u0AA8\u0ACD\u0AAF\u0AC1\u0A86\u0AB0\u0AC0","\u0AAB\u0AC7\u0AAC\u0ACD\u0AB0\u0AC1\u0A86\u0AB0\u0AC0","\u0AAE\u0ABE\u0AB0\u0ACD\u0A9A","\u0A8F\u0AAA\u0ACD\u0AB0\u0ABF\u0AB2","\u0AAE\u0AC7","\u0A9C\u0AC2\u0AA8","\u0A9C\u0AC1\u0AB2\u0ABE\u0A87","\u0A93\u0A97\u0AB8\u0ACD\u0A9F","\u0AB8\u0AAA\u0ACD\u0A9F\u0AC7\u0AAE\u0ACD\u0AAC\u0AB0","\u0A93\u0A95\u0ACD\u0A9F\u0ACB\u0AAC\u0AB0","\u0AA8\u0AB5\u0AC7\u0AAE\u0ACD\u0AAC\u0AB0","\u0AA1\u0ABF\u0AB8\u0AC7\u0AAE\u0ACD\u0AAC\u0AB0"]},F={narrow:["\u0AB0","\u0AB8\u0ACB","\u0AAE\u0A82","\u0AAC\u0AC1","\u0A97\u0AC1","\u0AB6\u0AC1","\u0AB6"],short:["\u0AB0","\u0AB8\u0ACB","\u0AAE\u0A82","\u0AAC\u0AC1","\u0A97\u0AC1","\u0AB6\u0AC1","\u0AB6"],abbreviated:["\u0AB0\u0AB5\u0ABF","\u0AB8\u0ACB\u0AAE","\u0AAE\u0A82\u0A97\u0AB3","\u0AAC\u0AC1\u0AA7","\u0A97\u0AC1\u0AB0\u0AC1","\u0AB6\u0AC1\u0A95\u0ACD\u0AB0","\u0AB6\u0AA8\u0ABF"],wide:["\u0AB0\u0AB5\u0ABF\u0AB5\u0ABE\u0AB0","\u0AB8\u0ACB\u0AAE\u0AB5\u0ABE\u0AB0","\u0AAE\u0A82\u0A97\u0AB3\u0AB5\u0ABE\u0AB0","\u0AAC\u0AC1\u0AA7\u0AB5\u0ABE\u0AB0","\u0A97\u0AC1\u0AB0\u0AC1\u0AB5\u0ABE\u0AB0","\u0AB6\u0AC1\u0A95\u0ACD\u0AB0\u0AB5\u0ABE\u0AB0","\u0AB6\u0AA8\u0ABF\u0AB5\u0ABE\u0AB0"]},P={narrow:{am:"AM",pm:"PM",midnight:"\u0AAE.\u0AB0\u0ABE\u0AA4\u0ACD\u0AB0\u0ABF",noon:"\u0AAC.",morning:"\u0AB8\u0AB5\u0ABE\u0AB0\u0AC7",afternoon:"\u0AAC\u0AAA\u0ACB\u0AB0\u0AC7",evening:"\u0AB8\u0ABE\u0A82\u0A9C\u0AC7",night:"\u0AB0\u0ABE\u0AA4\u0ACD\u0AB0\u0AC7"},abbreviated:{am:"AM",pm:"PM",midnight:"\u200B\u0AAE\u0AA7\u0ACD\u0AAF\u0AB0\u0ABE\u0AA4\u0ACD\u0AB0\u0ABF",noon:"\u0AAC\u0AAA\u0ACB\u0AB0\u0AC7",morning:"\u0AB8\u0AB5\u0ABE\u0AB0\u0AC7",afternoon:"\u0AAC\u0AAA\u0ACB\u0AB0\u0AC7",evening:"\u0AB8\u0ABE\u0A82\u0A9C\u0AC7",night:"\u0AB0\u0ABE\u0AA4\u0ACD\u0AB0\u0AC7"},wide:{am:"AM",pm:"PM",midnight:"\u200B\u0AAE\u0AA7\u0ACD\u0AAF\u0AB0\u0ABE\u0AA4\u0ACD\u0AB0\u0ABF",noon:"\u0AAC\u0AAA\u0ACB\u0AB0\u0AC7",morning:"\u0AB8\u0AB5\u0ABE\u0AB0\u0AC7",afternoon:"\u0AAC\u0AAA\u0ACB\u0AB0\u0AC7",evening:"\u0AB8\u0ABE\u0A82\u0A9C\u0AC7",night:"\u0AB0\u0ABE\u0AA4\u0ACD\u0AB0\u0AC7"}},k={narrow:{am:"AM",pm:"PM",midnight:"\u0AAE.\u0AB0\u0ABE\u0AA4\u0ACD\u0AB0\u0ABF",noon:"\u0AAC\u0AAA\u0ACB\u0AB0\u0AC7",morning:"\u0AB8\u0AB5\u0ABE\u0AB0\u0AC7",afternoon:"\u0AAC\u0AAA\u0ACB\u0AB0\u0AC7",evening:"\u0AB8\u0ABE\u0A82\u0A9C\u0AC7",night:"\u0AB0\u0ABE\u0AA4\u0ACD\u0AB0\u0AC7"},abbreviated:{am:"AM",pm:"PM",midnight:"\u0AAE\u0AA7\u0ACD\u0AAF\u0AB0\u0ABE\u0AA4\u0ACD\u0AB0\u0ABF",noon:"\u0AAC\u0AAA\u0ACB\u0AB0\u0AC7",morning:"\u0AB8\u0AB5\u0ABE\u0AB0\u0AC7",afternoon:"\u0AAC\u0AAA\u0ACB\u0AB0\u0AC7",evening:"\u0AB8\u0ABE\u0A82\u0A9C\u0AC7",night:"\u0AB0\u0ABE\u0AA4\u0ACD\u0AB0\u0AC7"},wide:{am:"AM",pm:"PM",midnight:"\u200B\u0AAE\u0AA7\u0ACD\u0AAF\u0AB0\u0ABE\u0AA4\u0ACD\u0AB0\u0ABF",noon:"\u0AAC\u0AAA\u0ACB\u0AB0\u0AC7",morning:"\u0AB8\u0AB5\u0ABE\u0AB0\u0AC7",afternoon:"\u0AAC\u0AAA\u0ACB\u0AB0\u0AC7",evening:"\u0AB8\u0ABE\u0A82\u0A9C\u0AC7",night:"\u0AB0\u0ABE\u0AA4\u0ACD\u0AB0\u0AC7"}},b=function B(C,G){return String(C)},h={ordinalNumber:b,era:I({values:_,defaultWidth:"wide"}),quarter:I({values:f,defaultWidth:"wide",argumentCallback:function B(C){return C-1}}),month:I({values:v,defaultWidth:"wide"}),day:I({values:F,defaultWidth:"wide"}),dayPeriod:I({values:P,defaultWidth:"wide",formattingValues:k,defaultFormattingWidth:"wide"})};function O(B){return function(C){var G=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},H=G.width,J=H&&B.matchPatterns[H]||B.matchPatterns[B.defaultMatchWidth],X=C.match(J);if(!X)return null;var Y=X[0],Z=H&&B.parsePatterns[H]||B.parsePatterns[B.defaultParseWidth],q=Array.isArray(Z)?c(Z,function(E){return E.test(Y)}):m(Z,function(E){return E.test(Y)}),T;T=B.valueCallback?B.valueCallback(q):q,T=G.valueCallback?G.valueCallback(T):T;var CB=C.slice(Y.length);return{value:T,rest:CB}}}function m(B,C){for(var G in B)if(Object.prototype.hasOwnProperty.call(B,G)&&C(B[G]))return G;return}function c(B,C){for(var G=0;G<B.length;G++)if(C(B[G]))return G;return}function y(B){return function(C){var G=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},H=C.match(B.matchPattern);if(!H)return null;var J=H[0],X=C.match(B.parsePattern);if(!X)return null;var Y=B.valueCallback?B.valueCallback(X[0]):X[0];Y=G.valueCallback?G.valueCallback(Y):Y;var Z=C.slice(J.length);return{value:Y,rest:Z}}}var p=/^(\d+)(લ|જ|થ|ઠ્ઠ|મ)?/i,d=/\d+/i,g={narrow:/^(ઈસપૂ|ઈસ)/i,abbreviated:/^(ઈ\.સ\.પૂર્વે|ઈ\.સ\.)/i,wide:/^(ઈસવીસન\sપૂર્વે|ઈસવીસન)/i},u={any:[/^ઈસપૂ/i,/^ઈસ/i]},l={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](લો|જો|થો)? ત્રિમાસ/i},i={any:[/1/i,/2/i,/3/i,/4/i]},n={narrow:/^[જાફેમાએમેજૂજુઓસઓનડિ]/i,abbreviated:/^(જાન્યુ|ફેબ્રુ|માર્ચ|એપ્રિલ|મે|જૂન|જુલાઈ|ઑગસ્ટ|સપ્ટે|ઓક્ટો|નવે|ડિસે)/i,wide:/^(જાન્યુઆરી|ફેબ્રુઆરી|માર્ચ|એપ્રિલ|મે|જૂન|જુલાઇ|ઓગસ્ટ|સપ્ટેમ્બર|ઓક્ટોબર|નવેમ્બર|ડિસેમ્બર)/i},s={narrow:[/^જા/i,/^ફે/i,/^મા/i,/^એ/i,/^મે/i,/^જૂ/i,/^જુ/i,/^ઑગ/i,/^સ/i,/^ઓક્ટો/i,/^ન/i,/^ડિ/i],any:[/^જા/i,/^ફે/i,/^મા/i,/^એ/i,/^મે/i,/^જૂ/i,/^જુ/i,/^ઑગ/i,/^સ/i,/^ઓક્ટો/i,/^ન/i,/^ડિ/i]},o={narrow:/^(ર|સો|મં|બુ|ગુ|શુ|શ)/i,short:/^(ર|સો|મં|બુ|ગુ|શુ|શ)/i,abbreviated:/^(રવિ|સોમ|મંગળ|બુધ|ગુરુ|શુક્ર|શનિ)/i,wide:/^(રવિવાર|સોમવાર|મંગળવાર|બુધવાર|ગુરુવાર|શુક્રવાર|શનિવાર)/i},r={narrow:[/^ર/i,/^સો/i,/^મં/i,/^બુ/i,/^ગુ/i,/^શુ/i,/^શ/i],any:[/^ર/i,/^સો/i,/^મં/i,/^બુ/i,/^ગુ/i,/^શુ/i,/^શ/i]},a={narrow:/^(a|p|મ\.?|સ|બ|સાં|રા)/i,any:/^(a|p|મ\.?|સ|બ|સાં|રા)/i},e={any:{am:/^a/i,pm:/^p/i,midnight:/^મ\.?/i,noon:/^બ/i,morning:/સ/i,afternoon:/બ/i,evening:/સાં/i,night:/રા/i}},t={ordinalNumber:y({matchPattern:p,parsePattern:d,valueCallback:function B(C){return parseInt(C,10)}}),era:O({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:u,defaultParseWidth:"any"}),quarter:O({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any",valueCallback:function B(C){return C+1}}),month:O({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),day:O({matchPatterns:o,defaultMatchWidth:"wide",parsePatterns:r,defaultParseWidth:"any"}),dayPeriod:O({matchPatterns:a,defaultMatchWidth:"any",parsePatterns:e,defaultParseWidth:"any"})},BB={code:"gu",formatDistance:D,formatLong:V,formatRelative:w,localize:h,match:t,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=Q(Q({},window.dateFns),{},{locale:Q(Q({},(A=window.dateFns)===null||A===void 0?void 0:A.locale),{},{gu:BB})})})();

//# debugId=2C807A4A88F941EB64756E2164756E21
