(()=>{var e={};e.id=947,e.ids=[947],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},46430:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>x,routeModule:()=>u,serverHooks:()=>v,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var n={};t.r(n),t.d(n,{GET:()=>p,revalidate:()=>c});var s=t(96559),o=t(48088),a=t(37719),i=t(32190);let c=0;async function p(){try{process.env.BACKEND_URL;let e=`voice_assistant_user_${Math.floor(1e4*Math.random())}`,r=`voice_assistant_room_${Math.floor(1e4*Math.random())}`,t={serverUrl:process.env.LIVEKIT_URL||"wss://your-livekit-url",roomName:r,participantToken:"mock-token",participantName:e};return i.NextResponse.json(t)}catch(e){return console.error("Error generating connection details:",e),i.NextResponse.json({error:"Failed to generate connection details"},{status:500})}}let u=new s.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/connection-details/route",pathname:"/api/connection-details",filename:"route",bundlePath:"app/api/connection-details/route"},resolvedPagePath:"E:\\quickcall\\frontend\\quickcall-frontend\\src\\app\\api\\connection-details\\route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:v}=u;function x(){return(0,a.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[447,580],()=>t(46430));module.exports=n})();