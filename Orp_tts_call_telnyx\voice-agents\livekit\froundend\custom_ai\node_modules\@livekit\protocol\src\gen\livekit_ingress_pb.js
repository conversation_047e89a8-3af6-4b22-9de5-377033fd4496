// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// @generated by protoc-gen-es v1.10.0 with parameter "target=dts+js"
// @generated from file livekit_ingress.proto (package livekit, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { proto3 } from "@bufbuild/protobuf";
import { AudioCodec, TrackInfo, TrackSource, VideoCodec, VideoLayer } from "./livekit_models_pb.js";

/**
 * @generated from enum livekit.IngressInput
 */
export const IngressInput = /*@__PURE__*/ proto3.makeEnum(
  "livekit.IngressInput",
  [
    {no: 0, name: "RTMP_INPUT"},
    {no: 1, name: "WHIP_INPUT"},
    {no: 2, name: "URL_INPUT"},
  ],
);

/**
 * @generated from enum livekit.IngressAudioEncodingPreset
 */
export const IngressAudioEncodingPreset = /*@__PURE__*/ proto3.makeEnum(
  "livekit.IngressAudioEncodingPreset",
  [
    {no: 0, name: "OPUS_STEREO_96KBPS"},
    {no: 1, name: "OPUS_MONO_64KBS"},
  ],
);

/**
 * @generated from enum livekit.IngressVideoEncodingPreset
 */
export const IngressVideoEncodingPreset = /*@__PURE__*/ proto3.makeEnum(
  "livekit.IngressVideoEncodingPreset",
  [
    {no: 0, name: "H264_720P_30FPS_3_LAYERS"},
    {no: 1, name: "H264_1080P_30FPS_3_LAYERS"},
    {no: 2, name: "H264_540P_25FPS_2_LAYERS"},
    {no: 3, name: "H264_720P_30FPS_1_LAYER"},
    {no: 4, name: "H264_1080P_30FPS_1_LAYER"},
    {no: 5, name: "H264_720P_30FPS_3_LAYERS_HIGH_MOTION"},
    {no: 6, name: "H264_1080P_30FPS_3_LAYERS_HIGH_MOTION"},
    {no: 7, name: "H264_540P_25FPS_2_LAYERS_HIGH_MOTION"},
    {no: 8, name: "H264_720P_30FPS_1_LAYER_HIGH_MOTION"},
    {no: 9, name: "H264_1080P_30FPS_1_LAYER_HIGH_MOTION"},
  ],
);

/**
 * @generated from message livekit.CreateIngressRequest
 */
export const CreateIngressRequest = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.CreateIngressRequest",
  () => [
    { no: 1, name: "input_type", kind: "enum", T: proto3.getEnumType(IngressInput) },
    { no: 9, name: "url", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "room_name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "participant_identity", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 5, name: "participant_name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 10, name: "participant_metadata", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 8, name: "bypass_transcoding", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 11, name: "enable_transcoding", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 6, name: "audio", kind: "message", T: IngressAudioOptions },
    { no: 7, name: "video", kind: "message", T: IngressVideoOptions },
    { no: 12, name: "enabled", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
  ],
);

/**
 * @generated from message livekit.IngressAudioOptions
 */
export const IngressAudioOptions = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.IngressAudioOptions",
  () => [
    { no: 1, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "source", kind: "enum", T: proto3.getEnumType(TrackSource) },
    { no: 3, name: "preset", kind: "enum", T: proto3.getEnumType(IngressAudioEncodingPreset), oneof: "encoding_options" },
    { no: 4, name: "options", kind: "message", T: IngressAudioEncodingOptions, oneof: "encoding_options" },
  ],
);

/**
 * @generated from message livekit.IngressVideoOptions
 */
export const IngressVideoOptions = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.IngressVideoOptions",
  () => [
    { no: 1, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "source", kind: "enum", T: proto3.getEnumType(TrackSource) },
    { no: 3, name: "preset", kind: "enum", T: proto3.getEnumType(IngressVideoEncodingPreset), oneof: "encoding_options" },
    { no: 4, name: "options", kind: "message", T: IngressVideoEncodingOptions, oneof: "encoding_options" },
  ],
);

/**
 * @generated from message livekit.IngressAudioEncodingOptions
 */
export const IngressAudioEncodingOptions = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.IngressAudioEncodingOptions",
  () => [
    { no: 1, name: "audio_codec", kind: "enum", T: proto3.getEnumType(AudioCodec) },
    { no: 2, name: "bitrate", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 3, name: "disable_dtx", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 4, name: "channels", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
  ],
);

/**
 * @generated from message livekit.IngressVideoEncodingOptions
 */
export const IngressVideoEncodingOptions = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.IngressVideoEncodingOptions",
  () => [
    { no: 1, name: "video_codec", kind: "enum", T: proto3.getEnumType(VideoCodec) },
    { no: 2, name: "frame_rate", kind: "scalar", T: 1 /* ScalarType.DOUBLE */ },
    { no: 3, name: "layers", kind: "message", T: VideoLayer, repeated: true },
  ],
);

/**
 * @generated from message livekit.IngressInfo
 */
export const IngressInfo = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.IngressInfo",
  () => [
    { no: 1, name: "ingress_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "stream_key", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "url", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 5, name: "input_type", kind: "enum", T: proto3.getEnumType(IngressInput) },
    { no: 13, name: "bypass_transcoding", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 15, name: "enable_transcoding", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 6, name: "audio", kind: "message", T: IngressAudioOptions },
    { no: 7, name: "video", kind: "message", T: IngressVideoOptions },
    { no: 8, name: "room_name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 9, name: "participant_identity", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 10, name: "participant_name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 14, name: "participant_metadata", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 11, name: "reusable", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 12, name: "state", kind: "message", T: IngressState },
    { no: 16, name: "enabled", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
  ],
);

/**
 * @generated from message livekit.IngressState
 */
export const IngressState = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.IngressState",
  () => [
    { no: 1, name: "status", kind: "enum", T: proto3.getEnumType(IngressState_Status) },
    { no: 2, name: "error", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "video", kind: "message", T: InputVideoState },
    { no: 4, name: "audio", kind: "message", T: InputAudioState },
    { no: 5, name: "room_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 7, name: "started_at", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 8, name: "ended_at", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 10, name: "updated_at", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 9, name: "resource_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 6, name: "tracks", kind: "message", T: TrackInfo, repeated: true },
  ],
);

/**
 * @generated from enum livekit.IngressState.Status
 */
export const IngressState_Status = /*@__PURE__*/ proto3.makeEnum(
  "livekit.IngressState.Status",
  [
    {no: 0, name: "ENDPOINT_INACTIVE"},
    {no: 1, name: "ENDPOINT_BUFFERING"},
    {no: 2, name: "ENDPOINT_PUBLISHING"},
    {no: 3, name: "ENDPOINT_ERROR"},
    {no: 4, name: "ENDPOINT_COMPLETE"},
  ],
);

/**
 * @generated from message livekit.InputVideoState
 */
export const InputVideoState = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.InputVideoState",
  () => [
    { no: 1, name: "mime_type", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "average_bitrate", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 3, name: "width", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 4, name: "height", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 5, name: "framerate", kind: "scalar", T: 1 /* ScalarType.DOUBLE */ },
  ],
);

/**
 * @generated from message livekit.InputAudioState
 */
export const InputAudioState = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.InputAudioState",
  () => [
    { no: 1, name: "mime_type", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "average_bitrate", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 3, name: "channels", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 4, name: "sample_rate", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
  ],
);

/**
 * @generated from message livekit.UpdateIngressRequest
 */
export const UpdateIngressRequest = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.UpdateIngressRequest",
  () => [
    { no: 1, name: "ingress_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "room_name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "participant_identity", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 5, name: "participant_name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 9, name: "participant_metadata", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 8, name: "bypass_transcoding", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 10, name: "enable_transcoding", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 6, name: "audio", kind: "message", T: IngressAudioOptions },
    { no: 7, name: "video", kind: "message", T: IngressVideoOptions },
    { no: 11, name: "enabled", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
  ],
);

/**
 * @generated from message livekit.ListIngressRequest
 */
export const ListIngressRequest = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.ListIngressRequest",
  () => [
    { no: 1, name: "room_name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "ingress_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ],
);

/**
 * @generated from message livekit.ListIngressResponse
 */
export const ListIngressResponse = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.ListIngressResponse",
  () => [
    { no: 1, name: "items", kind: "message", T: IngressInfo, repeated: true },
  ],
);

/**
 * @generated from message livekit.DeleteIngressRequest
 */
export const DeleteIngressRequest = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.DeleteIngressRequest",
  () => [
    { no: 1, name: "ingress_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ],
);

