// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//   http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// @generated by protoc-gen-es v1.10.0 with parameter "target=dts+js"
// @generated from file livekit_agent_dispatch.proto (package livekit, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { proto3 } from "@bufbuild/protobuf";
import { Job } from "./livekit_agent_pb.js";

/**
 * @generated from message livekit.CreateAgentDispatchRequest
 */
export const CreateAgentDispatchRequest = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.CreateAgentDispatchRequest",
  () => [
    { no: 1, name: "agent_name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "room", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "metadata", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ],
);

/**
 * @generated from message livekit.RoomAgentDispatch
 */
export const RoomAgentDispatch = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.RoomAgentDispatch",
  () => [
    { no: 1, name: "agent_name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "metadata", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ],
);

/**
 * @generated from message livekit.DeleteAgentDispatchRequest
 */
export const DeleteAgentDispatchRequest = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.DeleteAgentDispatchRequest",
  () => [
    { no: 1, name: "dispatch_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "room", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ],
);

/**
 * @generated from message livekit.ListAgentDispatchRequest
 */
export const ListAgentDispatchRequest = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.ListAgentDispatchRequest",
  () => [
    { no: 1, name: "dispatch_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "room", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ],
);

/**
 * @generated from message livekit.ListAgentDispatchResponse
 */
export const ListAgentDispatchResponse = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.ListAgentDispatchResponse",
  () => [
    { no: 1, name: "agent_dispatches", kind: "message", T: AgentDispatch, repeated: true },
  ],
);

/**
 * @generated from message livekit.AgentDispatch
 */
export const AgentDispatch = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.AgentDispatch",
  () => [
    { no: 1, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "agent_name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "room", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "metadata", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 5, name: "state", kind: "message", T: AgentDispatchState },
  ],
);

/**
 * @generated from message livekit.AgentDispatchState
 */
export const AgentDispatchState = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.AgentDispatchState",
  () => [
    { no: 1, name: "jobs", kind: "message", T: Job, repeated: true },
    { no: 2, name: "created_at", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 3, name: "deleted_at", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
  ],
);

