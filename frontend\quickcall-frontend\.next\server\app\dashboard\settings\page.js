(()=>{var e={};e.id=631,e.ids=[631],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19167:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>o});var s=t(65239),a=t(48088),i=t(88170),n=t.n(i),d=t(30893),l={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>d[e]);t.d(r,l);let o={children:["",{children:["dashboard",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,62623)),"E:\\quickcall\\frontend\\quickcall-frontend\\src\\app\\dashboard\\settings\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,63144)),"E:\\quickcall\\frontend\\quickcall-frontend\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"E:\\quickcall\\frontend\\quickcall-frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["E:\\quickcall\\frontend\\quickcall-frontend\\src\\app\\dashboard\\settings\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/settings/page",pathname:"/dashboard/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},22221:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i});var s=t(60687),a=t(43210);function i(){let[e,r]=(0,a.useState)([{id:"call-recording",name:"Call Recording",description:"Automatically record all outbound calls",type:"toggle",value:!0},{id:"transcription",name:"Call Transcription",description:"Generate text transcripts for all calls",type:"toggle",value:!0},{id:"call-timeout",name:"Call Timeout",description:"Maximum duration for outbound calls",type:"select",value:"10",options:[{value:"5",label:"5 minutes"},{value:"10",label:"10 minutes"},{value:"15",label:"15 minutes"},{value:"30",label:"30 minutes"},{value:"60",label:"60 minutes"}]},{id:"api-key",name:"API Key",description:"Your API key for external integrations",type:"input",value:"sk-••••••••••••••••••••••••"}]),[t,i]=(0,a.useState)(!1),[n,d]=(0,a.useState)({type:"",text:""}),l=e=>{r(r=>r.map(r=>r.id===e?{...r,value:!r.value}:r))},o=(e,t)=>{r(r=>r.map(r=>r.id===e?{...r,value:t}:r))},c=(e,t)=>{r(r=>r.map(r=>r.id===e?{...r,value:t}:r))},u=async()=>{i(!0);try{setTimeout(()=>{d({type:"success",text:"Settings saved successfully"}),i(!1),setTimeout(()=>{d({type:"",text:""})},3e3)},500)}catch(e){console.error("Error saving settings:",e),d({type:"error",text:"Failed to save settings"}),i(!1)}};return(0,s.jsxs)("div",{className:"p-6 max-w-7xl mx-auto",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-semibold",children:"Settings"}),(0,s.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"Configure your application preferences"})]}),(0,s.jsx)("div",{children:(0,s.jsx)("button",{onClick:u,disabled:t,className:"bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md font-medium transition-colors flex items-center gap-2 disabled:opacity-50",children:t?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("svg",{className:"animate-spin h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Saving..."]}):(0,s.jsx)(s.Fragment,{children:"Save Changes"})})})]}),n.text&&(0,s.jsx)("div",{className:`p-4 mb-6 rounded-md ${"success"===n.type?"bg-green-50 dark:bg-green-900/20 text-green-800 dark:text-green-200":"error"===n.type?"bg-red-50 dark:bg-red-900/20 text-red-800 dark:text-red-200":""}`,children:n.text}),(0,s.jsxs)("div",{className:"bg-white dark:bg-[#1A1A1A] rounded-lg border border-gray-200 dark:border-gray-800 overflow-hidden",children:[(0,s.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-800 px-6 py-4",children:(0,s.jsx)("h2",{className:"font-medium",children:"General Settings"})}),(0,s.jsx)("div",{className:"divide-y divide-gray-200 dark:divide-gray-800",children:e.map(e=>(0,s.jsxs)("div",{className:"px-6 py-4 flex flex-col md:flex-row md:items-center md:justify-between",children:[(0,s.jsxs)("div",{className:"mb-4 md:mb-0 md:pr-4",children:[(0,s.jsx)("h3",{className:"text-sm font-medium",children:e.name}),(0,s.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:e.description})]}),(0,s.jsxs)("div",{className:"flex items-center",children:["toggle"===e.type&&(0,s.jsxs)("button",{onClick:()=>l(e.id),className:`relative inline-flex h-6 w-11 items-center rounded-full ${e.value?"bg-indigo-600":"bg-gray-200 dark:bg-gray-700"}`,children:[(0,s.jsxs)("span",{className:"sr-only",children:["Toggle ",e.name]}),(0,s.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition ${e.value?"translate-x-6":"translate-x-1"}`})]}),"select"===e.type&&e.options&&(0,s.jsx)("select",{value:e.value,onChange:r=>o(e.id,r.target.value),className:"block w-full md:w-auto p-2 text-sm text-gray-900 border border-gray-300 rounded-md bg-white dark:bg-[#1A1A1A] dark:border-gray-700 dark:text-white focus:ring-indigo-500 focus:border-indigo-500",children:e.options.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))}),"input"===e.type&&(0,s.jsx)("input",{type:"text",value:e.value,onChange:r=>c(e.id,r.target.value),className:"block w-full md:w-64 p-2 text-sm text-gray-900 border border-gray-300 rounded-md bg-white dark:bg-[#1A1A1A] dark:border-gray-700 dark:text-white focus:ring-indigo-500 focus:border-indigo-500"})]})]},e.id))})]}),(0,s.jsxs)("div",{className:"bg-white dark:bg-[#1A1A1A] rounded-lg border border-gray-200 dark:border-gray-800 overflow-hidden mt-8",children:[(0,s.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-800 px-6 py-4",children:(0,s.jsx)("h2",{className:"font-medium",children:"Integrations"})}),(0,s.jsx)("div",{className:"p-6",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{className:"p-4 border border-gray-200 dark:border-gray-700 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"w-10 h-10 bg-indigo-100 dark:bg-indigo-900/30 rounded-md flex items-center justify-center text-indigo-600 dark:text-indigo-400 mr-3",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-medium",children:"LiveKit"}),(0,s.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Voice calling integration"})]})]}),(0,s.jsxs)("div",{className:"relative inline-flex h-6 w-11 items-center rounded-full bg-indigo-600",children:[(0,s.jsx)("span",{className:"sr-only",children:"Enable LiveKit"}),(0,s.jsx)("span",{className:"inline-block h-4 w-4 transform rounded-full bg-white transition translate-x-6"})]})]}),(0,s.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-300",children:"Connected and working properly"})]}),(0,s.jsxs)("div",{className:"p-4 border border-gray-200 dark:border-gray-700 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"w-10 h-10 bg-indigo-100 dark:bg-indigo-900/30 rounded-md flex items-center justify-center text-indigo-600 dark:text-indigo-400 mr-3",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-medium",children:"Groq"}),(0,s.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"LLM provider"})]})]}),(0,s.jsxs)("div",{className:"relative inline-flex h-6 w-11 items-center rounded-full bg-indigo-600",children:[(0,s.jsx)("span",{className:"sr-only",children:"Enable Groq"}),(0,s.jsx)("span",{className:"inline-block h-4 w-4 transform rounded-full bg-white transition translate-x-6"})]})]}),(0,s.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-300",children:"Connected and working properly"})]})]})})]})]})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},62623:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\quickcall\\frontend\\quickcall-frontend\\src\\app\\dashboard\\settings\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64421:(e,r,t)=>{Promise.resolve().then(t.bind(t,62623))},77573:(e,r,t)=>{Promise.resolve().then(t.bind(t,22221))},79551:e=>{"use strict";e.exports=require("url")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,771,658,418,310],()=>t(19167));module.exports=s})();