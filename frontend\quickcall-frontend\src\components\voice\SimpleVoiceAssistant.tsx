"use client";

import { useEffect } from "react";
import { BarVisualizer, useVoiceAssistant, AgentState } from "@livekit/components-react";

interface SimpleVoiceAssistantProps {
  onStateChange: (state: AgentState) => void;
}

/**
 * Simple voice assistant component with visualization
 */
const SimpleVoiceAssistant: React.FC<SimpleVoiceAssistantProps> = ({
  onStateChange,
}) => {
  const { state, audioTrack } = useVoiceAssistant();
  
  useEffect(() => {
    onStateChange(state);
  }, [state, onStateChange]);
  
  return (
    <div className="h-[300px] max-w-[90vw] mx-auto flex flex-col items-center justify-center">
      <div className="flex-1 flex items-center justify-center w-full">
        <BarVisualizer
          state={state}
          barCount={5}
          trackRef={audioTrack}
          className="agent-visualizer w-full h-full"
          options={{ minHeight: 24 }}
        />
      </div>
      <div className="mt-4 text-center">
        <p className="text-sm font-medium text-foreground">
          Status: <span className="capitalize">{state}</span>
        </p>
        {state === "listening" && (
          <p className="text-xs mt-1 text-muted-foreground">
            Listening for your voice...
          </p>
        )}
        {state === "thinking" && (
          <p className="text-xs mt-1 text-muted-foreground">
            Processing your request...
          </p>
        )}
        {state === "speaking" && (
          <p className="text-xs mt-1 text-muted-foreground">
            Assistant is speaking...
          </p>
        )}
      </div>
    </div>
  );
};

export default SimpleVoiceAssistant;
